import { getAuth } from '@clerk/express'
import { connectToPG, DBFunctions } from '@upbound/postgres'
import { RedisClient } from '@upbound/redis'
import { DBCustomer, DBQueueWorkflow, DBWorkflow, WorkflowLLM } from '@upbound/shared'
import { AgentLLM } from '@upbound/shared/agents'

import type { Request } from 'express'
import { OpenAI } from 'openai'
import { getAppEnv } from './env.js'
import { WorkflowColumnService } from './services/WorkflowColumnService.js'

// Initialize singleton services
const env = getAppEnv()

const [postgres, redisClient] = await Promise.all([
  connectToPG({ connectionString: env.POSTGRES_URL, maxPoolSize: 40 }, 'api'),
  RedisClient.create(env.REDIS_URL, { keyPrefix: 'upbound:' }),
])

export { postgres, redisClient }

// Database classes
const dbCustomer = new DBCustomer(postgres)
const dbFunctions = new DBFunctions(postgres)
const dbWorkflow = new DBWorkflow(postgres)
const dbQueueWorkflow = new DBQueueWorkflow(postgres)

// OpenAI
const openAI = new OpenAI({ apiKey: env.OPENAI_API_KEY })
const workflowLLM = new WorkflowLLM(openAI)
const agentLLM = new AgentLLM(
  {
    openAIApiKey: env.OPENAI_API_KEY,
  },
  redisClient
)

const workflowColumnService = new WorkflowColumnService(dbWorkflow, postgres, workflowLLM, openAI)

export const DEFAULT_CONTEXT = {
  postgres,
  redis: redisClient,
  integrations: {
    openai: openAI,
  },
  db: {
    dbCustomer,
    dbFunctions,
    dbWorkflow,
    dbQueueWorkflow,
  },

  workflowLLM,
  agentLLM,
  workflowColumnService,
} as const

export function createContext({
  req,
}: {
  req: Request
}): { req: typeof req; auth: ReturnType<typeof getAuth> } & typeof DEFAULT_CONTEXT {
  return {
    req,
    auth: getAuth(req),
    ...DEFAULT_CONTEXT,
  }
}

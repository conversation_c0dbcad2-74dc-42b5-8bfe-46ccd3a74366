import { clerkClient } from '@clerk/express'
import { isNotNullOrUndefined } from '@upbound/utils'
import z from 'zod'
import { orgAuthedProcedure, router } from '../trpc.js'
import { UserId } from '../utils/schemas.js'

type InvitationStatus = 'pending' | 'accepted' | 'revoked'

/**
 * This is a temporary implementation of the backend API for users.
 * We need to set-up webhooks to receive events emitted by clerk
 * and have a local user(s) table to base this off of.
 *
 * These API(s) are technically rate-limited.
 */
export const UserRouter = router({
  getAllUsers: orgAuthedProcedure.query(async ({ ctx: { auth } }) => {
    const memberships = await clerkClient.organizations.getOrganizationMembershipList({
      organizationId: auth.orgId,
      offset: 0,
      limit: 100,
    })

    return memberships.data.map(membership => membership.publicUserData).filter(isNotNullOrUndefined)
  }),

  getUsers: orgAuthedProcedure
    .input(
      z.object({
        userIds: UserId.array()
          .min(1)
          .transform(x => new Set(x)),
      })
    )
    .query(({ input }) => {
      return clerkClient.users
        .getUserList({ offset: 0, limit: 100 })
        .then(users => users.data.filter(user => input.userIds.has(user.id)))
    }),

  getInvitations: orgAuthedProcedure.query(async () => {
    return clerkClient.invitations
      .getInvitationList({ offset: 0, limit: 100 })
      .then(x => x.data.map(y => ({ email: y.emailAddress, status: y.status as InvitationStatus, invitationId: y.id })))
  }),

  revokeInvitation: orgAuthedProcedure.input(z.object({ invitationId: z.string() })).mutation(async ({ input }) => {
    return clerkClient.invitations.revokeInvitation(input.invitationId)
  }),
})

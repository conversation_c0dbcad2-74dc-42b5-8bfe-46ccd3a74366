import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server'
import { router } from '../trpc.js'
import { AgentRouter } from './AgentRouter.js'
import { CustomerRouter } from './CustomerRouter.js'
import { MetaRouter } from './MetaRouter.js'
import { OnboardingRouter } from './OnboardingRouter.js'
import { UserRouter } from './UserRouter.js'
import { WorkflowRouter } from './WorkflowRouter.js'

export const AppRouter = router({
  user: UserRouter,
  meta: MetaRouter,
  customer: CustomerRouter,
  onboarding: OnboardingRouter,
  workflow: WorkflowRouter,
  agent: AgentRouter,
})

// Export router type signature,
export type AppRouter = typeof AppRouter
export type AppRouterOutput = inferRouterOutputs<AppRouter>
export type AppRouterInput = inferRouterInputs<AppRouter>

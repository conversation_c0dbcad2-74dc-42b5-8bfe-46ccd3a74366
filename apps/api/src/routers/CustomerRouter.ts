import { clerkClient } from '@clerk/express'
import { TRPCError } from '@trpc/server'
import z from 'zod'
import { authedProcedure, orgAuthedProcedure, router } from '../trpc.js'

export const CustomerRouter = router({
  create: authedProcedure
    .input(
      z.object({
        companyName: z.string(),
        companyDomain: z.string(),
        startingData: z.object({ companyName: z.string(), companyDomain: z.string() }).array(),
      })
    )
    .mutation(
      async ({
        input: { companyName, companyDomain, startingData },
        ctx: {
          auth,
          db: { dbCustomer, dbWorkflow },
        },
      }) => {
        // Prevent users from creating more than 1 organization via the API
        const { data: userOrganizations } = await clerkClient.users.getOrganizationMembershipList({
          userId: auth.userId,
        })

        if (userOrganizations.length > 0) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'User is not allowed to be part of more than 1 organization.',
          })
        }

        // remove any domains from companyName
        const cleanedCompanyName = companyName
          .split(' ')
          .filter(word => !word.includes('.') && !word.startsWith('http'))
          .join(' ')
          .trim()

        const nextCleanedCompanyName = cleanedCompanyName.length > 0 ? cleanedCompanyName : companyName.split('.')[0]
        const finalCompanyName =
          nextCleanedCompanyName && nextCleanedCompanyName.length > 0 ? nextCleanedCompanyName : 'External'

        const org = await clerkClient.organizations.createOrganization({
          // Remove domains and urls from companyName
          name: finalCompanyName,
          createdBy: auth.userId,
        })

        await dbCustomer.createCustomer({ companyName, companyDomain, orgId: org.id, orgSlug: org.slug ?? '' })

        const onboardingTable = await dbWorkflow.createOnboardingTable({
          customerId: org.id,
          userId: auth.userId,
        })

        return { orgId: org.id, onboardingTable }
      }
    ),

  // Get or insert organization into our records.
  get: orgAuthedProcedure.query(async ({ ctx }) => {
    return { organization: await ctx.db.dbCustomer.getOrUpsert(ctx.auth.orgId, ctx.auth.orgSlug) }
  }),
})

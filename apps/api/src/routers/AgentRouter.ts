import { TRPCError } from '@trpc/server'
import { AgentDataType } from '@upbound/shared/agents'
import { type ColumnDefinition, columnDefinitionsToExistingColumnValuesForAgents } from '@upbound/shared/workflow'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { orgAuthedProcedure, router } from '../trpc.js'
import { IdInt } from '../utils/schemas.js'

export const AgentRouter = router({
  evalAiAgentCell: orgAuthedProcedure
    .input(
      z.object({
        task: z.string(),
        existingColumns: z
          .object({
            columnName: z.string(),
            dataType: AgentDataType,
          })
          .array(),
        desiredOutputDataType: z.string().optional(),
      })
    )
    .mutation(({ ctx: { auth, agentLLM }, input }) => {
      return agentLLM.computeAgentColumnDefinition({
        ...input,
        customerId: auth.orgId,
        desiredOutputDataType: input.desiredOutputDataType as AgentDataType['type'],
      })
    }),

  addAgentColumns: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
        tasks: z.string().array(),
      })
    )
    .mutation(
      async ({
        ctx: {
          auth,
          agentLLM,
          db: { dbWorkflow },
        },
        input,
      }) => {
        const workflow = await dbWorkflow.getWorkflowTable({
          customerId: auth.orgId,
          workflowTableId: input.workflowTableId,
        })

        if (!workflow) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Workflow table not found',
          })
        }

        const columnDefinitions = Object.values(workflow.tableDefinition.columnDefinitions ?? [])
        const computedColumnDefinitions: ColumnDefinition[] = []

        for (const task of input.tasks) {
          const existingColumns = columnDefinitionsToExistingColumnValuesForAgents([
            ...columnDefinitions,
            ...computedColumnDefinitions,
          ])

          const computed = await agentLLM.computeAgentColumnDefinition({
            customerId: auth.orgId,
            task,
            existingColumns,
          })

          computedColumnDefinitions.push({
            type: 'agent' as const,
            columnVariant: 'computed',
            columnId: uuidv7(),
            columnName: computed.columnName,
            config: {
              task,
              agentTask: computed.instructions,
              dataType: computed.columnType,
            },
            columnDigest: '',
            inputColumns: [],
          })
        }

        const newColumnDefinitions = Object.fromEntries(
          computedColumnDefinitions.map(x => [x.columnId, x] as const)
        ) satisfies Record<string, ColumnDefinition>

        const updatedTable = await dbWorkflow.upsertColumns({
          customerId: auth.orgId,
          workflowTableId: input.workflowTableId,
          columnDefinitions: newColumnDefinitions,
        })

        return {
          workflowTable: updatedTable,
        }
      }
    ),
})

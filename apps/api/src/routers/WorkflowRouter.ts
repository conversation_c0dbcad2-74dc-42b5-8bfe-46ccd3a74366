import { TRPCError } from '@trpc/server'
import { type ColumnDefinitionOf, columnDefinitionsToExistingColumnValuesForAgents } from '@upbound/shared/workflow'
import { iife } from '@upbound/utils'
import z from 'zod'
import { DerivedColumnDefinition, UpsertColumnSchema } from '../services/WorkflowColumnService.js'
import { internalAuthedProcedure, orgAuthedProcedure, router } from '../trpc.js'
import { IdInt } from '../utils/schemas.js'

export const RowId = z.string().uuid()
export const ColumnId = z.string().uuid()

export const WorkflowRouter = router({
  addOnboardingTable: internalAuthedProcedure.mutation(
    async ({
      ctx: {
        db: { dbWorkflow },
        auth,
      },
    }) => {
      return dbWorkflow.createOnboardingTable({ customerId: auth.orgId, userId: auth.userId })
    }
  ),

  resolveOnboardingTable: orgAuthedProcedure.query(
    async ({
      ctx: {
        db: { dbWorkflow },
        auth,
      },
    }) => dbWorkflow.getOnboardingTableId({ customerId: auth.orgId }).then(x => ({ workflowTableId: x }))
  ),

  addWorkflowTable: orgAuthedProcedure
    .input(
      z.object({
        tableName: z.string(),
        columnDefinitions: UpsertColumnSchema.array().default([]),
        rows: z
          .object({ rowId: RowId, rowData: z.record(z.unknown()).optional() })
          .array()
          .optional(),
      })
    )
    .mutation(
      async ({
        ctx: {
          db: { dbWorkflow },
          workflowColumnService,
          auth,
          postgres,
        },
        input,
      }) => {
        return postgres
          .begin(async sql => {
            const workflowTable = await dbWorkflow.createWorkflowTable(
              {
                name: input.tableName,
                tableDefinition: {
                  columnDefinitions: {},
                },
                customerId: auth.orgId,
                userId: auth.userId,
              },
              sql
            )

            if (input.rows) {
              await dbWorkflow.addRows(
                {
                  customerId: auth.orgId,
                  workflowTableId: workflowTable.table.workflowTableId,
                  rows: input.rows,
                },
                sql
              )
            }

            return { workflowTable }
          })
          .then(async ({ workflowTable }) => {
            for (const col of input.columnDefinitions) {
              await workflowColumnService.addColumn({
                auth,
                input: {
                  columnDefinition: col,
                  workflowTableId: workflowTable.table.workflowTableId,
                  upsert: false,
                },
              })
            }

            return { workflowTable }
          })
      }
    ),

  updateWorkflowTable: orgAuthedProcedure.input(z.object({ tableName: z.string(), workflowTableId: IdInt })).mutation(
    async ({
      ctx: {
        db: { dbWorkflow },
        auth,
      },
      input,
    }) => {
      const workflowTable = await dbWorkflow.updateWorkflowTable({
        name: input.tableName,
        customerId: auth.orgId,
        workflowTableId: input.workflowTableId,
      })

      return { workflowTable }
    }
  ),

  trashWorkflowTables: orgAuthedProcedure.input(z.object({ workflowTableIds: IdInt.array() })).mutation(
    async ({
      ctx: {
        db: { dbWorkflow },
        auth,
      },
      input,
    }) => {
      const success = await dbWorkflow.trashWorkflowTables({
        workflowTableIds: input.workflowTableIds,
        customerId: auth.orgId,
      })
      return { success }
    }
  ),

  restoreWorkflowTables: orgAuthedProcedure.input(z.object({ workflowTableIds: IdInt.array() })).mutation(
    async ({
      ctx: {
        db: { dbWorkflow },
        auth,
      },
      input,
    }) => {
      const success = await dbWorkflow.restoreWorkflowTables({
        workflowTableIds: input.workflowTableIds,
        customerId: auth.orgId,
      })
      return { success }
    }
  ),

  addColumn: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
        columnDefinition: UpsertColumnSchema,
        upsert: z.boolean().default(false),
        derivedColumns: DerivedColumnDefinition.array().optional(),
      })
    )
    .mutation(async ({ input, ctx: { auth, workflowColumnService } }) => {
      const addedColumnResult = await workflowColumnService.addColumn({
        auth,
        input: {
          upsert: input.upsert,
          columnDefinition: input.columnDefinition,
          workflowTableId: input.workflowTableId,
        },
      })

      if (!input.upsert && input.derivedColumns) {
        await workflowColumnService.createDerivedColumns({
          auth,
          derivedColumns: input.derivedColumns,
          sourceColumnId: input.columnDefinition.columnId,
          workflowTableId: input.workflowTableId,
        })
      }

      return addedColumnResult
    }),

  removeColumn: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
        columnId: ColumnId,
      })
    )
    .mutation(
      async ({
        input,
        ctx: {
          auth,
          db: { dbWorkflow },
        },
      }) => {
        const result = await dbWorkflow.removeColumn({
          customerId: auth.orgId,
          workflowTableId: input.workflowTableId,
          columnId: input.columnId,
        })

        if (!result) {
          throw new TRPCError({ code: 'NOT_FOUND', message: 'Table not found' })
        }

        return {
          updatedTable: result.updatedTable,
          updatedRows: result.updatedRows,
        }
      }
    ),

  renameColumn: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
        columnId: ColumnId,
        newColumnName: z.string().min(1),
      })
    )
    .mutation(async ({ input, ctx: { auth, workflowColumnService } }) => {
      return workflowColumnService.renameColumn({ ...input, auth })
    }),

  listWorkflowTables: orgAuthedProcedure
    .input(
      z.object({ fuzzyName: z.string().optional(), filter: z.enum(['active', 'trash']).optional().default('active') })
    )
    .query(
      async ({
        input,
        ctx: {
          auth,
          db: { dbWorkflow },
        },
      }) => {
        const workflowTables = await dbWorkflow.listWorkflowTables({
          customerId: auth.orgId,
          filter: input.filter,
          fuzzyName: input.fuzzyName,
        })

        return {
          workflowTables,
        }
      }
    ),

  listPinnedTableIds: orgAuthedProcedure.query(
    async ({
      ctx: {
        auth,
        db: { dbWorkflow },
      },
    }) => {
      const pinnedTableIds = await dbWorkflow.listPinnedWorkflowTableIds({
        customerId: auth.orgId,
        userId: auth.userId,
      })

      return { pinnedTableIds }
    }
  ),

  setPinnedTable: orgAuthedProcedure.input(z.object({ workflowTableId: IdInt, pinned: z.boolean() })).mutation(
    async ({
      input,
      ctx: {
        auth,
        db: { dbWorkflow },
      },
    }) => {
      await dbWorkflow.setPinnedTable({
        customerId: auth.orgId,
        userId: auth.userId,
        workflowTableId: input.workflowTableId,
        pinned: input.pinned,
      })
    }
  ),

  getWorkflowTable: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
      })
    )
    .query(
      async ({
        input,
        ctx: {
          auth,
          db: { dbWorkflow },
        },
      }) => {
        const workflowTable = await dbWorkflow.getWorkflowTable({
          customerId: auth.orgId,
          workflowTableId: input.workflowTableId,
        })

        return {
          workflowTable,
        }
      }
    ),

  run: orgAuthedProcedure
    .input(
      z.object({
        workflowTableId: IdInt,
        rowIds: RowId.array(),
        orderedColumnIds: ColumnId.array(),
      })
    )
    .mutation(
      async ({
        input,
        ctx: {
          auth,
          db: { dbWorkflow, dbQueueWorkflow },
          agentLLM,
          postgres,
        },
      }) => {
        if (input.rowIds.length === 0 || input.orderedColumnIds.length === 0) {
          return { state: 'Skipped' as const }
        }

        let workflowTable = await dbWorkflow.getWorkflowTable({
          customerId: auth.orgId,
          workflowTableId: input.workflowTableId,
        })

        if (!workflowTable) {
          return { state: 'Skipped' as const }
        }

        let columnDefUpdated = false

        for (const columnId of input.orderedColumnIds) {
          const colDef = workflowTable!.tableDefinition.columnDefinitions[columnId]

          if (colDef && colDef.type === 'agent' && colDef.config.dataType.type === 'unconfigured') {
            const updatedColDef = await agentLLM.computeAgentColumnDefinition({
              customerId: auth.orgId,
              task: colDef.columnName,
              existingColumns: columnDefinitionsToExistingColumnValuesForAgents(
                Object.values(workflowTable!.tableDefinition.columnDefinitions)
                  .filter(x => !(x.type === 'agent' && x.config.dataType.type === 'unconfigured'))
                  .filter(x => x.columnId !== columnId)
              ),
            })

            columnDefUpdated = true

            workflowTable = await dbWorkflow.upsertColumn({
              customerId: auth.orgId,
              workflowTableId: input.workflowTableId,
              columnDefinition: {
                type: 'agent',
                columnId,
                columnVariant: 'computed',
                columnName: updatedColDef.columnName,
                config: {
                  dataType: updatedColDef.columnType,
                  task: updatedColDef.instructions,
                  agentTask: updatedColDef.instructions,
                },
                columnDigest: '',
                inputColumns: [],
              } satisfies ColumnDefinitionOf<'agent'>,
            })
          }
        }

        return postgres.begin(async sql => {
          // If columnId is provided, update the column to 'queued' status
          // on all the provided row ids before queueing the row.
          const updatedRowIds = await iife(async () => {
            const payload = Object.fromEntries(
              input.orderedColumnIds.map(
                columnId =>
                  [
                    columnId,
                    {
                      status: 'queued',
                    },
                  ] as const
              )
            )

            const updatedRows = await dbWorkflow.updateRows(
              {
                customerId: auth.orgId,
                workflowTableId: input.workflowTableId,
                rows: input.rowIds.map(rowId => ({
                  rowId,
                  ...payload,
                })),
              },
              sql
            )

            return updatedRows.map(r => r.rowId)
          })

          if (updatedRowIds.length === 0) {
            return { state: 'Skipped' as const }
          }

          const queuePayload = input.rowIds.flatMap(rowId =>
            input.orderedColumnIds.map(columnId => {
              return {
                customerId: auth.orgId,
                workflowTableId: workflowTable!.workflowTableId,
                deduplicationId: `${rowId}-eval:row`,
                topic: 'eval:row' as const,
                delay: '1 second' as const,
                payload: {
                  rowId,
                  columnId,
                },
              }
            })
          )

          await dbQueueWorkflow.addToQueueWorkflow(queuePayload, sql)

          return {
            state: 'Queued' as const,
            columnDefUpdated,
          }
        })
      }
    ),
})

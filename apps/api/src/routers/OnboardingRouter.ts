import { zodResponseFormat } from 'openai/helpers/zod.mjs'
import { z } from 'zod'
import { authedProcedure, router } from '../trpc.js'

export const OnboardingRouter = router({
  lookalikeCompanies: authedProcedure
    .input(
      z.object({
        companyWebsite: z.string(),
      })
    )
    .mutation(
      async ({
        input,
        ctx: {
          integrations: { openai },
        },
      }) => {
        const size = 5

        const responseFormat = z.object({
          companyName: z.string().describe('The name of the provided company'),
          primaryIndustry: z.string().describe('The primary industry of the provided company'),
          similarCompanies: z
            .object({
              companyName: z.string().describe('The name of the company'),
              companyDomain: z.string().describe('The domain of the company'),
              reasoning: z.string().describe('The reasoning behind the company being similar to the provided company'),
            })
            .array()
            .describe(`A list of up-to ${size} company names and domains that are similar to the provided company.`),
        })

        const response = await openai.chat.completions.create({
          messages: [
            {
              role: 'system' as const,
              content: `
You are a company research analyst.
The user will provide you with a company website. Your task is to find and return a list of similar companies and their websites.

First, identify the primary industry of the provided company.
Then, find up-to ${size} other companies that are in the same industry and are currently operating.
Then, return a list of these companies and their websites in JSON format.
            `.trim(),
            },
            {
              role: 'user' as const,
              content: `<company_website>${input.companyWebsite}</company_website>`,
            },
          ],
          response_format: zodResponseFormat(responseFormat, 'Companies'),
          model: 'gpt-4o',
        })

        const result = JSON.parse(response.choices[0]?.message.content ?? '') as unknown as z.output<
          typeof responseFormat
        >

        return result
      }
    ),
})

import v8 from 'node:v8'
import z from 'zod'

export const AppEnv = z.object({
  NODE_ENV: z.enum(['production', 'development', 'test']).default('development'),
  APP_HOST_URL: z.string(),
  CLERK_SECRET_KEY: z.string(),
  OPENAI_API_KEY: z.string(),
  API_PORT: z
    .string()
    .transform(x => Number.parseInt(x, 10))
    .default('4000'),

  REDIS_URL: z.string().startsWith('redis').default('redis://localhost:6379'),
  POSTGRES_URL: z.string().startsWith('postgres').default('postgres://upbound:upbound@localhost:5432/superday'),
})

export type AppEnv = z.infer<typeof AppEnv>

export function getAppEnv(processEnv: unknown = process.env): AppEnv {
  const env = AppEnv.parse(processEnv)

  console.info('Config', {
    name: 'api',
    maxMemory: `${v8.getHeapStatistics().total_available_size / (1024 * 1024)} MB`,
    nodeEnv: env.NODE_ENV,
    port: env.API_PORT,
    host: env.APP_HOST_URL,
  })

  return env
}

import type { SignedInAuthObject } from '@clerk/backend/internal'
import { clerkClient, type SessionAuthObject } from '@clerk/express'
import { initTRPC, TRPCError } from '@trpc/server'
import type { DBFunctions, SqlClient } from '@upbound/postgres'
import type { RedisClient } from '@upbound/redis'
import type { DBCustomer, DBQueueWorkflow, DBWorkflow, WorkflowLLM } from '@upbound/shared'
import type { AgentLLM } from '@upbound/shared/agents'
import type { OpenAI } from 'openai'
import superjson from 'superjson'
import { ZodError } from 'zod'
import type { WorkflowColumnService } from './services/WorkflowColumnService.js'
import type { RemoveNullable } from './utils/TypeUtils.js'

export interface Context {
  postgres: SqlClient
  redis: RedisClient
  db: {
    dbCustomer: DBCustomer
    dbFunctions: DBFunctions
    dbWorkflow: DBWorkflow
    dbQueueWorkflow: DBQueueWorkflow
  }
  integrations: {
    openai: OpenAI
  }
  workflowLLM: WorkflowLLM
  agentLLM: AgentLLM
  workflowColumnService: WorkflowColumnService
  req: {
    hostname: string
    protocol: string
  }
  auth: SessionAuthObject
}

/**
 * Initialization of tRPC backend
 * Should be done only once per backend!
 */
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter(opts) {
    const { shape, error } = opts

    if (error.cause?.name === 'PostgresError') {
      console.error(error.cause)

      return {
        message: 'Internal server error',
        code: -32_603,
      }
    }

    return {
      ...shape,
      data: {
        ...shape.data,
        zodMessage: error.cause instanceof ZodError ? error.cause?.issues.map(x => x.message).join(', ') : null,
        zodError: error.code === 'BAD_REQUEST' && error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }
  },
})

/**
 * Export reusable router and procedure helpers
 * that can be used throughout the router
 */
export const router = t.router

// Trace every request appropriately
export const procedure = t.procedure
export const middleware = t.middleware
export const mergeRouter = t.mergeRouters

const isAuthed = middleware(opts => {
  if (opts.ctx.auth?.userId) {
    const auth: SignedInAuthObject = opts.ctx.auth
    return opts.next({ ctx: { auth } })
  } else {
    throw new TRPCError({ code: 'UNAUTHORIZED' })
  }
})

export const authedProcedure = procedure.use(isAuthed)

export type OrgAuthedUser = RemoveNullable<SignedInAuthObject, 'orgId' | 'orgRole' | 'orgSlug'>

const isOrgAuthed = middleware(opts => {
  if (opts.ctx.auth.orgId) {
    return opts.next({
      ctx: {
        auth: {
          userId: opts.ctx.auth.userId,
          orgId: opts.ctx.auth.orgId,
          orgSlug: opts.ctx.auth.orgSlug,
          orgRole: opts.ctx.auth.orgRole as 'admin' | 'basic_member',
        },
        clerkAuth: opts.ctx.auth as OrgAuthedUser,
        // This flag indicates if the user is an admin user or not.
        // This allows us to potentially customize responses based on the user's role.
        // We only consider "impersonated" sessions to be an admin user.
        isFreckleAdmin: !!opts.ctx.auth.actor,
      },
    })
  } else {
    throw new TRPCError({ code: 'FORBIDDEN' })
  }
})

// Note: This procedure is being depended on by the `addHubSpotClient`.
export const orgAuthedProcedure = authedProcedure.use(isOrgAuthed)

// Admin procedure
export const orgAdminProcedure = orgAuthedProcedure.use(async ({ ctx, next }) => {
  if (ctx.auth.orgRole === 'admin') {
    return next({ ctx })
  } else {
    throw new TRPCError({ code: 'FORBIDDEN' })
  }
})

export const internalAuthedProcedure = orgAuthedProcedure.use(async opts => {
  const isFreckleEmail = () =>
    clerkClient.users
      .getUser(opts.ctx.auth.userId)
      .then(
        user =>
          !!user.emailAddresses.find(x => user.primaryEmailAddressId === x.id)?.emailAddress?.endsWith('freckle.io')
      )

  if (opts.ctx.isFreckleAdmin || (await isFreckleEmail())) {
    return opts.next({ ctx: { auth: opts.ctx.auth } })
  } else {
    throw new TRPCError({ code: 'FORBIDDEN' })
  }
})

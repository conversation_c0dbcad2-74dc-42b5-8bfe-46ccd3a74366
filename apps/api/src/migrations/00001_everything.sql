CREATE
OR R<PERSON>LACE FUNCTION auto_update_updated_at_column () RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

create table
  public.customer (
    clerk_id char(31) not null primary key constraint customer_clerk_id_check check (starts_with((clerk_id)::text, 'org_'::text)),
    created_at timestamp with time zone default now() not null,
    hubspot_timezone text,
    clerk_org_slug text,
    company_name text default ''::text not null,
    company_domain text default ''::text not null,
    salesforce jsonb,
    credits_available integer default 0 not null,
    stripe_customer_id char(18) constraint customer_stripe_customer_id_check check (
      starts_with((stripe_customer_id)::text, 'cus_'::text)
    )
  );

create table
  public.workflow_table (
    id integer generated always as identity primary key,
    customer_id char(31) not null references public.customer on delete cascade,
    user_id char(32) not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    name text not null constraint workflow_table_name_check check (
      (name <> ''::text)
      AND (char_length(name) <= 255)
    ),
    table_definition jsonb not null,
    num_rows integer default 0 not null,
    deleted_at timestamp with time zone
  );

create trigger update_workflow_table_updated_on before
update on public.workflow_table for each row
execute procedure public.auto_update_updated_at_column ();

create table
  public.workflow_table_row (
    row_id uuid not null,
    customer_id char(31) not null references public.customer on delete cascade,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    row_value jsonb not null,
    primary key (workflow_table_id, row_id)
  );

create trigger update_workflow_table_updated_on before
update on public.workflow_table_row for each row
execute procedure public.auto_update_updated_at_column ();

create table
  public.workflow_table_view (
    view_id integer generated always as identity,
    customer_id char(31) not null references public.customer on delete cascade,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    view_name text default 'default'::text not null constraint workflow_table_view_view_name_check check (
      (view_name <> ''::text)
      AND (char_length(view_name) <= 255)
    ),
    view_definition jsonb,
    primary key (workflow_table_id, view_id)
  );

create table
  public.q_workflow (
    id bigint generated always as identity primary key,
    created_at timestamp with time zone default now() not null,
    customer_id char(31) not null references public.customer on delete cascade,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    status text default 'pending'::text not null constraint q_workflow_status_check check (
      status = ANY (
        ARRAY[
          'pending'::text,
          'running'::text,
          'done'::text,
          'error'::text
        ]
      )
    ),
    process_attempt smallint default 0,
    topic text not null,
    payload json not null,
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    trace_context json,
    metadata json,
    delay interval,
    deduplication_id text
  );

create index idx_workflow_queue_running on public.q_workflow (id) include (customer_id, deduplication_id)
where
  (status = 'running'::text);

create index idx_workflow_queue_pending on public.q_workflow (id) include (customer_id, deduplication_id, process_attempt)
where
  (
    (status = 'pending'::text)
    AND (process_attempt < 4)
  );

create table
  public.workflow_table_active_webhooks (
    webhook_id uuid not null primary key,
    customer_id char(31) not null references public.customer on delete cascade,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    column_id uuid not null,
    webhook_secret text,
    is_disabled boolean default false not null
  );

create table
  public.workflow_table_inbound_webhook_queue (
    id bigint generated always as identity primary key,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    webhook_id uuid not null references public.workflow_table_active_webhooks on delete cascade,
    payload jsonb not null,
    idempotency_key text constraint workflow_table_inbound_webhook_queue_idempotency_key_check check (
      (idempotency_key IS NULL)
      OR (char_length(idempotency_key) <= 255)
    )
  );

create index workflow_table_inbound_webhook_q_webhook_id_idempotency_key_idx on public.workflow_table_inbound_webhook_queue (webhook_id, idempotency_key)
where
  (idempotency_key IS NOT NULL);

create table
  public.workflow_recurring_tasks (
    id integer generated always as identity primary key,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    customer_id char(31) not null references public.customer on delete cascade,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    is_disabled boolean default false not null,
    interval interval default '00:02:00'::interval not null,
    next_run timestamp with time zone default now() not null,
    locked_at timestamp with time zone,
    topic text not null,
    payload jsonb not null,
    metadata jsonb
  );

create trigger update_workflow_recurring_tasks_updated_at before
update on public.workflow_recurring_tasks for each row
execute procedure public.auto_update_updated_at_column ();

create table
  public.workflow_table_pin (
    customer_id char(31) not null references public.customer on delete cascade,
    user_id char(32) not null,
    workflow_table_id integer not null references public.workflow_table on delete cascade,
    pinned_at timestamp with time zone default now() not null,
    primary key (customer_id, user_id, workflow_table_id)
  );

create table
  public.users (
    user_id char(32) not null primary key,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    first_name text,
    last_name text,
    primary_email text,
    last_sign_in_at timestamp with time zone,
    profile_image_url text
  );

create trigger update_users_updated_at before
update on public.users for each row
execute procedure public.auto_update_updated_at_column ();

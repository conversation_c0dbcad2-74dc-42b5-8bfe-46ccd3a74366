Error.stackTraceLimit = 50

import http from 'node:http'
import { exit } from 'node:process'
import { clerkClient, clerkMiddleware, getAuth, type SessionAuthObject } from '@clerk/express'
import { createExpressMiddleware } from '@trpc/server/adapters/express'
import { applyWSSHandler } from '@trpc/server/adapters/ws'
import cors from 'cors'
import express from 'express'
import multer from 'multer'
import { WebSocketServer } from 'ws'
import { createContext, DEFAULT_CONTEXT, postgres, redisClient } from './createContext.js'
import { getAppEnv } from './env.js'
import { AppRouter } from './routers/AppRouter.js'
import { uploadCsv } from './uploadCsv.js'

const env = getAppEnv()

const upload = multer({ limits: { fileSize: 10 * 1024 ** 2 } })

// Base Express App
const app = express().disable('x-powered-by')

const serverApp = http.createServer(
  app
    // Cors Headers
    .use(cors())
    .use('/health', (_req, res) => res.json({ status: 'ok' }))
    .use(clerkMiddleware())
    // Return 401 if user is not authenticated
    .use((req, res, next) => {
      const auth = getAuth(req)

      if (auth.userId) {
        return next()
      } else {
        console.error(`AuthRequestError: ${req.url} | ${req.headers.origin} | ${req.headers.referer}`)
        return res.sendStatus(401)
      }
    })
    .use(
      '/rpc',
      createExpressMiddleware({
        router: AppRouter,
        // Inject services into the context
        createContext,
      })
    )
    .use('/upload-csv', upload.single('csv'), uploadCsv)
)

const wsServer = new WebSocketServer({ server: serverApp })
const server = serverApp.listen(env.API_PORT, () => console.info(`Listening on ${env.API_PORT}`))

// Start `wss` server on the same port
const wssHandler = applyWSSHandler<AppRouter>({
  // biome-ignore lint/suspicious/noExplicitAny: no reason given
  wss: wsServer as any,
  router: AppRouter,
  keepAlive: {
    enabled: true,
    pingMs: 30_000,
    pongWaitMs: 5000,
  },
  createContext: async opts => {
    const token = opts.info.connectionParams?.['token']

    // Turn IncomingMessage into a Request object
    const headers = { Authorization: `Bearer ${token}` }
    const protocol = 'https'
    const dummyOriginReqUrl = new URL(opts.req.url || '', `${protocol}://clerk-dummy`)

    const dummyRequest = new Request(dummyOriginReqUrl, {
      method: opts.req.method,
      headers: new Headers(headers),
    })

    // biome-ignore lint/suspicious/noExplicitAny: no reason given
    const authenticationState = await clerkClient.authenticateRequest(dummyRequest as any)

    if (authenticationState.status === 'signed-in') {
      return {
        req: {
          hostname: opts.req.headers.host ?? '',
          protocol: 'wss',
        },
        auth: authenticationState.toAuth() as SessionAuthObject,
        ...DEFAULT_CONTEXT,
      }
    } else {
      // A hack to prevent the client from hanging.
      // We immediately close the connection and don't include anything in the context.
      // We should move to SSE to avoid this problem.
      opts.res.close()
      return {} as never
    }
  },
})

const close = async (exitCode: 1 | 0 = 0) => {
  setTimeout(() => exit(exitCode), 3000).unref()
  console.info('Shutting down server')

  // Get clients to reconnect ( to hopefully a newer instance of the server )
  wssHandler.broadcastReconnectNotification()
  await Promise.allSettled([redisClient.disconnect(), postgres.end()])
  await new Promise(resolve => server.close(resolve))
}

process.once('SIGTERM', () => close(0))
process.once('SIGINT', () => close(0))

process.once('uncaughtException', reason => {
  console.error(reason)
  close(1)
})

process.title = 'upbound-api'

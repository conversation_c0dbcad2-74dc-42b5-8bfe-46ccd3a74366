import { clerkClient } from '@clerk/express'
import type { RedisClient } from '@upbound/redis'
import { RedisPersistentCache } from '@upbound/redis'
import { isNonEmpty, ONE_MIN } from '@upbound/utils'
import { default as HttpAgent, HttpsAgent } from 'agentkeepalive'
import type { ExtendOptions, Got } from 'got-esm'
import got from 'got-esm'
import { z } from 'zod'

const makeAccessTokenKeyFor = (userId: string) => `hubSpotAccessToken:${userId}`

export const HubSpotIntegrationMe = z.object({
  portalId: z.number(),
  timeZone: z.string(),
  utcOffset: z.string(),
  utcOffsetMilliseconds: z.number(),
})

export class HubSpotClient {
  #gotInstance: Got
  #cache = RedisPersistentCache.apply('hubspot', this.redisClient)
  #lastKnownAccessToken: string | undefined

  static readonly REQUIRED_SCOPES = [
    'oauth' as const,
    'crm.objects.owners.read' as const,

    'crm.objects.contacts.read' as const,
    'crm.objects.contacts.write' as const,
    'crm.schemas.contacts.read' as const,

    'crm.objects.companies.read' as const,
    'crm.objects.companies.write' as const,
    'crm.schemas.companies.read' as const,

    'crm.objects.deals.read' as const,
    'crm.objects.deals.write' as const,
    'crm.schemas.deals.read' as const,
  ]

  constructor(
    private readonly redisClient: RedisClient,
    readonly clerkHubSpotUser: string
  ) {
    const defaultGotOptions: Got | ExtendOptions = {
      // Base URL of the API.
      // Inner-methods should only need to specify the "path".
      prefixUrl: 'https://api.hubapi.com',
      // Default "retry" logic.
      retry: {
        limit: 3,
        maxRetryAfter: ONE_MIN + 2000, // 60 secs + 2 sec jitter.
      },
      agent: {
        http: new HttpAgent(),
        https: new HttpsAgent(),
      },
      // Sets access-token before request and caches the access-token in scope for faster re-use
      hooks: {
        // Get access-token before every request.
        beforeRequest: [
          async options => {
            const accessToken =
              this.#lastKnownAccessToken || (await this.getHubSpotAccessToken_(clerkHubSpotUser))?.token

            if (!this.#lastKnownAccessToken) {
              this.#lastKnownAccessToken = accessToken
            }

            options.headers = {
              ...options.headers,
              Authorization: 'Bearer ' + accessToken,
            }
          },
        ],
        // Invalidates access-token on 401.
        afterResponse: [
          async (response, retryWithMergedOptions) => {
            if (response.statusCode === 401 || response.statusCode === 403) {
              this.#lastKnownAccessToken = void 0
              await this.invalidateAccessToken()
              return retryWithMergedOptions({})
            }

            if (response.statusCode === 429 || response.statusCode === 418) {
              const retryAfterSecs = response.headers['retry-after']
              const str = retryAfterSecs ? retryAfterSecs + 's' : 'Invalid'
              console.info(
                { retryAfterSecs, statusCode: response.statusCode, body: response.body },
                `RateLimit. RetryAfter: ${str}`
              )
            }

            return response
          },
        ],
      },
    }

    this.#gotInstance = got.extend(defaultGotOptions)
  }

  private getHubSpotAccessToken_ = this.#cache.memoizeLockedUnsafe(
    async (clerkHubSpotUser: string) => {
      const creds = await clerkClient.users.getUserOauthAccessToken(clerkHubSpotUser, 'hubspot')

      if (isNonEmpty(creds.data)) {
        const hubSpotCreds = creds.data[0]
        return { token: hubSpotCreds.token, scopes: hubSpotCreds.scopes }
      }
    },
    makeAccessTokenKeyFor,
    x => {
      if (x) {
        return { ttlMs: ONE_MIN }
      } else {
        return null
      }
    }
  )

  getHubSpotAccessToken = () => this.getHubSpotAccessToken_(this.clerkHubSpotUser)
  invalidateAccessToken = () => this.#cache.invalidate(makeAccessTokenKeyFor(this.clerkHubSpotUser))

  async whoami(): Promise<z.infer<typeof HubSpotIntegrationMe>> {
    return this.#gotInstance
      .get('account-info/v3/details')
      .json()
      .then(x => HubSpotIntegrationMe.parse(x))
  }

  /**
   * Validate that this HubSpotClient has access to the requested scopes.
   * All scopes are requested by default unless signalled otherwise.
   */
  validateScopes = async (mustHaveScopes = HubSpotClient.REQUIRED_SCOPES) => {
    const creds = await this.getHubSpotAccessToken()

    if (!creds) {
      return {
        status: 'missing_creds' as const,
      }
    }

    const missingScopes = mustHaveScopes.filter(x => !creds.scopes?.includes(x))

    if (creds.scopes && isNonEmpty(creds.scopes) && missingScopes.length === 0) {
      return {
        status: 'valid' as const,
        scopes: creds.scopes,
      }
    }

    return {
      status: 'missing_scopes' as const,
      missingScopes,
    }
  }

  async getCompanyProperties(): Promise<string[]> {
    return (
      this.#gotInstance
        .get('properties/v1/companies/properties')
        .json()
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        .then((x: any) => x.map((y: { name: string }) => y.name))
    )
  }

  async getContactProperties(): Promise<string[]> {
    return (
      this.#gotInstance
        .get('properties/v1/contacts/properties')
        .json()
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        .then((x: any) => x.map((y: { name: string }) => y.name))
    )
  }

  async getHubspotObjectProperties(objectType: string) {
    const schema = z.object({
      results: z
        .object({
          name: z.string(),
          label: z.string(),
          description: z.string(),
          groupName: z.string(),
          type: z.string(),
          fieldType: z.string(),
          hasUniqueValue: z.boolean(),
          hidden: z.boolean(),
          options: z
            .object({
              label: z.string(),
              value: z.string(),
              hidden: z.boolean(),
            })
            .passthrough()
            .array(),
          modificationMetadata: z.object({
            archivable: z.boolean(),
            readOnlyDefinition: z.boolean(),
            readOnlyValue: z.boolean(),
          }),
        })
        .passthrough()
        .array(),
    })

    return this.#gotInstance
      .get(`crm/v3/properties/${objectType}`, {
        searchParams: {
          archived: 'false',
        },
      })
      .json()
      .then(x => schema.parse(x))
  }
}

import { TRPCError } from '@trpc/server'
import type { SqlClient } from '@upbound/postgres'
import type { DBWorkflow, WorkflowLLM } from '@upbound/shared'
import { AgentDataTypeWithUnconfigured } from '@upbound/shared/agents'
import { addDigestToColumnDefinition, type ColumnDefinition, type ColumnMap } from '@upbound/shared/workflow'
import { isNotNullOrUndefined } from '@upbound/utils'
import type { OpenAI } from 'openai'
import { v7 as uuidv7 } from 'uuid'
import { z } from 'zod'
import { HandlebarsDefinition } from '../utils/schemas.js'

const ColumnId = z.string().uuid()
const RESERVED_COLUMN_NAMES = new Set(['updatedAt', 'createdAt', 'rowId'])

const ColumnName = z.string().refine(x => !RESERVED_COLUMN_NAMES.has(x), 'Column name is reserved')
const OptionalColumnName = ColumnName.optional().transform(x => (x?.trim().length === 0 ? undefined : x))

export const DerivedColumnDefinition = z.object({
  columnName: ColumnName,
  subPath: z.string().array(),
  formulaTemplate: z.string().optional(),
})

export type DerivedColumn = z.infer<typeof DerivedColumnDefinition>

const FormulaDefinition = z
  .discriminatedUnion('type', [
    z.object({
      type: z.literal('derived'),
      formula: HandlebarsDefinition,
      description: HandlebarsDefinition.optional(),
    }),
    z.object({
      type: z.literal('described'),
      description: HandlebarsDefinition,
      rowData: z.record(z.string(), z.unknown()).optional(),
    }),
  ])
  .optional()

export const StringColumnDefinition = z.object({
  type: z.literal('string'),
  columnId: ColumnId,
  columnName: ColumnName,
  defaultFormula: FormulaDefinition,
  config: z
    .object({
      subType: z.enum(['text', 'multilineText', 'email', 'url']).optional(),
    })
    .default({}),
})

export const AgentColumnDefinition = z.object({
  type: z.literal('agent'),
  columnId: ColumnId,
  columnName: OptionalColumnName,
  runIf: FormulaDefinition,
  config: z.object({
    dataType: AgentDataTypeWithUnconfigured,
    task: z.string(),
    agentTask: z.string(),
  }),
})

export const UpsertColumnSchema = z.discriminatedUnion('type', [StringColumnDefinition, AgentColumnDefinition])

const genUniqueColumnName = (columnName: string, existingColumnNames: Set<string>) => {
  let c = columnName

  for (let i = 1; existingColumnNames.has(c); i++) {
    c = [c.replace(new RegExp(` \\(${i - 1}\\)$`), ''), `(${i})`].join(' ')
  }

  return c
}

const columnNameSetFor = (columnMap: ColumnMap, columnId: string) => {
  return new Set(
    Object.values(columnMap)
      .filter(x => x.columnId !== columnId)
      .map(c => c.columnName)
  )
}

export class WorkflowColumnService {
  constructor(
    private readonly dbWorkflow: DBWorkflow,
    private readonly sql: SqlClient,
    private readonly workflowLLM: WorkflowLLM,
    private readonly openAI: OpenAI
  ) {}

  private computeDefaultFormula = async (
    defaultFormula: z.infer<typeof FormulaDefinition>,
    columnMap: ColumnMap,
    orgId: string
  ) => {
    if (!defaultFormula) {
      return void 0
    }

    switch (defaultFormula.type) {
      case 'derived': {
        return {
          formula: defaultFormula.formula.templateDef,
        }
      }
      case 'described': {
        if (defaultFormula.description.templateDef.template.length > 0) {
          const computedFormula = {
            template: '{{0}}',
            templateMapping: {
              0: {
                columnId: '123',
                subPath: [],
              },
            },
          }

          return {
            formula: computedFormula,
            description: defaultFormula.description.templateDef,
          }
        }
      }
    }
  }

  private computeRunIfFormula = async (
    runIf: z.infer<typeof FormulaDefinition>,
    columnMap: ColumnMap,
    orgId: string
  ) => {
    if (!runIf) {
      return void 0
    }

    switch (runIf.type) {
      case 'derived': {
        return {
          formula: runIf.formula.templateDef,
        }
      }
      case 'described': {
        if (runIf.description.templateDef.template.length > 0) {
          const computedFormula = {
            template: '{{0}}',
            templateMapping: {
              0: {
                columnId: '123',
                subPath: [],
              },
            },
          }

          return {
            formula: computedFormula,
            description: runIf.description.templateDef,
          }
        }
      }
    }
  }

  private addStaticColumn = async ({
    auth,
    columnMap,
    columnDefinition,
    workflowTableId,
  }: {
    auth: { orgId: string }
    columnMap: ColumnMap
    columnDefinition: z.output<typeof StringColumnDefinition>
    workflowTableId: number
  }) => {
    const computedDefaultFormula = await this.computeDefaultFormula(
      columnDefinition.defaultFormula,
      columnMap,
      auth.orgId
    )
    let columnName = columnDefinition.columnName?.trim() ?? ''

    if (columnName.length === 0) {
      if (columnDefinition.defaultFormula) {
        columnName = await this.computeColumnNameForPrompt({
          template:
            columnDefinition.defaultFormula.type === 'derived'
              ? columnDefinition.defaultFormula?.formula.templateDef.template
              : columnDefinition.defaultFormula?.description.templateDef.template,
          auth,
          existingColumnNames: columnNameSetFor(columnMap, columnDefinition.columnId),
        })
      } else {
        // validate static string column
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Column name is required',
        })
      }
    }

    const workflowTable = await this.dbWorkflow.upsertColumn({
      customerId: auth.orgId,
      workflowTableId,
      columnDefinition: {
        ...columnDefinition,
        columnName,
        columnVariant: 'static',
        config: {
          ...columnDefinition.config,
        },
        uiVariant: columnDefinition.defaultFormula
          ? columnDefinition.defaultFormula.type === 'derived'
            ? 'derived'
            : 'formula'
          : 'static',
        defaultFormula: computedDefaultFormula,
      },
    })

    return {
      workflowTable,
      updatedColumnId: columnDefinition.columnId,
    }
  }

  private computeColumnNameForPrompt = async ({
    template,
    existingColumnNames,
    auth,
  }: {
    auth: { orgId: string }
    template: string
    existingColumnNames: Set<string>
  }) => {
    const computedColumnName = (await this.workflowLLM.shortFieldNameForPrompt(auth.orgId, template)) ?? 'New Column'
    return genUniqueColumnName(computedColumnName, existingColumnNames)
  }

  private addAgentColumn = async ({
    auth,
    columnMap,
    columnDefinition,
    workflowTableId,
    autoRun,
  }: {
    auth: { orgId: string }
    columnMap: ColumnMap
    columnDefinition: z.output<typeof AgentColumnDefinition>
    workflowTableId: number
    autoRun?: 'onCreate' | 'onUpdate'
  }) => {
    const computedRunIfFormula = await this.computeRunIfFormula(columnDefinition.runIf, columnMap, auth.orgId)

    const newColumn = await addDigestToColumnDefinition({
      ...columnDefinition,
      columnName: columnDefinition.columnName ?? 'New AI Agent Column',
      type: 'agent' as const,
      columnVariant: 'computed' as const,
      runIf: computedRunIfFormula,
      config: {
        task: columnDefinition.config.task,
        agentTask: columnDefinition.config.agentTask,
        dataType: columnDefinition.config.dataType,
      },
      autoRun,
      inputColumns: [],
    })

    const workflowTable = await this.dbWorkflow.upsertColumn({
      customerId: auth.orgId,
      workflowTableId,
      columnDefinition: newColumn,
    })

    return {
      workflowTable,
      updatedColumnId: columnDefinition.columnId,
    }
  }

  renameColumn = async ({
    auth,
    workflowTableId,
    columnId,
    newColumnName,
  }: {
    auth: { orgId: string }
    workflowTableId: number
    columnId: string
    newColumnName: string
  }) => {
    const columns = await this.dbWorkflow.getWorkflowTable({
      customerId: auth.orgId,
      workflowTableId,
    })

    if (!columns) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Table not found' })
    }

    const columnMap = columns.tableDefinition.columnDefinitions

    if (!columnMap[columnId]) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Column not found' })
    }

    const updatedName = genUniqueColumnName(newColumnName, columnNameSetFor(columnMap, columnId))
    const updatedColumnDef = { ...columnMap[columnId], columnName: updatedName } satisfies ColumnDefinition

    const workflowTable = await this.dbWorkflow.upsertColumn({
      customerId: auth.orgId,
      workflowTableId,
      columnDefinition: updatedColumnDef,
    })

    return {
      workflowTable,
      updatedColumnId: columnId,
    }
  }

  setColumnAutoRun = async ({
    auth,
    workflowTableId,
    items,
  }: {
    auth: { orgId: string }
    workflowTableId: number
    items: { columnId: string; autoRun: 'onCreate' | null }[]
  }) => {
    const columns = await this.dbWorkflow.getWorkflowTable({
      customerId: auth.orgId,
      workflowTableId,
    })

    if (!columns) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Table not found' })
    }

    const columnMap = columns.tableDefinition.columnDefinitions

    const updatedColumnDefinitions = items
      .map(({ columnId, autoRun }) => {
        const columnDef = columnMap[columnId]

        if (columnDef && columnDef.columnVariant === 'computed') {
          return {
            ...columnDef,
            autoRun,
          }
        } else {
          return null
        }
      })
      .filter(isNotNullOrUndefined)

    if (updatedColumnDefinitions.length > 0) {
      const workflowTable = await this.dbWorkflow.upsertColumns({
        customerId: auth.orgId,
        workflowTableId,
        columnDefinitions: Object.fromEntries(updatedColumnDefinitions.map(x => [x.columnId, x])),
      })

      return {
        workflowTable,
        updatedColumnIds: updatedColumnDefinitions.map(x => x.columnId),
      }
    } else {
      return {
        workflowTable: columns,
        updatedColumnIds: [],
      }
    }
  }

  addColumn = async ({
    auth,
    input,
  }: {
    auth: { orgId: string }
    input: {
      upsert: boolean
      columnDefinition: z.output<typeof UpsertColumnSchema>
      workflowTableId: number
    }
  }) => {
    const columns = await this.dbWorkflow.getWorkflowTable({
      customerId: auth.orgId,
      workflowTableId: input.workflowTableId,
    })

    if (!columns) {
      throw new TRPCError({ code: 'NOT_FOUND', message: 'Table not found' })
    }

    const columnMap = columns.tableDefinition.columnDefinitions

    const columnNameToFieldMapping = Object.fromEntries(
      Object.values(columnMap).map(c => [c.columnName, c.columnId]) ?? []
    )

    // Disallow duplicate column ids unless upserting!
    if (!input.upsert) {
      if (columns.tableDefinition.columnDefinitions[input.columnDefinition.columnId]) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Column already exists. Please refresh the page and try again.',
        })
      }
    }

    if (input.columnDefinition.columnName && columnNameToFieldMapping[input.columnDefinition.columnName]) {
      input.columnDefinition.columnName = genUniqueColumnName(
        input.columnDefinition.columnName,
        columnNameSetFor(columnMap, input.columnDefinition.columnId)
      )
    }

    const tableAutoRunEnabled = Object.values(columnMap)
      .filter(x => x.columnVariant === 'computed')
      .every(c => c.autoRun === 'onCreate')
      ? 'onCreate'
      : void 0

    switch (input.columnDefinition.type) {
      case 'string': {
        return this.addStaticColumn({
          auth,
          columnMap,
          columnDefinition: input.columnDefinition,
          workflowTableId: input.workflowTableId,
        })
      }

      case 'agent': {
        return this.addAgentColumn({
          auth,
          columnMap,
          columnDefinition: input.columnDefinition,
          workflowTableId: input.workflowTableId,
          autoRun: tableAutoRunEnabled,
        })
      }
    }
  }

  createDerivedColumns = async ({
    auth,
    derivedColumns,
    sourceColumnId,
    workflowTableId,
  }: {
    auth: { orgId: string }
    derivedColumns: DerivedColumn[]
    sourceColumnId: string
    workflowTableId: number
  }) => {
    const updatedColumnIds: string[] = []

    for (const derivedColumn of derivedColumns) {
      const res = await this.addColumn({
        auth,
        input: {
          upsert: false,
          columnDefinition: {
            type: 'string' as const,
            columnId: uuidv7(),
            columnName: derivedColumn.columnName,
            config: {
              subType: 'text',
            },
            defaultFormula: {
              type: 'derived' as const,
              formula: {
                inputs: [sourceColumnId],
                templateDef: {
                  template: derivedColumn.formulaTemplate ?? '{{0}}',
                  templateMapping: {
                    0: {
                      columnId: sourceColumnId,
                      subPath: derivedColumn.subPath,
                    },
                  },
                },
              },
            },
          },
          workflowTableId,
        },
      })

      updatedColumnIds.push(res!.updatedColumnId)
    }

    return updatedColumnIds
  }
}

import type { ColumnDefinitionOf } from '@upbound/shared/workflow'
import { isNotNullOrUndefined } from '@upbound/utils'
import { type CsvError, parse as parseCsv } from 'csv-parse'
import type { Request, Response } from 'express'
import { v7 as uuidv7 } from 'uuid'
import { createContext } from './createContext.js'

const RESERVED_COLUMN_NAMES = new Set(['rowIndex', 'rowId', 'createdAt', 'updatedAt'])

export async function uploadCsv(req: Request, res: Response) {
  const {
    auth,
    db: { dbWorkflow },
    postgres,
  } = createContext({ req })

  if (!isNotNullOrUndefined(auth.orgId) || !isNotNullOrUndefined(auth.userId)) {
    res.status(401)
    return res.end()
  }

  if (!req.file) {
    res.status(400)
    return res.end()
  }

  const orgId = auth.orgId
  const customerId = orgId
  const userId = auth.userId
  const file = req.file

  const parseResult = await new Promise<{ err: CsvError | undefined; parsed: string[][] }>(resolve =>
    parseCsv(
      file.buffer,
      {
        relaxQuotes: true,
        skipEmptyLines: true,
      },
      (err, parsed: string[][]) => {
        resolve({ err, parsed })
      }
    )
  )

  if (parseResult.err) {
    res.status(400).send(parseResult.err.message)
    return res.end()
  }

  // check if csv has headers & rows
  if (!parseResult.parsed[0] || !parseResult.parsed[0][0]) {
    res.status(400).send('The CSV file is empty.')
    return res.end()
  }

  const csvColumns = parseResult.parsed[0]
  const csvRows = parseResult.parsed.slice(1)

  const columnDefinitions = csvColumns.map((field, index) => {
    let columnName = field
    if (RESERVED_COLUMN_NAMES.has(columnName)) {
      columnName = `${columnName} (1)`
    }
    if (field.trim().length === 0 || /^_\d$/.test(field)) {
      columnName = `Column ${index + 1}`
    }

    return {
      type: 'agent',
      columnId: uuidv7(),
      columnName,
      config: {
        dataType: { type: 'unconfigured' },
        task: '',
        agentTask: '',
      },
    } as ColumnDefinitionOf<'agent'>
  })

  const rows = csvRows.map(row => {
    const rowData: Record<string, { status: 'user'; value: string }> = {}
    for (let i = 0; i < columnDefinitions.length; i++) {
      const col = columnDefinitions[i]
      if (!col) continue
      rowData[col.columnId] = {
        status: 'user',
        value: cleanString(row[i] ?? ''),
      }
    }

    return {
      rowId: uuidv7(),
      rowData,
    }
  })

  const cDefMap = Object.fromEntries(columnDefinitions.map(c => [c.columnId, c] as const))

  const workflowTableId = await postgres.begin(async sql => {
    const workflowTable = await dbWorkflow.createWorkflowTable(
      {
        name: file.originalname ?? 'New table',
        tableDefinition: {
          columnDefinitions: cDefMap,
        },
        customerId,
        userId,
      },
      sql
    )

    const workflowTableId = workflowTable.table.workflowTableId

    if (rows) {
      await dbWorkflow.addRows(
        {
          customerId,
          workflowTableId,
          rows,
        },
        sql
      )
    }

    return workflowTableId
  })

  res.status(200).send(workflowTableId.toString())
  res.end()
}

/**
 * Remove illegal unicode characters from the string
 */
function cleanString(input: string) {
  // Remove control characters and other non-printable characters
  return (
    input
      // biome-ignore lint/suspicious/noControlCharactersInRegex: We're matching on control characters to remove them here
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
      .replace(/\uFEFF/g, '') // Remove byte order mark
      .replace(/\u200B/g, '') // Remove zero width space
      .trim()
  )
}

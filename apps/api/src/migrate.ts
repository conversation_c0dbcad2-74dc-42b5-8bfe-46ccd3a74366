import path from 'node:path'
import { fileURLToPath } from 'node:url'
import pg from 'pg'
import pgconnstring from 'pg-connection-string'
import { migrate } from 'postgres-migrations'
import { AppEnv } from './env.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function runMigrate() {
  const env = AppEnv.pick({ POSTGRES_URL: true }).parse(process.env)
  const conn = pgconnstring.parse(env.POSTGRES_URL)

  const client = new pg.Client({
    host: conn?.host ?? void 0,
    port: conn?.port ? Number.parseInt(conn.port) : void 0,
    user: conn?.user ?? void 0,
    password: conn?.password ?? void 0,
    database: conn?.database ?? void 0,
    ssl: conn.ssl as unknown as boolean,
  })

  try {
    await client.connect()
    const migrationPath = path.join(__dirname, '/', 'migrations')
    await migrate({ client }, migrationPath)
  } finally {
    client.end()
  }
}

runMigrate().catch(err => {
  console.error(err)
  process.exit(1)
})

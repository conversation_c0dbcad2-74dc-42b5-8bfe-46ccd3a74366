import type { TemplateDefinition } from '@upbound/shared/workflow'
import { isNonEmpty } from '@upbound/utils'
import { difference } from 'effect/Array'
import hb from 'handlebars'
import type { ZodTypeAny } from 'zod'
import { NEVER, z } from 'zod'

export const nonEmptyOrOptional = <T extends ZodTypeAny>(schema: T) => {
  return schema
    .array()
    .transform(x => (isNonEmpty(x) ? x : void 0))
    .optional()
}

export const IdInt = z.string().pipe(z.coerce.number().int().gt(0)).or(z.number().int().gt(0))
export const HostNameString = z.string().min(2)

export const HandlebarsDefinition = z
  .object({
    template: z.string(),
    templateMapping: z.record(
      z.object({
        columnId: z.string().uuid(),
        subPath: z
          .string()
          .array()
          .transform(x => (x.length === 0 ? void 0 : x))
          .optional(),
      })
    ),
  })
  .transform((x, ctx) => {
    try {
      const usedInternalIds: string[] = []

      const proxyHandler: ProxyHandler<Record<string, unknown>> = {
        get(_target, prop, _receiver) {
          usedInternalIds.push(prop.toString())
        },
      }

      const proxy = new Proxy({}, proxyHandler)
      const _ = hb.compile(x.template)(proxy)

      if (difference(Object.keys(x.templateMapping), usedInternalIds).length > 0) {
        ctx.addIssue({ code: 'custom', message: 'Invalid Handlebars template' })
        return NEVER
      }

      return {
        templateDef: x satisfies TemplateDefinition,
        inputs: Object.values(x.templateMapping).map(y => y.columnId),
      }
    } catch (e) {
      ctx.addIssue({ code: 'custom', message: 'Invalid Handlebars template' })
      return NEVER
    }
  })

const literalSchema = z.union([z.string(), z.number(), z.boolean(), z.null()])
type Literal = z.infer<typeof literalSchema>
export type JsonSchema = Literal | { [key: string]: JsonSchema } | JsonSchema[]
export const JsonSchema: z.ZodType<JsonSchema> = z.lazy(() =>
  z.union([literalSchema, z.array(JsonSchema), z.record(JsonSchema)])
)

export const UserId = z.string().length(32).startsWith('user_')

export const AccountSnippetId = z
  .string()
  .startsWith('act_')
  .transform(x => Number.parseInt(x.substring(4)))

export const ContactSnippetId = z
  .string()
  .startsWith('ctc_')
  .transform(x => Number.parseInt(x.substring(4)))

import { partition } from 'effect/Array'
import { pipe } from 'effect/Function'

export function sortedObjectString(obj: Record<string, unknown>) {
  return Object.entries(obj)
    .toSorted(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}=${value}`)
    .join('&')
}

export function partitionNullishData<T, K extends keyof T>(data: T[], key: K) {
  const [left, right] = pipe(
    data,
    partition((d): d is T & Record<K, NonNullable<T[K]>> => d[key] !== undefined && d[key] !== null)
  )

  return {
    invalid: left as (T & Record<K, null | undefined>)[],
    valid: right,
  }
}

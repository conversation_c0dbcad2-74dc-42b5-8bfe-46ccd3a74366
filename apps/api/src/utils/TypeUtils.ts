/**
 * A type-hint to improve DX when dealing with intersected types ( i.e A & B )
 * This does not change the underlying type. It only helps the typescript
 * compiler ( and language-server ) show a refined version of the type
 * which can be easier to read and debug.
 */
export type Pretty<T> = {
  [K in keyof T]: T[K]
} extends infer U
  ? U
  : never

/**
 * Get the keys of T without any keys of U.
 */
export type Without<T, U> = {
  [P in Exclude<keyof T, keyof U>]?: never
}

/**
 * Restrict using either exclusively the keys of T or exclusively the keys of U.
 * No unique keys of T can be used simultaneously with any unique keys of U.
 *
 * More: https://timhwang21.gitbook.io/index/programming/typescript/xor-type#at-least-one-but-not-multiple
 */
export type XOR<A, B> = A | B extends object ? Pretty<Without<A, B> & B> | Pretty<Without<B, A> & A> : A | B
export type XOR3<A, B, C> = XOR<XOR<A, B>, C>
export type XOR4<A, B, C, D> = XOR<XOR<XOR<A, B>, C>, D>
export type XOR5<A, B, C, D, E> = XOR<XOR<XOR<XOR<A, B>, C>, D>, E>
export type XOR6<A, B, C, D, E, F> = XOR<XOR<XOR<XOR<XOR<A, B>, C>, D>, E>, F>
export type XOR7<A, B, C, D, E, F, G> = XOR<XOR<XOR<XOR<XOR<XOR<A, B>, C>, D>, E>, F>, G>
export type XOR8<A, B, C, D, E, F, G, H> = XOR<XOR<XOR<XOR<XOR<XOR<XOR<A, B>, C>, D>, E>, F>, G>, H>

export type RemoveNullable<T, K extends keyof T> = Omit<T, K> & {
  [P in K]-?: NonNullable<T[P]>
}

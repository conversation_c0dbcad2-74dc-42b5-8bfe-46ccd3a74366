{"name": "@app/api", "version": "0.0.1", "description": "", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev:new": "bun --watch --env-file=../../.env.local src/index.ts", "dev": "pnpx tsx watch --env-file=../../.env.local src/index.ts", "start": "node --env-file=../../.env.local build/index.js", "migrate": "pnpx tsx --env-file=../../.env.local src/migrate.ts", "migrateColumnDefinitions": "node --enable-source-maps --no-warnings --env-file=../../.env.local --loader ts-node/esm src/scripts/migrateColumnDefinitions.ts", "emailChatHistory": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/emailChatHistory.ts", "getHubspotAccessToken": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/getHubspotAccessToken.ts", "createClerkInvitation": "pnpx tsx --env-file=../../.env.local src/scripts/createClerkInvitation.ts", "getClerkUsers": "pnpx tsx --env-file=../../.env.local src/scripts/getClerkUsers.ts", "getClerkOrgsUngatedForFeature": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/getClerkOrgsUngatedForFeature.ts", "getClerkOrgsUngatedForFeatureProd": "dotenv -e ../../.env.production.local -- ts-node-dev --exit-child src/scripts/getClerkOrgsUngatedForFeature.ts", "enrichPhoneNumbers": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/enrichPhoneNumberForContact.ts", "redisDumpAndExport": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/redisDumpAndExport.ts", "getLecacyCampaignsPerUserOrg": "dotenv -e ../../.env.local -- ts-node-dev --exit-child src/scripts/getLecacyCampaignsPerUserOrg.ts", "stripePlanPosthogSync": "pnpx tsx --env-file=../../.env.local src/scripts/stripePlanPosthogSync.ts", "columnTypeStats": "pnpx tsx --env-file=../../.env.local src/scripts/columnTypeStats.ts", "snapshotCustomerTable": "pnpx tsx --env-file=../../.env.local src/scripts/snapshotCustomerTable.ts", "backfillClerkUsersToDb": "pnpx tsx --env-file=../../.env.local src/scripts/backfillClerkUsersToDb.ts"}, "devDependencies": {"@clerk/backend": "^1.34.0", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/multer": "^2.0.0", "@types/pg": "^8.15.4", "@types/ws": "^8.18.1", "@upbound/types": "workspace:*", "tsconfig": "workspace:*", "typescript": "5.8.3"}, "author": "Upbound", "license": "UNLICENSED", "dependencies": {"@clerk/express": "^1.7.10", "@json2csv/node": "^7.0.6", "@trpc/server": "11.1.2", "@types/lodash-es": "^4.17.12", "@types/uuid": "^10.0.0", "@upbound/postgres": "workspace:*", "@upbound/redis": "workspace:*", "@upbound/shared": "workspace:*", "@upbound/utils": "workspace:*", "agentkeepalive": "^4.6.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dependency-graph": "^1.0.0", "effect": "^3.17.4", "express": "^4.21.1", "got-esm": "npm:got@14", "handlebars": "^4.7.8", "lodash-es": "^4.17.21", "multer": "^2.0.2", "openai": "^5.11.0", "pg": "^8.16.3", "pg-connection-string": "^2.9.1", "postgres-migrations": "^5.3.0", "stripe": "^17.7.0", "superjson": "^2.2.1", "uuid": "^11.1.0", "ws": "^8.18.3", "zod": "^3.25.76"}}
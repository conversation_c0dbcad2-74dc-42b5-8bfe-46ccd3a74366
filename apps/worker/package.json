{"name": "@app/worker", "version": "0.0.1", "description": "", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev:new": "bun --watch --env-file=../../.env.local src/index.ts", "dev": "pnpx tsx watch --env-file=../../.env.local src/index.ts", "start": "node --env-file=../../.env.local build/index.js", "evals": "node --env-file=../../.env.local ./node_modules/vitest/vitest.mjs"}, "author": "Upbound", "license": "UNLICENSED", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.22", "@ai-sdk/google-vertex": "^2.2.27", "@ai-sdk/openai": "^1.3.23", "@effect/opentelemetry": "^0.53.14", "@effect/platform": "^0.87.13", "@effect/platform-node": "^0.88.2", "@effect/sql": "^0.40.0", "@effect/sql-pg": "^0.41.0", "@openrouter/ai-sdk-provider": "^0.7.3", "@opentelemetry/exporter-metrics-otlp-http": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-metrics": "^2.0.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@opentelemetry/sdk-trace-web": "^2.0.0", "@upbound/postgres": "workspace:*", "@upbound/redis": "workspace:*", "@upbound/shared": "workspace:*", "@upbound/utils": "workspace:*", "ai": "^4.3.19", "effect": "^3.17.4", "got-esm": "npm:got@14", "hash-wasm": "^4.11.0", "libphonenumber-js": "^1.11.13", "openai": "^5.11.0", "pino": "^8.20.0", "radash": "^12.1.0", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@effect/language-service": "^0.28.3", "@effect/vitest": "^0.24.1", "@types/express": "^4.17.23", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "pino-pretty": "^11.0.0", "tsconfig": "workspace:*", "typescript": "5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4"}}
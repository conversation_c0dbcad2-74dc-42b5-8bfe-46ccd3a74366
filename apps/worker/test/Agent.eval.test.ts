import { describe, expect, it } from '@effect/vitest'
import { ApolloIO } from '@upbound/integrations/apollo.io'
import { <PERSON><PERSON><PERSON> } from '@upbound/integrations/hubspot'
import { OceanIO } from '@upbound/integrations/ocean.io'
import { Proxycurl } from '@upbound/integrations/proxycurl'
import { <PERSON><PERSON>er<PERSON><PERSON> } from '@upbound/integrations/scraperapi'
import { Serper } from '@upbound/integrations/serper'
import { Zenrows } from '@upbound/integrations/zenrows'
import { RedisClient } from '@upbound/redis/effect'
import { ConfigProvider, Effect, Layer } from 'effect'
import { LogLevelLive } from '../src/effect-apm/PinoLogger.js'
import { Agents } from '../src/integrations/Agents.js'
import { Enrow } from '../src/integrations/Enrow.js'
import { FindyMail } from '../src/integrations/FindyMail.js'
import { FullEnrich } from '../src/integrations/FullEnrich.com.js'
import { LeadMagic } from '../src/integrations/LeadMagic.js'
import { Prospeo } from '../src/integrations/Prospeo.js'
import { SearchAgent } from '../src/integrations/SearchAgent.js'
import { Zerobounce } from '../src/integrations/Zerobounce.js'

/**
 * These tests are live tests that require access to a set of external services and API keys for third party services.
 * Ensure Redis is also running and available
 */
describe.skip('Agent LLM Tests', { timeout: 120_000 }, () => {
  const configLayer = Layer.suspend(() => Layer.setConfigProvider(ConfigProvider.fromEnv()))
  const AgentLive = Layer.suspend(() =>
    Layer.mergeAll(
      Agents.Default,
      SearchAgent.Default,
      Serper.Default,
      Hubspot.Default,
      Zenrows.Default,
      ScraperApi.Default,
      Proxycurl.Default,
      FindyMail.Default,
      Prospeo.Default,
      Enrow.Default,
      Zerobounce.Default,
      LeadMagic.Default,
      OceanIO.Default,
      ApolloIO.Default,
      FullEnrich.Default
    ).pipe(Layer.provide(RedisClient.Default), Layer.provide(configLayer), Layer.provide(LogLevelLive))
  )

  describe('Search Agent', () => {
    it.effect('Performs a google search and returns a result', () =>
      Effect.gen(function* () {
        const agent = yield* SearchAgent

        const result = yield* agent.searchAgent(
          'What is the first link that shows up when you search for the word "freckle.io"?'
        )

        expect(result.result).toBeDefined()
        expect(result.sources).toBeDefined()

        const googResult = result.toolResults?.find(x => x.toolName === 'google_search')
        expect(googResult).toBeDefined()
        expect(googResult?.args?.query).toEqual('freckle.io')

        expect(result.result).toEqual('https://www.freckle.io/')
        expect(result.sources).toEqual(['https://www.freckle.io/'])

        return result
      }).pipe(Effect.provide(AgentLive))
    )
  })

  describe('Main Agent - General purpose tests', () => {
    it.effect('Hello world', () =>
      Effect.gen(function* () {
        const agent = yield* Agents

        const result = yield* agent.computeAgentCellValue({
          customerId: '1',
          dataType: {
            type: 'singleLineText',
            formatTemplate: '{{name}}',
          },
          task: 'Say "Hello World"',
          context: [],
        })

        expect(result.result).toBeDefined()
        expect(result.result).toEqual('Hello World')
        expect(result.sources.length).toEqual(0)
        expect(result.steps.length).toEqual(0)

        return result
      }).pipe(Effect.provide(AgentLive))
    )

    it.effect('CEO name from company website', () =>
      Effect.gen(function* () {
        const agent = yield* Agents

        const testCases = [
          {
            company: 'https://www.freckle.io',
            expected: 'Nathan Merzvinskis',
          },
          {
            company: 'freckle.io',
            expected: 'Nathan Merzvinskis',
          },
          {
            company: 'skedulo.com',
            expected: 'Matt Fairhurst',
          },
          {
            company: 'microsoft.com',
            expected: 'Satya Nadella',
          },
        ]

        yield* Effect.all(
          testCases.map(({ company, expected }) =>
            Effect.gen(function* () {
              const result = yield* agent.computeAgentCellValue({
                customerId: '1',
                dataType: {
                  type: 'singleLineText',
                  formatTemplate: '[First Name] [Last Name] (e.g. John Smith)',
                },
                task: 'Find the full name of the current Chief Executive Officer (CEO) or equivalent top executive position for the company',
                context: [
                  {
                    columnName: 'Company Website',
                    value: company,
                  },
                ],
              })

              expect(result.result).toBeDefined()
              expect(result.result).toEqual(expected)
              expect(result.sources.length).toBeGreaterThan(0)
              expect(result.steps.length).toBeGreaterThan(0)
            })
          ),
          { concurrency: 'unbounded' }
        )
      }).pipe(Effect.provide(AgentLive))
    )

    it.effect('CEO LinkedIn URL from company website', () =>
      Effect.gen(function* () {
        const agent = yield* Agents

        const testCases = [
          {
            company: 'https://www.freckle.io',
            expected: 'https://www.linkedin.com/in/nathan-merzvinskis',
          },
          {
            company: 'freckle.io',
            expected: 'https://www.linkedin.com/in/nathan-merzvinskis',
          },
          {
            company: 'skedulo.com',
            expected: 'https://www.linkedin.com/in/mattfairhurst',
          },
          {
            company: 'microsoft.com',
            expected: 'https://www.linkedin.com/in/satyanadella',
          },
        ]

        yield* Effect.all(
          testCases.map(({ company, expected }) =>
            Effect.gen(function* () {
              const result = yield* agent.computeAgentCellValue({
                customerId: '1',
                dataType: {
                  type: 'url',
                },
                task: `Find the LinkedIn profile URL of the company's current Chief Executive Officer (CEO)`,
                context: [
                  {
                    columnName: 'Company Website',
                    value: company,
                  },
                ],
              })

              expect(result.result).toBeDefined()
              expect(result.result).toEqual(expected)
              expect(result.sources.length).toBeGreaterThan(0)
              expect(result.steps.length).toBeGreaterThan(0)
            })
          ),
          { concurrency: 'unbounded' }
        )
      }).pipe(Effect.provide(AgentLive))
    )
  })

  describe('Main Agent - Sitemate', () => {
    it.effect('Email address from event', () =>
      Effect.gen(function* () {
        const agent = yield* Agents

        const testCases = [
          {
            context: {
              Name: 'Omari Morgan',
              Email: '<EMAIL>',
              'Lead ID': '00QOd00000Jn5QEMAZ',
              Timezone: 'America/Port_of_Spain',
              'Phone Number': '+18688683579221',
            },
            expected: '<EMAIL>',
          },
          // {
          //   context: {
          //     Name: 'Angelique Botha',
          //     Email: '<EMAIL>',
          //     'Lead ID': '00QOd00000IuGe2MAF',
          //   },
          //   expected: '<EMAIL>',
          // },
        ]

        yield* Effect.all(
          testCases.map(({ context, expected }) =>
            Effect.gen(function* () {
              const result = yield* agent.computeAgentCellValue({
                customerId: '1',
                dataType: {
                  type: 'email',
                },
                task: `Find the person's professional/work email address. If not available, leave empty.`,
                context: [
                  {
                    columnName: 'Event',
                    value: context,
                  },
                ],
              })

              console.dir(result, { depth: null })
              expect(result.result).toBeDefined()
              expect(result.result).toEqual(expected)
              expect(result.sources.length).toBeGreaterThan(0)
              expect(result.steps.length).toBeGreaterThan(0)
            })
          ),
          { concurrency: 'unbounded' }
        )
      }).pipe(Effect.provide(AgentLive))
    )
  })
})

import { createServer } from 'node:http'
import { HttpMiddleware, HttpRouter, HttpServer, HttpServerResponse } from '@effect/platform'
import { NodeHttpServer } from '@effect/platform-node'
import { Effect, Layer } from 'effect'

// Setting Up Express
const ServerLive = NodeHttpServer.layer(() => createServer(), { port: 4003 })

export const HttpServerLive = HttpRouter.empty.pipe(
  HttpRouter.get('/health', HttpServerResponse.json({ status: 'ok' })),
  HttpMiddleware.withLoggerDisabled,
  HttpServer.serve(),
  HttpMiddleware.withTracerDisabledForUrls(['/health']),
  Layer.provide(ServerLive),
  Layer.tap(() => Effect.logInfo('Server started on port 4003'))
)

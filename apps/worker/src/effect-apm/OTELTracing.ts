import { NodeSdk } from '@effect/opentelemetry'
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-http'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http'
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics'
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base'
import { Effect, Layer } from 'effect'
import { ServiceConfig } from './Config.js'

export const OtelExporterLive = ServiceConfig.pipe(
  Effect.map(({ nodeEnv, service, version, env }) =>
    NodeSdk.layer(() => ({
      resource: {
        serviceName: service,
        serviceVersion: version,
        attributes: { 'deployment.environment': env },
      },

      metricReader:
        nodeEnv === 'development'
          ? void 0
          : new PeriodicExportingMetricReader({
              exportIntervalMillis: 5000,
              exporter: new OTLPMetricExporter({ keepAlive: true }),
            }),

      spanProcessor:
        nodeEnv === 'development' ? void 0 : new BatchSpanProcessor(new OTLPTraceExporter({ keepAlive: true })),
    }))
  )
).pipe(Layer.unwrapEffect)

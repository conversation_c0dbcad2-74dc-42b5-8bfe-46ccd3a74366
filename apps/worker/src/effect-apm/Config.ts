import { Config, Effect } from 'effect'

export const ServiceConfig = Effect.gen(function* () {
  const nodeEnv = yield* Config.withDefault(Config.string('NODE_ENV'), 'development')
  const service = yield* Config.withDefault(Config.string('OTEL_SERVICE_NAME'), 'freckle_worker')
  const env = yield* Config.option(Config.string('DEPLOYMENT_ENVIRONMENT'))
  const version = yield* Config.option(Config.string('APP_VERSION'))

  return {
    nodeEnv,
    service,
    env: env._tag === 'Some' ? env.value : void 0,
    version: version._tag === 'Some' ? version.value : void 0,
  }
})

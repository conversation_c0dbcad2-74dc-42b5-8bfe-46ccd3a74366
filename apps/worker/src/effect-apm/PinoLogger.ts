import {
  Cause,
  Config,
  Context,
  Effect,
  FiberId,
  FiberRef,
  <PERSON>berRefs,
  HashSet,
  Layer,
  List,
  Logger,
  LogLevel,
  Option,
  Tracer,
} from 'effect'
import { currentContext } from 'effect/FiberRef'
import { pino } from 'pino'
import { ServiceConfig } from './Config.js'

// Load the log level from the configuration as a layer
export const LogLevelLive = Config.withDefault(Config.logLevel('LOG_LEVEL'), LogLevel.Info).pipe(
  Effect.andThen(level => Logger.minimumLogLevel(level)),
  Layer.unwrapEffect
)

const clearAllLoggers = Layer.scopedDiscard(Effect.locallyScoped(FiberRef.currentLoggers, HashSet.empty()))

export const PinoLoggerLive = ServiceConfig.pipe(
  Effect.map(({ nodeEnv, service, env, version }) => {
    const basePayload = {
      service,
      env,
      version,
    }

    const DEV_CONFIG = {
      level: 'trace',
      transport: {
        target: 'pino-pretty',
        colorize: true,
        singleLine: true,
        levelFirst: true,
        translateTime: 'sys:standard',
      },
      formatters: {
        level: (level: string) => ({ level }),
      },
      timestamp: false,
      base: basePayload,
    }

    const PROD_CONFIG = {
      level: 'trace',
      transport: {
        target: 'pino/file',
      },
      formatters: {
        level: (level: string) => ({ level }),
      },
      timestamp: false,
      base: basePayload,
    }

    const pinoLogger = pino(nodeEnv === 'development' ? DEV_CONFIG : PROD_CONFIG)

    const logMethod = (logLevel: LogLevel.LogLevel, payload: unknown) => {
      switch (logLevel._tag) {
        case 'Debug':
          return pinoLogger.debug(payload)
        case 'Info':
          return pinoLogger.info(payload)
        case 'Warning':
          return pinoLogger.warn(payload)
        case 'Error':
          return pinoLogger.error(payload)
        case 'Fatal':
          return pinoLogger.fatal(payload)
        case 'Trace':
          return pinoLogger.trace(payload)
        case 'All':
          return pinoLogger.info(payload)
        case 'None':
          return pinoLogger.info(payload)
      }
    }

    const logger = Logger.make(({ logLevel, message, date, fiberId, spans, annotations, cause, context }) => {
      const now = date.valueOf()

      const activeSpan = Option.flatMap(
        FiberRefs.get(context, currentContext),
        Context.getOption(Tracer.ParentSpan)
      ).pipe(Option.getOrUndefined)

      const traceIdEnd =
        activeSpan && activeSpan.traceId !== 'noop' ? activeSpan.traceId.slice(activeSpan.traceId.length / 2) : void 0

      const payload: Record<string, unknown> = {
        timestamp: now,
        level: logLevel.label,
        'logger.fiber': FiberId.threadName(fiberId),
        spans:
          List.size(spans) > 0 ? Object.fromEntries(Array.from(spans).map(x => [x.label, now - x.startTime])) : void 0,
        ...Object.fromEntries(annotations),
        dd: {
          trace_id: traceIdEnd ? BigInt(`0x${traceIdEnd}`).toString() : void 0,
          span_id: activeSpan && traceIdEnd ? BigInt(`0x${activeSpan.spanId}`).toString() : void 0,
        },
        msg: '',
      }

      const messages = Array.isArray(message) ? message : [message]

      for (const msg of messages) {
        if (typeof msg === 'object') {
          if (msg instanceof Error) {
            payload['err'] = msg
          } else {
            void Object.assign(payload, msg)
          }
        } else if (typeof msg === 'string') {
          payload['msg'] = payload['msg'] ? [payload['msg'], msg].join('|') : msg
        }
      }

      if (!Cause.isEmpty(cause)) {
        if (!payload['err']) {
          payload['err'] = Cause.squash(cause)
        } else {
          payload['cause'] = Cause.squash(cause)
        }
      }

      logMethod(logLevel, payload)
    })

    return Logger.replace(Logger.defaultLogger, logger).pipe(
      Layer.provide(LogLevelLive),
      Layer.provide(clearAllLoggers)
    )
  })
).pipe(Layer.unwrapEffect)

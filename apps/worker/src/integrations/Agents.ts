import { AgentCellOutputSchema, type AgentDataType } from '@upbound/shared/agents'
import { Effect } from 'effect'
import { z } from 'zod'
import { getTaskSummary, provideAgentContext } from '../agents/AgentContext.js'
import { generateTextEffect } from './ai-effect.js'
import { OpenAIProvider } from './ai-providers.js'

const MainAgentSystemPrompt = `
You are a helpful AI agent.
You will be given some context data and a task.

1. First, come up with a plan to accomplish the task.
  - When planning, identify any additional information you need to complete the task including the information required to call any of the available tools.
  - If you don't have enough information to call a tool, then search the web to try and find the missing information before calling the tool.

2. Call any of the available tools if you have all the data required to call them.

3. Do not hallucinate or make up information. Unless the information came from a tool or the context, do not return it as a value.

4. Once you have completed the task, return the result in the specified output format.

If you cannot find an answer, return nothing as the result with the appropriate reasoning.
`.trim()

export class Agents extends Effect.Service<Agents>()('integration/Agents', {
  effect: Effect.gen(function* () {
    const openAI = yield* OpenAIProvider

    const computeAgentCellValue = ({
      dataType,
      task,
      context,
    }: {
      customerId: string
      dataType: AgentDataType
      task: string
      context: { columnName: string; value: unknown }[]
    }) =>
      Effect.gen(function* () {
        const userPrompt = `
<context>
${context.map(({ columnName, value }) => `${columnName}: ${JSON.stringify(value)}`).join('\n')}
</context>
<task>
${task}
</task>
${(dataType.type === 'singleLineText' || dataType.type === 'multiLineText') && dataType.formatTemplate ? `<example>${dataType.formatTemplate}</example>` : ''}
    `.trim()

        yield* Effect.log(`Starting agentic computation`)

        const result = yield* generateTextEffect({
          model: openAI('gpt-5-nano', { structuredOutputs: true }),
          messages: [
            {
              role: 'system',
              content: MainAgentSystemPrompt,
            },
            {
              role: 'user',
              content: userPrompt,
            },
          ],
          toolChoice: 'required',
          maxSteps: 1,
          tools: {
            answer: {
              description: 'The answer to the task in json format.',
              parameters: z.object({
                result: AgentCellOutputSchema(dataType).nullable(),
                reasoning: z
                  .string()
                  .describe(
                    "Provide a concise reasoning that led to this result. Keep it short, concise and don't include any references to tool calls performed."
                  ),
              }),
            },
          },
        })

        const ans = result.pendingToolCalls?.find(x => x.toolName === 'answer')
        const summary = yield* getTaskSummary()

        return {
          result: ans?.args.result,
          reasoning: ans?.args.reasoning,
          steps: summary.flatMap(x => x.summaryMarkdown),
          sources: Array.from(new Set(summary.flatMap(x => x.sources))),
          integrations: Array.from(new Set(summary.flatMap(x => x.integrations ?? []))),
          meta: Object.assign({}, ...summary.flatMap(x => x.meta)),
          rawReasoning: result.reasoning,
        }
      }).pipe(provideAgentContext)

    return {
      computeAgentCellValue,
    }
  }),
}) {}

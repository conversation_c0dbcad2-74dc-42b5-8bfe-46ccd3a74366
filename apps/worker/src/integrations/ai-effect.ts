import { flattenRecord, type OmitOverUnion } from '@upbound/utils'
import {
  AISDKError,
  type CallWarning,
  type CoreMessage,
  generateText,
  InvalidToolArgumentsError,
  type LanguageModelUsage,
  type LanguageModelV1,
  type ProviderMetadata,
  type Schema,
  type Tool,
  type ToolExecutionOptions,
  TypeValidationError,
} from 'ai'
import { Cause, Effect, Metric, Predicate } from 'effect'
import { mapValues } from 'radash'
import { ZodError, type z } from 'zod'
import { AgentUnknownError, AIInvalidToolArgumentsError, AISdkAgentError } from '../computation/Errors.js'

// biome-ignore lint/suspicious/noExplicitAny: library-code
type AIParameters = z.ZodTypeAny | Schema<any>
// biome-ignore lint/suspicious/noExplicitAny: library-code
type inferParameters<PARAMETERS extends AIParameters> = PARAMETERS extends Schema<any>
  ? PARAMETERS['_type']
  : PARAMETERS extends z.ZodTypeAny
    ? z.infer<PARAMETERS>
    : never

export type ToolWithEffectExecute<
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  PARAMETERS extends AIParameters = any,
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  RESULT = any,
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  Dependencies = any,
> = OmitOverUnion<Tool<PARAMETERS, RESULT>, 'execute'> & {
  execute?: (args: inferParameters<PARAMETERS>) => Effect.Effect<RESULT, unknown, Dependencies>
}

/**
 * A helper function to create a tool that has an effectful execute function.
 */
export function toolEffect<PARAMETERS extends AIParameters, RESULT, Dependencies = never>(
  tool: ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies> & {
    execute: (
      args: inferParameters<PARAMETERS>,
      options: ToolExecutionOptions
    ) => Effect.Effect<RESULT, unknown, Dependencies>
  }
): ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies> & {
  execute: (
    args: inferParameters<PARAMETERS>,
    options: ToolExecutionOptions
  ) => Effect.Effect<RESULT, unknown, Dependencies>
}
export function toolEffect<PARAMETERS extends AIParameters, RESULT, Dependencies = never>(
  tool: ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies> & {
    execute?: undefined
  }
): ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies> & {
  execute: undefined
}
// biome-ignore lint/suspicious/noExplicitAny: library-code
export function toolEffect<PARAMETERS extends AIParameters, RESULT = any, Dependencies = never>(
  tool: ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies>
): ToolWithEffectExecute<PARAMETERS, RESULT, Dependencies> {
  return tool
}

const cachedCreationInputTokens = Metric.counter('ai_cached_creation_input_tokens', {
  incremental: true,
  description: 'The number of tokens "cached tokens" created by the model. (Anthropic only)',
})
const cacheReadInputTokenCounter = Metric.counter('ai_cached_input_tokens', {
  incremental: true,
  description: 'The number of cached input tokens read from the model.',
})
const inputTokenCounter = Metric.counter('ai_input_tokens', {
  incremental: true,
  description: 'Total number of input tokens',
})
const outputTokenCounter = Metric.counter('ai_output_tokens', {
  incremental: true,
  description: 'Total number of output tokens',
})
const reasoningTokenCounter = Metric.counter('ai_reasoning_tokens', {
  incremental: true,
  description: 'Total number of reasoning tokens',
})

/**
 * An effectful wrapper over the `generateText` function.
 */
export const generateTextEffect_ = <
  TOOLS extends Record<string, OmitOverUnion<Tool, 'execute'>>,
  OUTPUT = never,
  OUTPUT_PARTIAL = never,
>(
  params: Omit<Parameters<typeof generateText<TOOLS, OUTPUT, OUTPUT_PARTIAL>>[0], 'abortSignal' | 'maxSteps'>
) => {
  return Effect.tryPromise({
    try: abortSignal =>
      generateText({
        ...params,
        abortSignal,
        temperature: 1,
        experimental_repairToolCall: ({ toolCall, error }) =>
          new Promise((yeah, nah) => {
            if (
              InvalidToolArgumentsError.isInstance(error) &&
              TypeValidationError.isInstance(error.cause) &&
              error.cause.cause instanceof ZodError
            ) {
              const nulledProperties = Object.fromEntries(
                error.cause.cause.issues
                  .filter(x => x.code === 'invalid_type' && x.received === 'undefined')
                  .map(x => [x.path[0]!, null])
              )

              try {
                const parsedToolArgs = JSON.parse(toolCall.args)
                const updatedToolArgs = { ...parsedToolArgs, ...nulledProperties }

                return yeah({
                  ...toolCall,
                  args: JSON.stringify(updatedToolArgs),
                })
              } catch (e) {
                return nah(e)
              }
            }

            return yeah(null)
          }),
      }),
    catch: error => {
      if (AISDKError.isInstance(error)) {
        if (InvalidToolArgumentsError.isInstance(error)) {
          return AIInvalidToolArgumentsError.for(error)
        }

        return AISdkAgentError.for(error)
      } else {
        return AgentUnknownError.for(error)
      }
    },
  }).pipe(
    Effect.tap(msg => {
      const usage =
        (msg.response.body as { usage?: unknown })?.usage ??
        (msg.response.body as { usageMetadata?: unknown })?.usageMetadata

      if (!usage) {
        return Effect.logWarning(`No usage data found for model ${params.model.modelId}`)
      }

      let metricEffect = Effect.void

      switch (params.model.provider) {
        case 'google.vertex.chat':
        case 'google.generative-ai': {
          const googleUsage = usage as {
            promptTokenCount: number
            candidatesTokenCount: number
            totalTokenCount: number
            thoughtsTokenCount?: number
          }

          metricEffect = Effect.all(
            [
              inputTokenCounter(Effect.succeed(googleUsage.promptTokenCount)),
              outputTokenCounter(Effect.succeed(googleUsage.candidatesTokenCount)),
              googleUsage.thoughtsTokenCount
                ? reasoningTokenCounter(Effect.succeed(googleUsage.thoughtsTokenCount))
                : Effect.void,
            ],
            { concurrency: 'unbounded' }
          )

          break
        }
        case 'anthropic.messages': {
          const anthropicUsage = usage as {
            input_tokens: number
            output_tokens: number
            cache_creation_input_tokens: number
            cache_read_input_tokens: number
          }

          metricEffect = Effect.all(
            [
              inputTokenCounter(Effect.succeed(anthropicUsage.input_tokens)),
              outputTokenCounter(Effect.succeed(anthropicUsage.output_tokens)),
              cachedCreationInputTokens(Effect.succeed(anthropicUsage.cache_creation_input_tokens)),
              cacheReadInputTokenCounter(Effect.succeed(anthropicUsage.cache_read_input_tokens)),
            ],
            { concurrency: 'unbounded' }
          )
          break
        }
        case 'openai.chat': {
          const openaiUsage = usage as {
            prompt_tokens: number
            prompt_tokens_details: {
              cached_tokens: number
            }
            completion_tokens: number
            completion_tokens_details?: {
              reasoning_tokens?: number
            }
          }

          metricEffect = Effect.all(
            [
              inputTokenCounter(Effect.succeed(openaiUsage.prompt_tokens)),
              cacheReadInputTokenCounter(Effect.succeed(openaiUsage.prompt_tokens_details.cached_tokens)),
              outputTokenCounter(Effect.succeed(openaiUsage.completion_tokens)),
              openaiUsage?.completion_tokens_details?.reasoning_tokens
                ? reasoningTokenCounter(Effect.succeed(openaiUsage.completion_tokens_details.reasoning_tokens))
                : Effect.void,
            ],
            { concurrency: 'unbounded' }
          )

          break
        }
        default: {
          metricEffect = Effect.void
        }
      }

      const spanEffect =
        usage && typeof usage === 'object'
          ? Effect.annotateCurrentSpan({ ...flattenRecord('ai_usage', usage), ai_model: params.model.modelId })
          : Effect.annotateCurrentSpan({ ai_usage: null, ai_model: params.model.modelId })

      return Effect.all([metricEffect, spanEffect], { concurrency: 2 })
    }),
    Effect.tagMetrics('ai_model', params.model.modelId),
    Effect.tagMetrics('ai_provider', params.model.provider),
    Effect.withSpan('ai-effect/generateText')
  )
}

interface GenerateTextEffectParams<T extends Record<string, ToolWithEffectExecute>> {
  model: LanguageModelV1
  messages: CoreMessage[]
  providerOptions?: ProviderMetadata
  tools: T
  toolChoice?: 'required' | 'auto' | 'none'
  maxSteps?: number
  maxRetries?: number
}

export interface GenerateTextEffectResult<TOOLS extends Record<string, ToolWithEffectExecute>> {
  stepCount: number
  warnings: CallWarning[]
  messages: CoreMessage[]
  usage: LanguageModelUsage[]
  totalUsage: LanguageModelUsage
  toolResults?: CombinedResults<TOOLS>[]
  pendingToolCalls?: PendingToolCalls<TOOLS>[]
  reasoning?: unknown[]
}

/**
 * A helper type to extract the dependencies from a record of effectful ai tools.
 */
// biome-ignore lint/suspicious/noExplicitAny: library-code
type CombinedDependencies<DEPS extends Record<string, ToolWithEffectExecute<any, any, any>>> = {
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  [K in keyof DEPS]: DEPS[K]['execute'] extends (...args: any) => Effect.Effect<any, any, infer U> ? U : never
}[keyof DEPS]

// biome-ignore lint/suspicious/noExplicitAny: library-code
type CombinedResults<DEPS extends Record<string, ToolWithEffectExecute<any, any, any>>> = {
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  [K in keyof DEPS]: DEPS[K]['execute'] extends (...args: any) => Effect.Effect<infer U, any, any>
    ? {
        type: 'tool-result'
        toolCallId: string
        toolName: K & string
        args: inferParameters<DEPS[K]['parameters']>
        result: U
      }
    : never
}[keyof DEPS]

// biome-ignore lint/suspicious/noExplicitAny: no reason given
type PendingToolCalls<DEPS extends Record<string, ToolWithEffectExecute<any, any, any>>> = {
  // biome-ignore lint/suspicious/noExplicitAny: library-code
  [K in keyof DEPS]: DEPS[K]['execute'] extends (...args: any) => Effect.Effect<infer U, any, any>
    ? never
    : {
        type: 'tool-call'
        toolCallId: string
        toolName: K & string
        args: inferParameters<DEPS[K]['parameters']>
      }
}[keyof DEPS]

export const generateTextEffect = <TOOLS extends Record<string, ToolWithEffectExecute>>(
  params: GenerateTextEffectParams<TOOLS>
): Effect.Effect<GenerateTextEffectResult<TOOLS>, unknown, CombinedDependencies<TOOLS>> =>
  Effect.gen(function* () {
    const toolsWithoutExecute: Record<string, Tool> = mapValues(params.tools, ({ execute, ...rest }) => rest)

    const maxSteps = params.maxSteps ?? 1
    let stepCount = 0

    const messages: CoreMessage[] = params.messages.filter(Predicate.isNotUndefined)

    const usage: LanguageModelUsage[] = []
    const warnings: CallWarning[] = []
    const toolResults: CombinedResults<TOOLS>[] = []
    const pendingToolCalls: PendingToolCalls<TOOLS>[] = []

    let result: Awaited<ReturnType<typeof generateText>> | undefined

    while (stepCount < maxSteps) {
      result = yield* generateTextEffect_({
        model: params.model,
        tools: toolsWithoutExecute as Record<string, Tool>,
        messages,
        maxRetries: params.maxRetries,
        toolChoice: params.toolChoice,
        providerOptions: params.providerOptions,
      })

      messages.push(...result.response.messages)
      warnings.push(...(result.warnings ?? []))

      const toolCallsWithNoExecute = result.toolCalls.filter(call => !params.tools[call.toolName]?.execute)

      if (toolCallsWithNoExecute.length > 0) {
        pendingToolCalls.push(...(toolCallsWithNoExecute as unknown as PendingToolCalls<TOOLS>[]))
        break
      }

      if (result.finishReason === 'tool-calls') {
        const evaluateToolCalls = result.toolCalls
          .filter(call => !!params.tools[call.toolName]?.execute)
          .map(call => {
            const tool = params.tools[call.toolName]
            return Effect.zipRight(
              Effect.logDebug(`${call.toolName} | ${JSON.stringify(call.args)}`),
              tool!.execute!(call.args)
            ).pipe(
              Effect.flatMap(x =>
                Effect.isEffect(x) ? Effect.fail(`ToolCall returned an Effect : ${call.toolName}`) : Effect.succeed(x)
              ),
              Effect.tap(toolResult => Effect.logDebug(`ToolResult: ${call.toolName} | ${JSON.stringify(toolResult)}`)),
              Effect.map(toolResult => ({
                type: 'tool-result' as const,
                toolCallId: call.toolCallId,
                toolName: call.toolName,
                args: call.args,
                result: toolResult,
              })),
              Effect.tapErrorCause(error =>
                Effect.logError(
                  `ToolCallError: ${call.toolName} | ${JSON.stringify(call.args)}`,
                  Cause.pretty(error, { renderErrorCause: true })
                )
              ),
              Effect.catchAll(defect =>
                Effect.succeed({
                  type: 'tool-result' as const,
                  toolCallId: call.toolCallId,
                  toolName: call.toolName,
                  result: defect,
                  args: call.args,
                  isError: true,
                })
              )
            )
          })

        const toolResults_ = yield* Effect.all(evaluateToolCalls)
        messages.push({ role: 'tool', content: toolResults_ })
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        toolResults.push(...(toolResults_ as unknown as any))
      }

      stepCount++
    }

    return {
      warnings,
      stepCount,
      messages,
      toolResults,
      pendingToolCalls,
      usage,
      reasoning: result?.reasoningDetails,
      totalUsage: usage.reduce(
        (acc, curr) => ({
          promptTokens: acc.promptTokens + curr.promptTokens,
          completionTokens: acc.completionTokens + curr.completionTokens,
          totalTokens: acc.totalTokens + curr.totalTokens,
        }),
        { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
      ),
    }
  })

import { FetchHttpClient, HttpBody, HttpClient, HttpClientResponse } from '@effect/platform'
import { Config, Effect, Option, RateLimiter, Redacted } from 'effect'
import { HttpTransientRetryPolicy } from './RetryPolicies.js'

export class Posthog extends Effect.Service<Posthog>()('integration/Posthog', {
  scoped: Effect.gen(function* () {
    const posthogCredentials = yield* Config.zip(
      Config.string('POSTHOG_HOST'),
      Config.redacted('POSTHOG_API_KEY')
    ).pipe(
      Config.map(([host, apiKey]) => ({ host, apiKey: apiKey.pipe(Redacted.value) })),
      Config.option
    )

    yield* Effect.logInfo(
      Option.isSome(posthogCredentials) ? '🐗 ✅ Posthog credentials found' : '🐗 ❌ Posthog credentials missing'
    )

    const client = yield* HttpClient.HttpClient

    const rateLimiter = yield* RateLimiter.make({
      limit: 50,
      interval: '1 minute',
      algorithm: 'fixed-window',
    })

    const createCapture = (settings: Option.Option<{ host: string; apiKey: string }>) => {
      if (Option.isSome(settings)) {
        return (eventName: string, orgId: string, metadata: Record<string, unknown> = {}) =>
          Effect.gen(function* () {
            return yield* rateLimiter(
              client.post(`${settings.value.host}/capture/`, {
                acceptJson: true,
                body: HttpBody.unsafeJson({
                  api_key: settings.value.apiKey,
                  event: eventName,
                  // https://posthog.com/docs/product-analytics/group-analytics#advanced-server-side-only-capturing-group-events-without-a-user
                  distinct_id: 'freckle',
                  properties: {
                    $groups: { company: orgId },
                    ...metadata,
                  },
                }),
              })
            ).pipe(
              Effect.andThen(HttpClientResponse.filterStatusOk),
              Effect.retry(HttpTransientRetryPolicy),
              Effect.asVoid,
              // Any errors are logged and ignored
              Effect.catchAllCause(Effect.logError)
            )
          }).pipe(Effect.scoped, Effect.withSpan('posthog/capture'))
      } else {
        return () => Effect.void
      }
    }

    const capture = createCapture(posthogCredentials)

    const creditConsumed = (orgId: string, amount = 1, metadata?: Record<string, unknown>) =>
      capture('credit_consumed', orgId, metadata).pipe(
        Effect.repeatN(amount - 1),
        Effect.tap(() => Effect.logInfo(`Consumed ${amount} credits: ` + orgId))
      )

    return { creditConsumed } as const
  }),
  dependencies: [FetchHttpClient.layer],
}) {}

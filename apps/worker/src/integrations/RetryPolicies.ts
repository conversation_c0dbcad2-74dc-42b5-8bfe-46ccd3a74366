import type { HttpClientError } from '@effect/platform'
import { Clock, Duration, Effect, Schedule } from 'effect'
import { isDate } from 'effect/Predicate'

const parseRetryAfter = (
  defaultDuration: Duration.DurationInput,
  header?: string
): Effect.Effect<Duration.Duration> => {
  if (!header) {
    return Effect.succeed(Duration.decode(defaultDuration))
  }

  try {
    const num = Number.parseInt(header)

    if (Number.isFinite(num)) {
      return Effect.succeed(Duration.seconds(num))
    }

    const instant = new Date(header)

    if (isDate(instant)) {
      return Clock.currentTimeMillis.pipe(Effect.map(now => Duration.millis(instant.valueOf() - now)))
    }

    return Effect.succeed(Duration.decode(defaultDuration))
  } catch (e) {
    return Effect.succeed(Duration.decode(defaultDuration))
  }
}

const retry429Policy = Schedule.recurs(2).pipe(
  Schedule.checkEffect((input: HttpClientError.ResponseError) =>
    parseRetryAfter('5 seconds', input.response.headers['retry-after']).pipe(
      Effect.tap(x => Effect.logInfo(`Ratelimited. Retrying in ${x}`)),
      Effect.flatMap(x => Effect.sleep(x)),
      Effect.as(true)
    )
  ),
  Schedule.whileInput((response: HttpClientError.ResponseError) => response.response.status === 429)
)

const retry5xxPolicy = Schedule.exponential('2 seconds').pipe(
  Schedule.intersect(Schedule.recurs(3)),
  Schedule.tapInput(x => Effect.logInfo(`5xx. Retrying ...`)),
  Schedule.whileInput((response: HttpClientError.ResponseError) => response.response.status >= 500)
)

export const HttpTransientRetryPolicy = Schedule.union(retry5xxPolicy, retry429Policy).pipe(
  Schedule.whileInput(
    (response: HttpClientError.ResponseError): response is HttpClientError.ResponseError =>
      response._tag === 'ResponseError'
  ),
  Schedule.mapInput((x: HttpClientError.HttpClientError) => x as HttpClientError.ResponseError)
)

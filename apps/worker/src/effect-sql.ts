import { PgClient } from '@effect/sql-pg'
import type { PgClientConfig } from '@effect/sql-pg/PgClient'
import { Config, Redacted } from 'effect'

const postgresOptions = Config.redacted('POSTGRES_URL').pipe(
  Config.withDefault(Redacted.make('postgres://upbound:upbound@localhost:5432/superday')),
  Config.map(
    (dbString): PgClientConfig => ({
      applicationName: 'workflow-worker',
      url: dbString,
      maxConnections: 20,
      prepare: true,
      ssl: Redacted.value(dbString).endsWith('?sslmode=no-verify')
        ? ({ rejectUnauthorized: false } as unknown as boolean)
        : false,
      fetchTypes: true,
      transformJson: true,
    })
  )
)

export const PgLive = PgClient.layerConfig(postgresOptions)

import { SqlClient } from '@effect/sql'
import { flattenRecord } from '@upbound/utils'
import { Cause, type Duration, Effect, Exit, Fiber, identity, Option, Schedule, Stream } from 'effect'

type TaskMetadata = Partial<{
  afterCreatedOrUpdatedAt: string
  error: string
}>

type TaskPayload = {
  topic: 'hubspot:poll'
  payload: {
    columnId: string
  }
}

export type Task = {
  id: number
  customerId: string
  workflowTableId: number
  nextRun: Date
  lockedAt: Date | null
  metadata?: TaskMetadata
} & TaskPayload

export class DbRecurringWorkflow extends Effect.Service<DbRecurringWorkflow>()('db/DbRecurringWorkflow', {
  effect: Effect.gen(function* () {
    const sql = yield* SqlClient.SqlClient

    const fetchPendingTasks = sql<Task>`
      WITH to_update AS (
        SELECT id FROM workflow_recurring_tasks
        WHERE next_run < NOW()
        AND locked_at IS NULL
        AND is_disabled = FALSE
        ORDER BY next_run ASC
        LIMIT 10
      )
      UPDATE workflow_recurring_tasks
      SET locked_at = NOW()
      WHERE id IN (SELECT id from to_update)
      RETURNING id, customer_id "customerId", workflow_table_id "workflowTableId", next_run "nextRun", locked_at "lockedAt", topic, payload, metadata
    `.pipe(Effect.map(x => x))

    const requeue = (with10MinDelay: boolean, ...taskIds: Task['id'][]) =>
      sql`UPDATE workflow_recurring_tasks
          SET
            locked_at = NULL,
            next_run = NOW() + ${with10MinDelay ? sql`INTERVAL '10 minutes'` : sql`INTERVAL '0 seconds'`}
          WHERE
            id IN ${sql.in(taskIds)} AND locked_at IS NOT NULL`.pipe(Effect.asVoid)

    const done = (taskId: Task['id'], metadata: TaskMetadata | undefined) => {
      // biome-ignore lint/suspicious/noExplicitAny: no reason given
      const mtdt: any = { error: null, ...(metadata ?? {}) }

      return sql`UPDATE workflow_recurring_tasks
          SET
            locked_at = NULL,
            metadata = COALESCE(metadata, '{}'::jsonb) || ${mtdt}::JSONB,
            next_run = NOW() + "interval"
          WHERE
            id = ${taskId} AND locked_at IS NOT NULL`.pipe(Effect.asVoid)
    }

    const requeueStale = sql<{ id: Task['id'] }>`
        UPDATE workflow_recurring_tasks
        SET locked_at = NULL
        WHERE locked_at < NOW() - INTERVAL '5 minutes' AND locked_at IS NOT NULL
        RETURNING id`.pipe(Effect.map(identity))

    const cleanupStale = requeueStale.pipe(
      Effect.tap(x => Effect.logInfo(`RequeuedStaleRecurringTasks: ${x.length}`)),
      Effect.repeat(Schedule.spaced('5 minutes')),
      Effect.asVoid
    )

    const forEachMessage = <A extends { metadata: TaskMetadata }, E, R>(
      fn: (message: Task) => Effect.Effect<A, E, R>,
      opts: { concurrency: number; visibilityTimeout: Duration.DurationInput }
    ) => {
      return Effect.gen(function* () {
        const semaphore = yield* Effect.makeSemaphore(opts.concurrency)

        // Controlled concurrency of tasks.
        const controlledExecution = (eff: Effect.Effect<A, E, R>) => semaphore.withPermits(1)(eff)

        const evalMsg = (msg: Task) => {
          return controlledExecution(fn(msg)).pipe(
            Effect.timeout(opts.visibilityTimeout),
            Effect.uninterruptible,
            Effect.onExit(exit => {
              if (Exit.isSuccess(exit)) {
                const result = exit.pipe(Exit.getOrElse(() => void 0))
                return done(msg.id, result?.metadata).pipe(Effect.ignoreLogged)
              } else if (Exit.isInterrupted(exit)) {
                return requeue(false, msg.id).pipe(Effect.ignoreLogged)
              } else {
                const error = Exit.causeOption(exit).pipe(
                  Option.map(Cause.pretty),
                  Option.getOrElse(() => 'Reason unknown')
                )

                return Effect.zipRight(
                  Effect.logError('RecurringTaskError. Requeued to run in 10 mins', error),
                  requeue(true, msg.id)
                ).pipe(Effect.ignoreLogged)
              }
            }),
            Effect.ignoreLogged,
            Effect.annotateLogs(flattenRecord('task', msg))
          )
        }

        const slowWhenIdle = Schedule.spaced(2000).pipe(
          Schedule.whileInput((messages: readonly Task[]) => messages.length === 0)
        )

        const noDelayWhenElementsExist = Schedule.forever.pipe(
          Schedule.whileInput((messages: readonly Task[]) => messages.length > 0)
        )

        const slowAndFastSchedule = Schedule.union(slowWhenIdle, noDelayWhenElementsExist).pipe(Schedule.asVoid)

        // This task will continuously take messages from the queue and run the evaluation function.
        const taskRunner = Stream.repeatEffectWithSchedule(fetchPendingTasks, slowAndFastSchedule).pipe(
          Stream.filter(x => x.length > 0),
          Stream.mapEffect(
            messages => {
              const effx = messages.map(evalMsg)
              return Effect.all(effx, { concurrency: 'unbounded' })
            },
            { concurrency: opts.concurrency, unordered: true }
          ),
          Stream.runDrain
        )

        return yield* Fiber.joinAll([yield* taskRunner.pipe(Effect.fork), yield* cleanupStale.pipe(Effect.fork)]).pipe(
          Effect.asVoid
        )
      })
    }

    return {
      forEachMessage,
    } as const
  }),
}) {}

import { SqlClient } from '@effect/sql'
import type { SqlError } from '@effect/sql/SqlError'
import type { IntegrationCredential, IntegrationType } from '@upbound/shared/repo/IntegrationCredentialSchema.js'
import { Effect } from 'effect'

export class DbIntegrationCredential extends Effect.Service<DbIntegrationCredential>()('db/DbIntegrationCredential', {
  scoped: Effect.gen(function* () {
    const sql = yield* SqlClient.SqlClient
    const getCredential = <T extends IntegrationType>(
      customerId: string,
      credentialId: number,
      credentialType: T
    ): Effect.Effect<Extract<IntegrationCredential, { integration: T }> | null, SqlError> => {
      const statement = sql<IntegrationCredential>`
        SELECT id, integration, label, secret
        FROM integration_credentials
        WHERE customer_id = ${customerId}
        AND id = ${credentialId}
        AND integration = ${credentialType}
      `

      return statement.pipe(Effect.map(x => x[0] as Extract<IntegrationCredential, { integration: T }> | null))
    }

    return {
      getCredential,
    } as const
  }),
}) {}

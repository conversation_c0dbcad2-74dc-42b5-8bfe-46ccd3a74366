import { SqlClient, type SqlError } from '@effect/sql'
import { Cause, Context, Duration, Effect, Exit, Fiber, Layer, Option, pipe, Schedule, Stream } from 'effect'
import type { DurationInput } from 'effect/Duration'
import { none, some } from 'effect/Option'

export type EvalRow = {
  topic: 'eval:row'
  payload: {
    rowId: string
    // If "columnId" is present, only run evaluation for the defined column in the row.
    // Any upstream dependencies will also be evaluated.
    columnId?: string
  }
}

export type GenRow = {
  topic: 'gen:row'
  payload: {
    columnId: string
    size?: number
  }
}

export type UpdateRow = {
  topic: 'update:row'
  payload: {
    columnId: string
  }
}

export type Topic = EvalRow | GenRow | UpdateRow

export type QueueTake<T = Topic> = T & {
  id: string // BigInt in the DB.
  createdAt: Date
  customerId: string
  workflowTableId: number
  status: 'pending' | 'running' | 'done' | 'error'
  startedAt: Date | null
  completedAt: Date | null
  traceContext?: { b3?: string }
}

export type QueueTakeOf<T extends Topic['topic']> = QueueTake<Extract<Topic, { topic: T }>>

export type QueueEnqueue<T = Topic> = T & Pick<QueueTake<T>, 'customerId' | 'traceContext'>

export interface QueueWorkflowClient {
  addMessages: (
    params: ({
      customerId: string
      workflowTableId: number
      deduplicationId: string
      delay?: DurationInput
    } & EvalRow)[]
  ) => Effect.Effect<void, SqlError.SqlError, never>

  forEachMessage: <A, E, R>(
    fn: (message: QueueTake<Topic>) => Effect.Effect<A, E, R>,
    opts?: { concurrency?: number }
  ) => Effect.Effect<void, SqlError.SqlError | E, R>

  forEachMessageInTopic: <T extends Topic['topic'], A, E, R>(
    topic: T,
    fn: (message: QueueTakeOf<T>) => Effect.Effect<A, E, R>,
    opts?: { concurrency?: number }
  ) => Effect.Effect<void, SqlError.SqlError | E, R>

  /**
   * This background task is responsible for cleanup operations of errored or outdated messages.
   * 1. Requeue messages that are currently running and have exceeded the visibility timeout.
   * 2. Cancel outdated "pending" tasks when a customer has no credits left.
   */
  cleanupMessages: Effect.Effect<void, SqlError.SqlError, never>

  /**
   * This background task is responsible for deleting tables that have been in the trash
   * for more than 30 days.
   */
  cleanupTables: Effect.Effect<void, SqlError.SqlError, never>
}

interface QueueWorkflowClientParams {
  batchSize: number
  maxCustomerConcurrency: number
  visibilityTimeout: Duration.DurationInput
}

const make = (params: QueueWorkflowClientParams) =>
  Effect.gen(function* () {
    const { batchSize, maxCustomerConcurrency, visibilityTimeout } = params
    const sql = yield* SqlClient.SqlClient

    const addMessages = (
      params: ({
        customerId: string
        workflowTableId: number
        deduplicationId: string
        delay?: DurationInput
      } & EvalRow)[]
    ) => {
      const payload = params.map(p => {
        const intervalString = p.delay ? `${Duration.decode(p.delay).pipe(Duration.toMillis)} milliseconds` : null

        return {
          customer_id: p.customerId,
          workflow_table_id: p.workflowTableId,
          topic: p.topic,
          payload: p.payload,
          delay: intervalString ? sql`${intervalString}::INTERVAL` : null,
          deduplication_id: p.deduplicationId,
        }
      })

      if (payload.length === 0) {
        return Effect.void
      }

      return sql`INSERT INTO q_workflow ${
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        sql.insert(payload as any)
      }`.pipe(Effect.asVoid)
    }

    const take = <T extends Topic['topic']>(onlyTopic: Option.Option<T> = none()) => {
      return sql<
        QueueTake<typeof onlyTopic extends Option.None<T> ? Topic : Extract<Topic, { topic: T }>>
      >`WITH id_with_dedup_ids AS (
      -- Select the next batch of messages to process.
      SELECT deduplication_id, q.id
      FROM q_workflow q
      JOIN customer ON q.customer_id = customer.clerk_id
      WHERE q.status = 'pending'
      -- Filter by topic if provided.
      ${Option.isSome(onlyTopic) ? sql`AND q.topic = ${onlyTopic.value}` : sql``}
      AND customer.credits_available > 0
      AND process_attempt < 4
      AND (q.delay IS NULL OR q.created_at + q.delay < NOW())
      -- Filter out messages with dedup ids that are currently being processed.
      AND NOT EXISTS (
        SELECT 1
        FROM q_workflow q2
        WHERE q2.status = 'running' AND q2.customer_id = q.customer_id AND q2.deduplication_id = q.deduplication_id
        LIMIT 1
        -- By defining an offset, we essentially allow "offset + limit" number of concurrent executions
        -- per deduplication_id.
        OFFSET 0
      )
      -- If messages per customer limit is reached, skip this message.
      -- (optional and can be turned off if needed)
      AND NOT EXISTS (
        SELECT 1
        FROM q_workflow q2
        WHERE q2.status = 'running'
        AND q2.customer_id = q.customer_id
        ORDER BY id ASC
        LIMIT 1 OFFSET ${maxCustomerConcurrency} - 1
      )
      ORDER BY q.id ASC
      LIMIT ${batchSize}
    ),
    item_ids AS (
      -- Deduplicate by deduplication_id to avoid processing the same message multiple times.
      SELECT DISTINCT ON (deduplication_id) deduplication_id, id
      FROM id_with_dedup_ids
      ORDER BY 1, 2 ASC
    )
    UPDATE q_workflow
    SET
      status          = 'running',
      started_at      = NOW(),
      process_attempt = process_attempt + 1
    WHERE
        id IN (
        SELECT
          id
        FROM item_ids
      )
    AND status = 'pending'
    RETURNING id, created_at "createdAt", customer_id "customerId", workflow_table_id "workflowTableId", status, topic, payload, trace_context "traceContext";`.pipe(
        Effect.withTracerEnabled(false)
      )
    }

    // Requeue messages that are currently running.
    const requeue = (queueIds: QueueTake['id'][]) =>
      queueIds.length === 0
        ? Effect.void
        : sql`UPDATE q_workflow SET status = 'pending', process_attempt = process_attempt - 1 WHERE id IN ${sql.in(queueIds)} AND status = 'running'`.pipe(
            Effect.asVoid
          )

    // Requeue messages that are currently running and have exceeded the visibility timeout
    // This is to ensure that messages that are stuck in the "running" state are re-processed.
    const requeueStale =
      sql`UPDATE q_workflow SET status = 'pending' WHERE status = 'running' AND started_at < NOW() - CAST(${`${Duration.toMillis(visibilityTimeout)} ms`} AS INTERVAL) RETURNING id`.pipe(
        Effect.map(x => x)
      )

    const cancelTasksWhenNoCredits = sql<{ customer_id: string }>`WITH customers_with_no_credits AS (
      SELECT DISTINCT customer_id
      FROM q_workflow q
      JOIN customer ON q.customer_id = customer.clerk_id
      WHERE q.status = 'pending'
      AND customer.credits_available <= 0
      AND q.created_at <= now() - INTERVAL '23 hours'
      LIMIT 1
    )
    DELETE FROM q_workflow
    WHERE customer_id IN (SELECT customer_id FROM customers_with_no_credits)
    AND status = 'pending'
    RETURNING customer_id;
    `.pipe(Effect.map(x => ({ numEvents: x.length, customerIds: Array.from(new Set(x.map(x => x.customer_id))) })))

    const deleteTrashedTables = sql`DELETE FROM workflow_table 
          WHERE deleted_at IS NOT NULL 
          AND NOW() > deleted_at + INTERVAL '30 days'
          RETURNING id`.pipe(Effect.map(x => ({ deletedCount: x.length })))

    const doneOrError = (queueId: QueueTake['id'], status: 'done' | 'error', metadata?: unknown) =>
      sql`UPDATE q_workflow SET
        status = ${status},
        metadata = ${
          // biome-ignore lint/suspicious/noExplicitAny: no reason given
          metadata ? (metadata as any) : null
        },
        completed_at = NOW()
      WHERE id = ${queueId} AND status = 'running'`.pipe(Effect.asVoid)

    const forEachMessage = <A, E, R>(
      fn: (message: QueueTake<Topic>) => Effect.Effect<A, E, R>,
      opts?: { concurrency?: number }
    ) => {
      return Effect.gen(function* () {
        const semaphore = yield* Effect.makeSemaphore(opts?.concurrency ?? batchSize)

        // Controlled concurrency of tasks.
        const controlledExecution = (eff: Effect.Effect<A, E, R>) => semaphore.withPermits(1)(eff)

        const evalMsg = (msg: QueueTake<Topic>) =>
          Effect.acquireUseRelease(
            Effect.succeed(msg),
            qt => controlledExecution(fn(qt)),
            (msg, exit) => {
              if (Exit.isSuccess(exit)) {
                const result = pipe(
                  exit,
                  Exit.map(data => (data ? { data } : void 0)),
                  Exit.getOrElse(() => void 0)
                )

                return doneOrError(msg.id, 'done', result).pipe(Effect.ignoreLogged)
              } else if (Exit.isInterrupted(exit)) {
                return Effect.void
              } else {
                const error = pipe(
                  Exit.causeOption(exit),
                  Option.map(Cause.pretty),
                  Option.getOrElse(() => 'Reason unknown')
                )

                return doneOrError(msg.id, 'error', { error }).pipe(Effect.ignoreLogged)
              }
            }
          )

        const slowWhenIdle = Schedule.spaced(2000).pipe(
          Schedule.whileInput((messages: readonly QueueTake<Topic>[]) => messages.length === 0)
        )

        const noDelayWhenElementsExist = Schedule.forever.pipe(
          Schedule.whileInput((messages: readonly QueueTake<Topic>[]) => messages.length > 0)
        )

        const slowAndFastSchedule = Schedule.union(slowWhenIdle, noDelayWhenElementsExist).pipe(Schedule.asVoid)

        // This task will continuously take messages from the queue and run the evaluation function.
        const taskRunner = yield* Stream.repeatEffectWithSchedule(take(), slowAndFastSchedule).pipe(
          Stream.filter(x => x.length > 0),
          Stream.mapEffect(
            messages =>
              Effect.acquireUseRelease(
                Effect.succeed(messages),
                msgs => {
                  const effx = msgs.map(evalMsg)
                  return Effect.all(effx, { concurrency: 'unbounded' })
                },
                msgs => requeue(msgs.map(message => message.id)).pipe(Effect.ignoreLogged)
              ),
            { concurrency: batchSize * 2, unordered: true }
          ),
          Stream.runDrain,
          Effect.fork
        )

        return taskRunner
      })
    }

    const forEachMessageInTopic = <T extends Topic['topic'], A, E, R>(
      topic: T,
      fn: (message: QueueTake<Extract<Topic, { topic: T }>>) => Effect.Effect<A, E, R>,
      opts?: { concurrency?: number }
    ) => {
      return Effect.gen(function* () {
        const semaphore = yield* Effect.makeSemaphore(opts?.concurrency ?? batchSize)

        // Controlled concurrency of tasks.
        const controlledExecution = (eff: Effect.Effect<A, E, R>) => semaphore.withPermits(1)(eff)

        const evalMsg = (msg: QueueTakeOf<T>) =>
          Effect.acquireUseRelease(
            Effect.succeed(msg),
            qt => controlledExecution(fn(qt)),
            (msg, exit) => {
              if (Exit.isSuccess(exit)) {
                const result = pipe(
                  exit,
                  Exit.map(data => (data ? { data } : void 0)),
                  Exit.getOrElse(() => void 0)
                )

                return doneOrError(msg.id, 'done', result).pipe(Effect.ignoreLogged)
              } else if (Exit.isInterrupted(exit)) {
                return Effect.void
              } else {
                const error = pipe(
                  Exit.causeOption(exit),
                  Option.map(Cause.pretty),
                  Option.getOrElse(() => 'Reason unknown')
                )

                return doneOrError(msg.id, 'error', { error }).pipe(Effect.ignoreLogged)
              }
            }
          )

        const slowWhenIdle = Schedule.spaced(2000).pipe(
          Schedule.whileInput((messages: readonly QueueTakeOf<T>[]) => messages.length === 0)
        )

        const noDelayWhenElementsExist = Schedule.forever.pipe(
          Schedule.whileInput((messages: readonly QueueTakeOf<T>[]) => messages.length > 0)
        )

        const slowAndFastSchedule = Schedule.union(slowWhenIdle, noDelayWhenElementsExist).pipe(Schedule.asVoid)

        // This task will continuously take messages from the queue and run the evaluation function.
        const taskRunner = yield* Stream.repeatEffectWithSchedule(take(some(topic)), slowAndFastSchedule).pipe(
          Stream.filter(x => x.length > 0),
          Stream.mapEffect(
            messages =>
              Effect.acquireUseRelease(
                Effect.succeed(messages),
                msgs => {
                  const effx = msgs.map(x =>
                    evalMsg(x).pipe(
                      Effect.matchCauseEffect({
                        onFailure: cause => Effect.logError(Cause.pretty(cause, { renderErrorCause: true })),
                        onSuccess: () => Effect.void,
                      })
                    )
                  )
                  return Effect.all(effx, { concurrency: 'unbounded' }).pipe(Effect.asVoid)
                },
                msgs => requeue(msgs.map(message => message.id)).pipe(Effect.ignoreLogged)
              ),
            { concurrency: batchSize * 2, unordered: true }
          ),
          Stream.runDrain
        )

        return taskRunner
      })
    }

    const cleanupMessages = Effect.gen(function* () {
      // This task will requeue messages that are currently running and have exceeded the visibility timeout
      const cleanupStale = yield* requeueStale.pipe(
        Effect.tap(x => Effect.logInfo(`RequeuedStale: ${x.length}`)),
        Effect.repeat(Schedule.spaced('5 minutes')),
        Effect.asVoid,
        Effect.fork
      )

      // This task will cancel outdated "pending" tasks when a customer has no credits left.
      const cleanupNoCredits = yield* cancelTasksWhenNoCredits.pipe(
        Effect.tap(x => Effect.logInfo(`CanceledTasksWhenNoCredits: ${x.numEvents}`, x)),
        Effect.repeat(Schedule.spaced('5 minutes')),
        Effect.asVoid,
        Effect.fork
      )

      const cleanupProgram = yield* Fiber.joinAll([cleanupStale, cleanupNoCredits]).pipe(Effect.asVoid)

      return cleanupProgram
    })

    const cleanupTables = Effect.gen(function* () {
      // This task will delete tables that have been in the trash for more than 30 days
      const cleanupTrashedTables = yield* deleteTrashedTables.pipe(
        Effect.tap(x => Effect.logInfo(`DeletedTrashedTables: ${x.deletedCount}`)),
        Effect.repeat(Schedule.spaced('10 minutes')),
        Effect.asVoid,
        Effect.fork
      )

      const cleanupProgram = yield* Fiber.join(cleanupTrashedTables).pipe(Effect.asVoid)

      return cleanupProgram
    })

    return {
      addMessages,
      forEachMessage,
      forEachMessageInTopic,
      cleanupMessages,
      cleanupTables,
    } as const
  })

export class QueueWorkflow extends Context.Tag('QueueWorkflow')<QueueWorkflow, QueueWorkflowClient>() {}

export const QueueWorkflowLayer = (params: QueueWorkflowClientParams) => Layer.effect(QueueWorkflow, make(params))

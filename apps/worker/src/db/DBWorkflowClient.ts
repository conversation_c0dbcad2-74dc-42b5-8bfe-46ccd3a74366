import { SqlClient } from '@effect/sql'
import type { WorkflowTable, WorkflowTableRow } from '@upbound/shared'
import type { ColumnDefinition, ColumnId } from '@upbound/shared/workflow'
import { Effect, identity } from 'effect'

export class DbWorkflowClient extends Effect.Service<DbWorkflowClient>()('db/DbWorkflowClient', {
  scoped: Effect.gen(function* () {
    const sql = yield* SqlClient.SqlClient

    const WorkflowTableFields = sql`
  id "workflowTableId",
  customer_id "customerId",
  name,
  table_definition "tableDefinition"`

    const WorkflowTableRowFields = sql`
  workflow_table_id "workflowTableId",
  row_id "rowId",
  customer_id "customerId",
  row_value "rowValue",
  created_at "createdAt",
  updated_at "updatedAt"`

    const getWorkflowTable = (params: { customerId: string; workflowTableId: number }) => {
      return sql<WorkflowTable>`
      SELECT ${WorkflowTableFields}
      FROM workflow_table
      WHERE id = ${params.workflowTableId}
      AND customer_id = ${params.customerId}`.pipe(Effect.map(x => x[0] ?? null))
    }

    const getWorkflowTableWithAutorunColumns = Effect.fn(function* (params: {
      customerId: string
      workflowTableId: number
    }) {
      const workflowTable = yield* getWorkflowTable(params)

      if (!workflowTable) {
        return {
          workflowTable: null,
        } as const
      }

      const colsToAutoRun = Object.values(workflowTable.tableDefinition.columnDefinitions)
        .filter(x => x.columnVariant === 'computed' && x.autoRun)
        .map(x => x.columnId)

      if (colsToAutoRun.length === 0) {
        return {
          workflowTable,
          autorunColumns: [],
        }
      }

      return {
        workflowTable,
      }
    })

    const getColumnsForTable = (params: { customerId: string; workflowTableId: number }) =>
      getWorkflowTable(params).pipe(Effect.map(x => x?.tableDefinition.columnDefinitions ?? null))

    const getRowsByIdsForCustomer = (params: { customerId: string; workflowTableId: number; rowIds?: string[] }) => {
      const statement = sql<WorkflowTableRow>`
    SELECT ${WorkflowTableRowFields}
    FROM workflow_table_row
    WHERE ${sql.and([
      sql`customer_id = ${params.customerId}`,
      sql`workflow_table_id = ${params.workflowTableId}`,
      params.rowIds ? sql.in('row_id', params.rowIds) : sql``,
    ])}
    ORDER BY row_id ASC
    `

      return statement.pipe(Effect.map(identity))
    }

    const setRowCellValue = <T = unknown>(params: {
      customerId: string
      workflowTableId: number
      rowId: string
      columnId: ColumnId
      value: T
      whereDigest?: { inputDigest: string; columnDigest: string; allowNull?: boolean }
    }) => {
      const digest = params.whereDigest

      const digestFragment = digest
        ? sql.or([
            sql`row_value->'data'->${params.columnId}->>'columnDigest' = ${digest.columnDigest} AND row_value->'data'->${params.columnId}->>'inputDigest' = ${digest.inputDigest}`,
            sql`row_value->'data'->${params.columnId}->>'status' = 'queued'`,
            digest.allowNull ? sql`row_value->'data'->>${params.columnId} IS NULL` : sql`1=2`,
          ])
        : sql`1=1`

      // biome-ignore lint/suspicious/noExplicitAny: The SQL statement actually does take a `JSON` struct, but the type is not defined correctly here.
      const paramsValue = params.value as any

      const statement = sql<WorkflowTableRow>`
    UPDATE workflow_table_row
    SET row_value = JSONB_SET(row_value, ARRAY['data', ${params.columnId}::TEXT], ${paramsValue})
    WHERE customer_id = ${params.customerId}
    AND workflow_table_id = ${params.workflowTableId}
    AND row_id = ${params.rowId}
    AND ${digestFragment}
    RETURNING ${WorkflowTableRowFields}`

      return statement.pipe(Effect.map(res => res[0] ?? null))
    }

    const addRows = <T = unknown>(params: {
      customerId: string
      workflowTableId: number
      rows: { rowId: string; value: T }[]
    }) => {
      // biome-ignore lint/suspicious/noExplicitAny: no reason given
      const payload: any = params.rows.map(row => ({
        customer_id: params.customerId,
        workflow_table_id: params.workflowTableId,
        row_id: row.rowId,
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        row_value: { data: row.value as any },
      }))

      const statement = sql<WorkflowTableRow>`
      INSERT INTO workflow_table_row ${sql.insert(payload)}
      RETURNING ${WorkflowTableRowFields}`

      return statement.pipe(Effect.map(res => res[0] ?? null))
    }

    // Use with caution. This can potentially fetch a lot of data and use up available memory.
    const getColumnValueFromAllRows = <T = unknown>(params: {
      customerId: string
      workflowTableId: number
      columnId: string
    }) => {
      const statement = sql<{ value: T; rowId: string }>`
        SELECT row_id as "rowId", row_value->'data'->${params.columnId} "value"
        FROM workflow_table_row
        WHERE customer_id = ${params.customerId}
        AND workflow_table_id = ${params.workflowTableId}
        ORDER BY row_id ASC
      `

      return statement.pipe(Effect.map(x => x))
    }

    const upsertColumnDefinition = (params: {
      customerId: string
      workflowTableId: number
      newColumnDefinition: ColumnDefinition
      currentColumnDefinition: ColumnDefinition
    }) => {
      const statement = sql<WorkflowTable>`
        UPDATE workflow_table
        SET table_definition = JSONB_SET(table_definition, ARRAY['columnDefinitions', ${params.newColumnDefinition.columnId}::TEXT], ${
          // biome-ignore lint/suspicious/noExplicitAny: no reason given
          params.newColumnDefinition as any
        })
        WHERE id = ${params.workflowTableId} AND customer_id = ${params.customerId}
        AND table_definition->'columnDefinitions'->${params.currentColumnDefinition.columnId} = ${
          // biome-ignore lint/suspicious/noExplicitAny: no reason given
          params.currentColumnDefinition as any
        }
        RETURNING ${WorkflowTableFields}
        WHERE customer_id = ${params.customerId}
      `

      return statement.pipe(Effect.map(res => res[0] ?? null))
    }

    return {
      getWorkflowTable,
      getWorkflowTableWithAutorunColumns,
      getColumnsForTable,
      getRowsByIdsForCustomer,
      setRowCellValue,
      getColumnValueFromAllRows,
      addRows,
      upsertColumnDefinition,
    } as const
  }),
}) {}

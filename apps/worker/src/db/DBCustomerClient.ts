import { SqlClient } from '@effect/sql'
import { Effect, Metric } from 'effect'
import { Posthog } from '../integrations/Posthog.js'

const CreditsConsumedCounter = Metric.counter('credits_consumed', {
  description: 'The number of credits consumed',
  incremental: true,
}).pipe(Metric.withConstantInput(1))

export class DbCustomerClient extends Effect.Service<DbCustomerClient>()('db/DbCustomerClient', {
  scoped: Effect.gen(function* () {
    const sql = yield* SqlClient.SqlClient
    const posthog = yield* Posthog

    const updateAvailableCredits = (customerId: string, credits: number) => {
      const statement = sql<{ creditsAvailable: number }>`
        UPDATE "customer"
        SET credits_available = credits_available + ${credits}
        WHERE clerk_id = ${customerId}
        RETURNING credits_available "creditsAvailable"
      `

      return Effect.suspend(() => statement).pipe(Effect.map(x => x[0]?.creditsAvailable ?? null))
    }

    const getAvailableCredits = (customerId: string) => {
      const statement = sql<{ creditsAvailable: number }>`
        SELECT credits_available "creditsAvailable" FROM "customer" WHERE clerk_id = ${customerId}
      `

      return statement.pipe(Effect.map(x => x[0]?.creditsAvailable ?? null))
    }

    const consumeCredit = (customerId: string, amount = -1) =>
      CreditsConsumedCounter(updateAvailableCredits(customerId, amount)).pipe(
        Effect.tap(() => posthog.creditConsumed(customerId, amount * -1))
      )

    return {
      getAvailableCredits,
      consumeCredit,
    } as const
  }),
}) {}

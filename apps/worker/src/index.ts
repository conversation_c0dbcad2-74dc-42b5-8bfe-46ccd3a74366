import { NodeCommandExecutor, NodeFileSystem, NodePath, NodeRuntime } from '@effect/platform-node'
import { RedisClient } from '@upbound/redis/effect'
import { Layer } from 'effect'
import { OtelExporterLive } from './effect-apm/OTELTracing.js'
import { PinoLoggerLive } from './effect-apm/PinoLogger.js'
import { PgLive } from './effect-sql.js'
import { HttpServerLive } from './HttpServerProgram.js'
import { Posthog } from './integrations/Posthog.js'
import { WorkflowWorkerProgram } from './WorkflowWorkerProgram.js'

// Combine the layers
const AppLive = Layer.merge(HttpServerLive, WorkflowWorkerProgram).pipe(
  Layer.provide(RedisClient.Default),
  Layer.provide(Posthog.Default),
  Layer.provide(PgLive),
  Layer.provide(NodePath.layer),
  Layer.provide(NodeCommandExecutor.layer),
  Layer.provide(NodeFileSystem.layer),
  Layer.provide(OtelExporterLive),
  Layer.provide(PinoLoggerLive)
)

// Run the program
NodeRuntime.runMain(Layer.launch(AppLive))

// Don't delete this line.
// For some reason, effect only seems to get these shutdown signals
// when we manually add a listener for these events.
for (const signal of ['SIGINT', 'SIGTERM']) {
  process.on(signal, recdSignal => console.log(`Received ${recdSignal}`))
}

process.title = 'upbound-worker'

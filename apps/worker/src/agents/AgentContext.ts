/**
 * The AgentContext is a service that holds runtime context for an multi-agent
 * chain as it progresses through various stages of execution. The context can store
 * any additional information from each step as required and exposed to the user when relevant.
 */
import { Context, Effect, Option, Ref } from 'effect'

export class AgentContext extends Context.Tag('AgentContext')<
  AgentContext,
  Ref.Ref<{
    task: { summaryMarkdown: string; sources: string[]; integrations?: string[]; meta?: Record<string, unknown> }[]
  }>
>() {}

export const provideAgentContext = Effect.provideServiceEffect(
  AgentContext,
  Ref.make({
    task: [] as {
      summaryMarkdown: string
      sources: string[]
      integrations?: string[]
      meta?: Record<string, unknown>
    }[],
  })
)

export const addTaskSummaryMarkdown = (
  markdown: string,
  sources?: string[],
  integrations?: string[],
  meta?: Record<string, unknown>
) => {
  return Effect.serviceOption(AgentContext).pipe(
    Effect.flatMap(maybeCtx => {
      return Option.match(maybeCtx, {
        onNone: () => Effect.void,
        onSome: ref =>
          Ref.update(ref, ctx => ({
            ...ctx,
            task: [
              ...ctx.task,
              { summaryMarkdown: markdown, sources: sources ?? [], integrations: integrations ?? [], meta },
            ],
          })),
      })
    })
  )
}

export const getTaskSummary = () => {
  return AgentContext.pipe(
    Effect.flatMap(ctx => ctx.get),
    Effect.map(x => x.task)
  )
}

import { NodeHttpClient } from '@effect/platform-node'
import type { WorkflowTableRow } from '@upbound/shared'
import {
  buildDepGraph,
  type CellValueComputed,
  computeDigestForCell,
  type Digests,
  evalCellFormula,
  isComputedColumn,
  transformWorkflowTableRowToRowData,
} from '@upbound/shared/workflow'
import { flattenRecord } from '@upbound/utils/Record'
import { Cause, Duration, Effect, Fiber, Layer, Match } from 'effect'
import type { DurationInput } from 'effect/Duration'
import { EvaluateAgentCell } from './computation/Agent.js'
import { DigestsChanged } from './computation/Errors.js'
import type { SetComputedCellValueFailFast } from './computation/Utils.js'
import { DbCustomerClient } from './db/DBCustomerClient.js'
import { DbIntegrationCredential } from './db/DBIntegrationCredential.js'
import { DbWorkflowClient } from './db/DBWorkflowClient.js'
import { DbRecurringWorkflow } from './db/DbRecurringWorkflow.js'
import { type EvalRow, type QueueTake, QueueWorkflow, QueueWorkflowLayer } from './db/QueueWorkflow.js'

const VISIBILITY_TIMEOUT: DurationInput = '1 minute'

const RunEvalRow = (message: QueueTake<EvalRow>) =>
  Effect.gen(function* () {
    const dbWorkflow = yield* DbWorkflowClient
    const dbCustomer = yield* DbCustomerClient

    const columnMap = yield* dbWorkflow.getColumnsForTable({
      customerId: message.customerId,
      workflowTableId: message.workflowTableId,
    })

    if (!columnMap) {
      return { error: 'ColumnsNotFound' }
    }

    if (message.payload.columnId && !columnMap[message.payload.columnId]) {
      return { error: 'ColumnNotFound', detail: message.payload.columnId }
    }

    const columns = Object.values(columnMap)
    const depGraph = buildDepGraph(columns)

    if (!depGraph.isValid) {
      return { error: 'InvalidDepGraph', detail: depGraph.graphErrors }
    }

    const colsToRun = (
      message.payload.columnId
        ? [...depGraph.graph.dependenciesOf(message.payload.columnId), message.payload.columnId]
        : depGraph.graph.overallOrder()
    )
      .map(columnId => columnMap[columnId]!)
      .filter(isComputedColumn)

    const [row] = yield* dbWorkflow.getRowsByIdsForCustomer({
      customerId: message.customerId,
      workflowTableId: message.workflowTableId,
      rowIds: [message.payload.rowId],
    })

    if (!row) {
      return { error: 'RowNotFound' }
    }

    let workflowTableRow: WorkflowTableRow = row

    for (const columnDef of colsToRun) {
      const rowData = transformWorkflowTableRowToRowData(workflowTableRow)

      const columnId = columnDef.columnId

      const inputDigestForCell = yield* Effect.promise(() =>
        computeDigestForCell({ columnId, row: rowData, columnMap })
      )

      // If the input digest for cell is null, it means that this cell is not yet ready to be computed.
      // and should be skipped.
      if (columnDef.type !== 'agent' && inputDigestForCell === null) {
        continue
      }

      const cellData = rowData[columnId] as CellValueComputed | null | undefined

      // If the data in the cell is already up-to-date, we can skip this computation.
      if (
        (cellData?.status === 'success' &&
          inputDigestForCell === cellData?.inputDigest &&
          columnDef.columnDigest === cellData?.columnDigest) ||
        cellData?.status === 'user'
      ) {
        continue
      }

      // These are the new digests of the input cells and the column.
      const digests: Digests = {
        inputDigest: inputDigestForCell ?? 'agent',
        columnDigest: columnDef.columnDigest,
      }

      const setComputedCellValue: SetComputedCellValueFailFast<typeof columnDef.type> = (
        value,
        { allowNullDigest, valueDigest = digests, whereDigest = digests } = {}
      ) =>
        dbWorkflow
          .setRowCellValue<typeof value>({
            customerId: row.customerId,
            workflowTableId: row.workflowTableId,
            rowId: row.rowId,
            columnId: columnDef.columnId,
            value: { ...value, ...valueDigest },
            whereDigest: { ...whereDigest, allowNull: allowNullDigest },
          })
          .pipe(
            Effect.flatMap(x => {
              if (x) {
                return Effect.succeed(x)
              } else {
                return DigestsChanged.for(`${columnDef.type}:${value.status}`, columnDef)
              }
            })
          )

      /**
       * RunIf condition check!
       */
      if (columnDef.runIf) {
        const runIfResult = evalCellFormula(columnDef.runIf.formula, columnMap, rowData)

        if (!runIfResult) {
          const latestRow = yield* setComputedCellValue(
            { status: 'runConditionNotMet' },
            {
              whereDigest: { inputDigest: cellData?.inputDigest ?? '', columnDigest: cellData?.columnDigest ?? '' },
              allowNullDigest: true,
            }
          )

          workflowTableRow = latestRow
          continue
        }
      }

      const latestRow = yield* setComputedCellValue(
        { status: 'running' },
        {
          whereDigest: { inputDigest: cellData?.inputDigest ?? '', columnDigest: cellData?.columnDigest ?? '' },
          allowNullDigest: true,
        }
      )

      let usedIntegrations: string[] = []

      const legacyCellEval = setComputedCellValue({
        status: 'error' as const,
        message: 'Please upgrade to agent cells.',
      }).pipe(Effect.map(updatedRow => ({ updatedRow, chargeCredit: false })))

      const computation = Match.value(columnDef)
        .pipe(
          Match.when({ type: 'agent' }, () =>
            EvaluateAgentCell(
              message.customerId,
              columnId,
              columnMap,
              latestRow,
              setComputedCellValue as SetComputedCellValueFailFast<'agent'>
            ).pipe(
              Effect.timeout(VISIBILITY_TIMEOUT),
              Effect.withSpan('EvaluateAgentCell'),
              Effect.tap(x => {
                usedIntegrations = x.integrationsUsed
                return Effect.void
              })
            )
          ),
          Match.exhaustive
        )
        .pipe(
          // Effect.catchTag('OpenAIError', error => {
          //   return Effect.zipRight(
          //     setComputedCellValue({ status: 'error', message: 'Error. Please try again later.' }),
          //     Effect.fail(error)
          //   )
          // }),
          // Effect.catchTag('TimeoutException', error => {
          //   return Effect.zipRight(setComputedCellValue({ status: 'error', message: 'Timed out' }), Effect.fail(error))
          // }),
          Effect.catchAllCause(cause => {
            return Effect.logError(Cause.pretty(cause, { renderErrorCause: true })).pipe(
              Effect.flatMap(() =>
                setComputedCellValue({ status: 'error', message: 'Error. Please try again later.' })
              ),
              Effect.flatMap(() => Effect.failCause(cause))
            )
          }),
          Effect.annotateSpans(
            flattenRecord('cell', {
              rowId: message.payload.rowId,
              column: {
                columnId,
                type: columnDef.type,
                name: columnDef.columnName,
              },
            })
          ),
          Effect.annotateLogs({
            customerId: message.customerId,
            workflowTableId: message.workflowTableId,
            rowId: message.payload.rowId,
            column: { columnId, type: columnDef.type, name: columnDef.columnName },
          })
        )

      const { updatedRow } = yield* computation

      workflowTableRow = updatedRow
    }

    return 'Success'
  }).pipe(
    Effect.annotateSpans(
      flattenRecord('customer', {
        customerId: message.customerId,
        workflowTableId: message.workflowTableId,
      })
    ),
    Effect.tagMetrics({ customerId: message.customerId, workflowTableId: message.workflowTableId.toString() })
  )

const WorkflowWorkerLive = Layer.scopedDiscard(
  Effect.gen(function* () {
    const queueWorkflow = yield* QueueWorkflow

    const cleanupProgram = queueWorkflow.cleanupMessages
    const cleanupTablesProgram = queueWorkflow.cleanupTables

    const evalRowProcessProgram = queueWorkflow.forEachMessageInTopic(
      'eval:row',
      message => {
        return RunEvalRow(message).pipe(
          // Effect.catchTag('DigestsChanged', error => Effect.succeed({ code: 'DigestsChanged', reason: error.reason })),
          Effect.catchAllCause(error => Effect.succeed({ error })),
          Effect.tapDefect(error => Effect.logError(Cause.pretty(error))),
          // The maximum time window per message.
          // We will need to halt message processing if it exceeds this time window
          // and mark the message that is being processed as timed out.
          Effect.timeout(VISIBILITY_TIMEOUT)
        )
      },
      { concurrency: 50 }
    )

    return yield* Fiber.joinAll([
      yield* cleanupProgram.pipe(Effect.fork),
      yield* cleanupTablesProgram.pipe(Effect.fork),
      yield* evalRowProcessProgram.pipe(Effect.fork),
    ])
  })
)

export const WorkflowWorkerProgram = WorkflowWorkerLive.pipe(
  Layer.provide(NodeHttpClient.layer),
  Layer.provide(
    QueueWorkflowLayer({
      batchSize: 10,
      maxCustomerConcurrency: 10,
      visibilityTimeout: Duration.decode(VISIBILITY_TIMEOUT).pipe(Duration.sum('5 seconds')),
    })
  ),
  Layer.provide(DbRecurringWorkflow.Default),
  Layer.provide(DbWorkflowClient.Default),
  Layer.provide(DbCustomerClient.Default),
  Layer.provide(DbIntegrationCredential.Default)
)

import type { WorkflowTableRow } from '@upbound/shared'
import {
  type CellValueOf,
  type ColumnDefinition,
  type ColumnDefinitionOf,
  type ColumnId,
  getValueOfCell,
  transformWorkflowTableRowToRowData,
} from '@upbound/shared/workflow'
import { isNotNullOrUndefined } from '@upbound/utils'
import { Effect } from 'effect'
import { Agents } from '../integrations/Agents.js'
import { NoResults } from './Errors.js'
import type { SetComputedCellValueFailFast } from './Utils.js'

type ThisColumnType = 'agent'
type ThisColumnDefinition = ColumnDefinitionOf<ThisColumnType>
type ThisCellDefinition = CellValueOf<ThisColumnType>

export const EvaluateAgentCell = (
  customerId: string,
  columnId: ColumnId,
  columnMap: Record<ColumnId, ColumnDefinition>,
  row: WorkflowTableRow,
  setRowCellValue: SetComputedCellValueFailFast<ThisColumnType>
) => {
  return Effect.gen(function* () {
    const columnDefinition = columnMap[columnId] as ThisColumnDefinition
    const Agents_ = yield* Agents

    if (columnDefinition.config.dataType.type === 'unconfigured') {
      return yield* Effect.fail(new Error('Unconfigured agent column detected. Please eval the column before running.'))
    }

    const agentContext = Object.values(columnMap)
      .filter(x => x.columnId !== columnId)
      .map(x => ({
        columnName: x.columnName,
        value:
          columnMap[x.columnId]?.type === 'agent'
            ? (
                getValueOfCell({
                  row: transformWorkflowTableRowToRowData(row),
                  columnDef: columnMap[x.columnId]!,
                  columnMap,
                }) as Extract<CellValueOf<'agent'>, { status: 'success' }>
              )?.value
            : getValueOfCell({
                row: transformWorkflowTableRowToRowData(row),
                columnDef: columnMap[x.columnId]!,
                columnMap,
              }),
      }))
      .filter(x => isNotNullOrUndefined(x.value) && x.value !== '')

    // If the agent context is empty, there's really nothing to run and
    // we should just exit immediately.
    if (agentContext.length === 0) {
      return {
        value: null,
        updatedRow: yield* setRowCellValue({ status: 'user', value: '' }),
        integrationsUsed: [] as string[],
      }
    }

    const agentCellEvaluationStream = Agents_.computeAgentCellValue({
      customerId,
      dataType: columnDefinition.config.dataType,
      task: columnDefinition.config.agentTask,
      context: agentContext,
    }).pipe(
      Effect.flatMap(event => {
        return setRowCellValue({
          status: 'success',
          value: event.result ?? '',
          reasoning: event.reasoning ?? '',
          steps: event.steps ?? [],
          sources: event.sources ?? [],
          meta: event.meta,
        }).pipe(Effect.map(x => ({ updatedRow: x, value: event.result ?? '', integrationsUsed: event.integrations })))
      })
    )

    const updatedRowAndValue = yield* agentCellEvaluationStream

    if (!updatedRowAndValue.updatedRow) {
      return yield* NoResults.for(`AI stream yielded no results`, columnDefinition)
    }

    return updatedRowAndValue
  }).pipe(
    Effect.map(({ updatedRow, value, integrationsUsed }) => ({
      updatedRow,
      chargeCredit: isNotNullOrUndefined(value) && value !== '',
      value,
      integrationsUsed: integrationsUsed ?? void 0,
    })),
    Effect.tapErrorCause(() => {
      return setRowCellValue({
        status: 'error' as const,
        message: `Agent Error. Please try again later.`,
      })
    }),
    Effect.provide(Agents.Default)
  )
}

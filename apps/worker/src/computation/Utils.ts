import type { SqlError } from '@effect/sql'
import type { WorkflowTableRow } from '@upbound/shared'
import type { CellValueOf, ColumnDefinitionComputed, Digests } from '@upbound/shared/workflow'
import type { OmitOverUnion } from '@upbound/utils'
import type { Effect } from 'effect'
import type { DigestsChanged } from './Errors.js'

export type SetComputedCellValue<T extends ColumnDefinitionComputed['type']> = (
  value: OmitOverUnion<CellValueOf<T>, keyof Digests>,
  params?: {
    valueDigest?: Digests
    whereDigest?: Digests
    allowNullDigest?: boolean
  }
) => Effect.Effect<WorkflowTableRow | null, SqlError.SqlError>

export type SetComputedCellValueFailFast<T extends ColumnDefinitionComputed['type']> = (
  value: OmitOverUnion<CellValueOf<T>, keyof Digests>,
  params?: {
    valueDigest?: Digests
    whereDigest?: Digests
    allowNullDigest?: boolean
  }
) => Effect.Effect<WorkflowTableRow, SqlError.SqlError | DigestsChanged>

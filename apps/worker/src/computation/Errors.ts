import type { ColumnDefinition } from '@upbound/shared/workflow'
import type { AISDKError, InvalidToolArgumentsError } from 'ai'
import { Data } from 'effect'

export class DigestsChanged extends Data.TaggedError('DigestsChanged')<{
  columnId?: string
  columnName?: string
  reason: string
}> {
  static for(reason: string, columnDef: ColumnDefinition) {
    return new DigestsChanged({
      reason,
      columnId: columnDef.columnId,
      columnName: columnDef.columnName,
    })
  }
}

export class NoResults extends Data.TaggedError('NoResults')<{
  columnId?: string
  columnName?: string
  reason: string
}> {
  static for(reason: string, columnDef: ColumnDefinition) {
    return new NoResults({
      reason,
      columnId: columnDef.columnId,
      columnName: columnDef.columnName,
    })
  }
}

export class InsufficientCredits extends Data.TaggedError('InsufficientCredits')<{
  columnId?: string
  columnName?: string
  reason: string
}> {
  static for(reason: string, columnDef: ColumnDefinition) {
    return new InsufficientCredits({
      reason,
      columnId: columnDef.columnId,
      columnName: columnDef.columnName,
    })
  }
}

export class AgentUnknownError extends Data.TaggedError('AgentUnknownError')<{
  cause: unknown
}> {
  static for(cause: unknown) {
    return new AgentUnknownError({
      cause,
    })
  }
}

export class AIInvalidToolArgumentsError extends Data.TaggedError('AIInvalidToolArgumentsError')<{
  cause: InvalidToolArgumentsError
}> {
  static for(cause: InvalidToolArgumentsError) {
    return new AIInvalidToolArgumentsError({ cause })
  }
}

export class AISdkAgentError extends Data.TaggedError('AISdkAgentError')<{
  cause: AISDKError
}> {
  static for(cause: AISDKError) {
    return new AISdkAgentError({
      cause,
    })
  }
}

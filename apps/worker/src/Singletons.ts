import { WorkflowLLM } from '@upbound/shared'
import { Config, Context, Effect, Layer, Redacted } from 'effect'
import { OpenAI } from 'openai'

interface SingletonInstances {
  workflowLLM: WorkflowLLM
}

const make = Effect.gen(function* () {
  const openAIAPIKey = yield* Config.redacted('OPENAI_API_KEY')
  const openAI = new OpenAI({ apiKey: Redacted.value(openAIAPIKey) })

  const workflowLLM = new WorkflowLLM(openAI)
  return { workflowLLM }
})

export class Singletons extends Context.Tag('Singletons')<Singletons, SingletonInstances>() {}

export const SingletonsLive = Layer.effect(Singletons, make)

import type { AgentDataType } from '@upbound/shared/agents'

export const blankTableColumnDefinitions = [
  {
    columnName: 'Full name',
    agentTask: 'Find the full name of the CEO of the company',
    dataType: {
      type: 'singleLineText' as const,
      formatTemplate: '[First Name](eg: <PERSON>)',
    } satisfies AgentDataType,
  },
  {
    columnName: 'Job title',
    agentTask: 'Find the job title of the person',
    dataType: {
      type: 'singleLineText' as const,
      formatTemplate: '[Job Title](eg: CEO)',
    } satisfies AgentDataType,
  },
  {
    columnName: 'Company name',
    agentTask: 'Find the name of the company',
    dataType: {
      type: 'singleLineText' as const,
      formatTemplate: '[Company Name](eg: Acme, Inc.)',
    } satisfies AgentDataType,
  },
  {
    columnName: 'Company website',
    agentTask: 'Find the website of the company',
    dataType: {
      type: 'url' as const,
    } satisfies AgentDataType,
  },
].map(def => ({
  type: 'agent' as const,
  columnId: crypto.randomUUID(),
  columnName: def.columnName,
  config: {
    task: def.columnName,
    agentTask: def.agentTask,
    dataType: def.dataType,
  },
}))

const firstColumnId = blankTableColumnDefinitions[0]?.columnId

export const defaultRows = Array.from({ length: 5 }, (_, rowIndex) => ({
  rowId: crypto.randomUUID(),
  rowData:
    rowIndex === 0
      ? {
          [firstColumnId as string]: 'Type, paste, or upload data to this table',
        }
      : {},
}))

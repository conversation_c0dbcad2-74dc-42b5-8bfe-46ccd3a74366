import { useCallback, useState } from 'react'

export const useStackNavigation = <T>(steps: T[]) => {
  const [stepIndex, setStepIndex] = useState(0)
  const currentStep = steps[stepIndex]

  const goToNextStep = useCallback(() => {
    stepIndex < steps.length - 1 && setStepIndex(stepIndex + 1)
  }, [stepIndex, steps.length])

  const goToPreviousStep = useCallback(() => {
    stepIndex > 0 && setStepIndex(stepIndex - 1)
  }, [stepIndex])

  return {
    currentStep,
    currentStepIndex: stepIndex,
    goToNextStep,
    goToPreviousStep,
  }
}

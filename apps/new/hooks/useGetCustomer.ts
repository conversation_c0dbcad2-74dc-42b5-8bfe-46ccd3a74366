import { trpc } from '../utils/trpc'

/**
 * Get the connection status of the Salesforce CRM integration
 * If `null`, a connection does not exist.
 */
export const useGetCustomer = () => {
  const state = trpc.customer.get.useQuery(void 0, {
    staleTime: Number.POSITIVE_INFINITY,
    gcTime: Number.POSITIVE_INFINITY,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  return state
}

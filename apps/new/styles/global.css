/* Disables macOs system gesture for swipe to go back */
body:has(.ag-root-wrapper) {
  overscroll-behavior-x: contain;
}

.mantine-ComboboxChevron-chevron {
  color: var(--mantine-color-gray-9) !important;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.cl-alertTextContainer:before {
  position: absolute;
  left: 0;
  top: calc(50% - 10px);
  width: 20px;
  height: 20px;
  content: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22none%22%3E%0A%20%20%3Cg%20clip-path%3D%22url%28%23clip0_823_30058%29%22%3E%0A%20%20%20%20%3Cpath%20d%3D%22M9.99992%207.4995V10.8328M9.99992%2013.3328H10.0083M8.63574%202.99201L1.88074%2014.2703C1.74149%2014.5115%201.6678%2014.7849%201.667%2015.0634C1.6662%2015.3418%201.73832%2015.6157%201.87619%2015.8576C2.01407%2016.0996%202.21288%2016.3012%202.45286%2016.4425C2.69284%2016.5837%202.96562%2016.6597%203.24407%2016.6628H16.7557C17.0341%2016.6596%2017.3067%2016.5836%2017.5466%2016.4424C17.7864%2016.3012%2017.9852%2016.0997%2018.123%2015.8578C18.2609%2015.616%2018.333%2015.3423%2018.3323%2015.064C18.3316%2014.7856%2018.2581%2014.5123%2018.1191%2014.2712L11.3641%202.99117C11.222%202.75659%2011.0217%202.56263%2010.7828%202.428C10.5438%202.29338%2010.2742%202.22266%209.99991%202.22266C9.72563%202.22266%209.45599%202.29338%209.21703%202.428C8.97807%202.56263%208.77786%202.75659%208.63574%202.99117V2.99201Z%22%20stroke%3D%22%23E3353F%22%20stroke-width%3D%221.66667%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%0A%20%20%3C%2Fg%3E%0A%20%20%3Cdefs%3E%0A%20%20%20%20%3CclipPath%20id%3D%22clip0_823_30058%22%3E%0A%20%20%20%20%20%20%3Crect%20width%3D%2220%22%20height%3D%2220%22%20fill%3D%22white%22%20transform%3D%22translate%280%20-0.*********%29%22%2F%3E%0A%20%20%20%20%3C%2FclipPath%3E%0A%20%20%3C%2Fdefs%3E%0A%3C%2Fsvg%3E");
}

.cl-formFieldLabelRow:has(+ input[required]) > label:after {
  content: "*";
  color: var(--mantine-color-red-5);
  vertical-align: super;
  line-height: 0;
  margin-left: 2px;
}

.cl-formFieldLabel__emailAddress-field {
  text-transform: lowercase;
}

.cl-formFieldLabel__emailAddress-field:before {
  content: "Company";
  text-transform: capitalize;
  margin-right: 4px;
}

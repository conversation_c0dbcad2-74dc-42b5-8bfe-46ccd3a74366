import type { useModalsStack } from '@mantine/core'
import { createContext, useContext } from 'react'

export interface ModalsStackContextValue<
  ModalIdsType extends string,
  DataType extends Record<string, unknown> = Record<string, never>,
> {
  stack: ReturnType<typeof useModalsStack<ModalIdsType>>
  data?: DataType
}

export const createModalsStackContext = <
  ModalIdsType extends string,
  DataType extends Record<string, unknown> = Record<string, never>,
>() => {
  const ModalsStackContext = createContext<ModalsStackContextValue<ModalIdsType, DataType> | undefined>(undefined)

  const useModalsStackContext = () => {
    const context = useContext(ModalsStackContext)

    if (!context) {
      throw new Error('useModalsStackContext must be used within a ModalsStackProvider')
    }

    return context
  }

  return {
    Provider: ModalsStackContext.Provider,
    Consumer: ModalsStackContext.Consumer,
    useModalsStackContext,
  } as const
}

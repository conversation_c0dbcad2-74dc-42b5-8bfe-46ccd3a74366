import { cloneElement, type ReactElement, type ReactNode } from 'react'

export const addEventToChildren = <E extends React.SyntheticEvent>(
  children: ReactNode,
  eventName: keyof React.DOMAttributes<Element>,
  eventHandler: (e: E) => void,
  preserveExisting = true
): ReactNode => {
  if (typeof children === 'object' && children !== null) {
    return cloneElement(children as ReactElement<{ [K in typeof eventName]?: (e: E) => void }>, {
      [eventName]: (e: E) => {
        eventHandler(e)

        if (preserveExisting) {
          ;(children as ReactElement<{ [K in typeof eventName]?: (e: E) => void }>).props?.[eventName]?.(e)
        }
      },
    })
  }
  return children
}

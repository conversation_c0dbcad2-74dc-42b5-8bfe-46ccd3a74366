import type { DetailedHTMLProps, ForwardedRef, ImgHTMLAttributes } from 'react'

export const logoFor = (
  domain: string,
  size: number | string = 16,
  additionalProps?: DetailedHTMLProps<ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> & {
    ref?: ForwardedRef<HTMLImageElement>
  }
) => {
  const { ref, key, ...otherProps } = additionalProps || {}
  return (
    <img
      ref={ref}
      key={key}
      loading="lazy"
      decoding="async"
      src={`https://img.logo.dev/${domain}?token=pk_J6yErTKkTDusO_itTmjAkQ&size=${size}&format=png&retina=true`}
      alt={`${domain} logo`}
      width={size}
      height={size}
      {...otherProps}
    />
  )
}

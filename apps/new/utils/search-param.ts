export type SearchParams = { [key: string]: string | string[] | undefined }

export function getSearchParamValue<T>(
  searchParams: SearchParams,
  paramName: string,
  validOptions: readonly { value: T }[],
  defaultValue: T
): T {
  let paramValue = searchParams[paramName]

  if (Array.isArray(paramValue)) {
    paramValue = paramValue[0]
  }

  if (!paramValue) {
    return defaultValue
  }

  for (const option of validOptions) {
    if (paramValue === option.value) {
      return paramValue as T
    }
  }

  return defaultValue
}

/**
 * Sets a global CSS custom property (CSS variable) on the root element.
 *
 * @param name - The name of the CSS custom property, with the leading '--'.
 * @param value - The value to set for the CSS custom property. If null, the property will be removed.
 *
 * @example
 * // Set a CSS variable
 * setGlobalCssVar('primary-color', '#007bff');
 *
 * // Remove a CSS variable
 * setGlobalCssVar('primary-color', null);
 */
export const setGlobalCssVar = (name: string, value: string | null) => {
  document.documentElement.style.setProperty(name, value)
}

import { useOrganization, useUser } from '@clerk/nextjs'

export const useIsFreckleEmployee = () => {
  const { user } = useUser()
  return Boolean(user?.primaryEmailAddress?.emailAddress.endsWith('freckle.io'))
}

export const useIsLegacyCellsDisabled = () => {
  const { organization } = useOrganization()
  return !!organization?.publicMetadata?.legacyCellsDisabled
}

export const useIsHttpApiEnabled = () => {
  const { organization } = useOrganization()
  return !!organization?.publicMetadata?.httpApiEnabled
}

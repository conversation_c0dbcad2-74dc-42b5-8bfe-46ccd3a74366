import type { inferReactQueryProcedureOptions } from '@trpc/react-query'
import { createTRPCReact } from '@trpc/react-query'
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server'
import type { AppRouter } from '../../api/src/routers/AppRouter'

export type ReactQueryOptions = inferReactQueryProcedureOptions<AppRouter>
export type RouterInputs = inferRouterInputs<AppRouter>
export type RouterOutputs = inferRouterOutputs<AppRouter>

export const trpc = createTRPCReact<AppRouter>()

export const API_SERVER = (process.env['NEXT_PUBLIC_API_SERVER'] as string | undefined) ?? 'http://localhost:4000'

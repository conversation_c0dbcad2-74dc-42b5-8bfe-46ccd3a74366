/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  images: {
    remotePatterns: [{ hostname: 'zenprospect-production.s3.amazonaws.com' }],
  },
  typescript: {
    tsconfigPath: './tsconfig.json',
  },
  experimental: {
    turbopackMinify: true,
    turbopackTreeShaking: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    optimizePackageImports: [
      '@mantine/core',
      '@mantine/hooks',
      '@mantine/form',
      '@mantine/modals',
      '@mantine/spotlight',
      '@mantine/dropzone',
      '@mantine/notifications',
      '@mantine/dates',
    ],
  },
  redirects() {
    return [
      {
        source: '/',
        destination: '/tables',
        permanent: false,
      },
    ]
  },
  async rewrites() {
    return [
      // PostHog reverse-proxy rules
      {
        source: "/gluttony/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/gluttony/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
      {
        source: "/gluttony/decide",
        destination: "https://us.i.posthog.com/decide",
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
}

export default nextConfig

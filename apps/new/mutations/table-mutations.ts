import { useMemo } from 'react'
import { trpc } from '../utils/trpc'

export const useWorkflowTableMutations = () => {
  const trpcUtils = trpc.useUtils()
  const commonMutationOptions = useMemo(
    () => ({
      onSuccess: () => trpcUtils.workflow.listWorkflowTables.invalidate(),
    }),
    [trpcUtils]
  )

  return {
    addWorkflowTableMutation: trpc.workflow.addWorkflowTable.useMutation(commonMutationOptions),
    updateWorkflowTableMutation: trpc.workflow.updateWorkflowTable.useMutation(commonMutationOptions),
    trashWorkflowTablesMutation: trpc.workflow.trashWorkflowTables.useMutation(commonMutationOptions),
    restoreWorkflowTablesMutation: trpc.workflow.restoreWorkflowTables.useMutation(commonMutationOptions),
  }
}

export const useAddAgentColumnsMutation = (options?: Parameters<typeof trpc.agent.addAgentColumns.useMutation>[0]) => {
  const trpcUtils = trpc.useUtils()

  return trpc.agent.addAgentColumns.useMutation({
    ...options,
    onSuccess: (...args) => {
      options?.onSuccess?.(...args)

      const [{ workflowTable }] = args
      if (workflowTable) {
        return trpcUtils.workflow.getWorkflowTable.setData(
          { workflowTableId: workflowTable?.workflowTableId.toString() },
          data => {
            return { ...data, workflowTable }
          }
        )
      }
    },
    onError: (...args) => {
      // If we encounter an error, simply reload the table entirely.
      options?.onError?.(...args)
    },
  })
}

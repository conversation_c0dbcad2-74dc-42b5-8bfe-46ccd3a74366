import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface SettingsState {
  showAgentSteps: boolean
  setShowAgentSteps: (show: boolean) => void
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    set => ({
      showAgentSteps: true,
      setShowAgentSteps: show => set({ showAgentSteps: show }),
    }),
    {
      name: 'settings-storage',
    }
  )
)

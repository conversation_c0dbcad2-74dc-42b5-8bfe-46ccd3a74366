{"name": "@app/new", "version": "0.1.0", "private": true, "scripts": {"with-env": "dotenv -e ../../.env.local --", "dev": "pnpm with-env next dev --turbopack", "build": "pnpm with-env next build", "start": "next start"}, "dependencies": {"@clerk/nextjs": "^6.25.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@intercom/messenger-js-sdk": "^0.0.14", "@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/dropzone": "^8.1.3", "@mantine/form": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/modals": "^8.1.3", "@mantine/notifications": "^8.1.3", "@mantine/spotlight": "^8.1.3", "@stripe/react-stripe-js": "^3.3.0", "@stripe/stripe-js": "^5.8.0", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "@trpc/client": "11.1.2", "@trpc/react-query": "11.1.2", "@upbound/shared": "workspace:*", "@upbound/ui-kit": "workspace:*", "@upbound/utils": "workspace:*", "ag-grid-enterprise": "^34.0.2", "ag-grid-react": "^34.0.2", "csv-parse": "^5.6.0", "dayjs": "^1.11.13", "fuse.js": "^7.1.0", "iso-3166": "^4.3.0", "libphonenumber-js": "^1.12.10", "next": "15.3.4", "posthog-js": "^1.257.1", "radash": "^12.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^9.0.3", "superjson": "^2.2.1", "tippy.js": "^6.3.7", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@app/api": "workspace:*", "@total-typescript/ts-reset": "^0.6.1", "@trpc/server": "11.1.2", "@types/node": "22.15.29", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/uuid": "^10.0.0", "@upbound/types": "workspace:*", "dotenv-cli": "^8.0.0", "postcss": "^8.5.6", "postcss-preset-mantine": "1.18.0", "postcss-simple-vars": "^7.0.1", "tsconfig": "workspace:*"}}
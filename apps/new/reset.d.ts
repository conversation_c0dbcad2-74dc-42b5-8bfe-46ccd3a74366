// Do not add any other lines of code to this file!

// all rules - disabled until all rules are safely enabled below
// import '@total-typescript/ts-reset'

// Make JSON.parse return unknown - some unchecked use of JSON.parse needs to be refactored
// import '@total-typescript/ts-reset/json-parse'

// Make .json() return unknown
import '@total-typescript/ts-reset/fetch'

// Make .filter(Boolean) filter out falsy values - breaks interpretation of the api's use of `propertyIsNullOrUndefined`
// import '@total-typescript/ts-reset/filter-boolean'

// Make .includes on as const arrays less strict
import '@total-typescript/ts-reset/array-includes'

// Make .indexOf on as const arrays less strict
import '@total-typescript/ts-reset/array-index-of'

// Make Set.has() less strict
import '@total-typescript/ts-reset/set-has'

// Make Map.has() less strict
import '@total-typescript/ts-reset/map-has'

// Removing any[] from Array.isArray()
import '@total-typescript/ts-reset/is-array'

// Making sessionStorage and localStorage safer
import '@total-typescript/ts-reset/storage'

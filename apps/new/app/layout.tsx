import '@mantine/core/styles.css'
import '@mantine/notifications/styles.css'
import '@mantine/dates/styles.css'
import 'styles/global.css'

import { ClerkProvider } from '@clerk/nextjs'
import { ColorSchemeScript, MantineProvider, mantineHtmlProps } from '@mantine/core'
import { ModalsProvider } from '@mantine/modals'
import { Notifications } from '@mantine/notifications'
import { InterFont } from '@upbound/ui-kit/fonts/GoogleFonts'
import { cssVariablesResolver, theme } from '@upbound/ui-kit/theme'
import { IntercomWrapper } from 'components/Analytics/Intercom'
import { CSPostHogProvider } from 'components/Analytics/Posthog'
import type { Metadata } from 'next'
import localFont from 'next/font/local'
import type { ComponentProps, ReactNode } from 'react'
import { TrpcProvider } from '../components/TrpcProvider'

export const metadata: Metadata = {
  title: 'Freckle',
  description: 'User friendly data enrichment',
}

const mackinac = localFont({
  src: [
    {
      path: './P22 Mackinac Pro Book.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: './P22 Mackinac Pro Book Italic.otf',
      weight: '400',
      style: 'italic',
    },
    {
      path: './P22 Mackinac Pro Medium.otf',
      weight: '500',
      style: 'normal',
    },
    {
      path: './P22 Mackinac Pro Medium Italic.otf',
      weight: '500',
      style: 'italic',
    },
    {
      path: './P22 Mackinac Pro Bold.otf',
      weight: '700',
      style: 'normal',
    },
    {
      path: './P22 Mackinac Pro Bold Italic.otf',
      weight: '700',
      style: 'italic',
    },
    {
      path: './P22 Mackinac Pro Extra Bold.otf',
      weight: '900',
      style: 'normal',
    },
    {
      path: './P22 Mackinac Pro Extra Bold Italic.otf',
      weight: '900',
      style: 'italic',
    },
  ],
})

const ClerkAppearance: ComponentProps<typeof ClerkProvider>['appearance'] = {
  elements: {
    alert: {
      alignItems: 'center',
      backgroundColor: '#ffeaec',
      borderColor: '#ec777e',
      padding: 16,
    },

    // see styles/global.css for what we replace the icon with
    alertIcon: {
      display: 'none',
    },

    // see styles.global.css for associated `:before` styling
    alertTextContainer: {
      position: 'relative',
      paddingLeft: 36,
    },

    avatarBox: {
      height: 18,
      width: 18,
    },
    buttonArrowIcon: {
      display: 'none',
    },
    card: {
      gap: 24,
    },
    dividerText: {
      color: '#bbbcbc',
      fontSize: 14,
      lineHeight: 20 / 14,
    },
    footerActionLink: {
      color: '#1f2020',
      fontSize: 14,
      lineHeight: 20 / 14,
    },
    footerActionText: {
      fontSize: 14,
      lineHeight: 20 / 14,
    },
    form: {
      gap: 16,
    },
    formButtonPrimary: {
      backgroundColor: '#713bdb',
      boxShadow: 'none !important',
      fontSize: 14,
      fontWeight: 400,
      lineHeight: 14 / 14,
      paddingBlock: 10,
      paddingInline: 16,
    },
    formFieldInput: {
      fontSize: 14,
      lineHeight: 20 / 14,
      padding: 8,
    },
    formFieldLabel: {
      fontSize: 14,
      lineHeight: 20 / 14,
    },
    header: {
      gap: 24,
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: 500,
      lineHeight: 20 / 16,
      marginBottom: 4,
    },
    headerSubtitle: {
      color: '#737474',
      fontSize: 12,
      fontWeight: 400,
      lineHeight: 16 / 12,
    },
    logoBox: {
      height: 18,
    },
    notificationBadge: {
      display: 'none',
    },
    organizationPreview__organizationSwitcherTrigger: {
      gap: 8,
    },
    socialButtonsBlockButton: {
      padding: 8,
    },
    socialButtonsBlockButtonText: {
      color: '#1f2020',
      fontSize: 14,
      fontWeight: 400,
      lineHeight: 20 / 14,
    },
    socialButtonsProviderIcon: {
      height: 20,
      maxWidth: 20,
      width: 20,
    },
  },
  layout: {
    shimmer: false,
  },
  signIn: {
    elements: {
      rootBox: {
        backgroundColor: '#f9fafa',
        minHeight: '100vh',
        width: '100%',
        paddingBlock: 32,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
    },
  },
  signUp: {
    elements: {
      rootBox: {
        backgroundColor: '#f9fafa',
        minHeight: '100vh',
        width: '100%',
        paddingBlock: 32,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
    },
  },
  variables: {
    borderRadius: '8px',
    colorDanger: '#e3353f',
    colorInputText: '#1f2020',
    colorNeutral: '#1f2020',
    colorPrimary: '#713bdb',
    colorSuccess: '#64c19a',
    colorText: '#1f2020',
    colorTextSecondary: '#737474',
    colorWarning: '#fb931d',
    fontFamily: InterFont.style.fontFamily,
    fontFamilyButtons: InterFont.style.fontFamily,
  },
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <ClerkProvider appearance={ClerkAppearance}>
      <html lang="en" {...mantineHtmlProps} className={mackinac.className}>
        <head>
          <ColorSchemeScript />
          <link rel="shortcut icon" href="/favicon.png" />
          <meta name="viewport" content="minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no" />
        </head>
        <body>
          <MantineProvider theme={theme} cssVariablesResolver={cssVariablesResolver}>
            <CSPostHogProvider>
              <IntercomWrapper>
                {/* the insanely high z-index is intercom's z-index + 1 to push notifications above intercom */}
                <Notifications containerWidth="calc(22.5rem * var(--mantine-scale))" zIndex={2_147_483_002} />
                <TrpcProvider>
                  <ModalsProvider>{children}</ModalsProvider>
                </TrpcProvider>
              </IntercomWrapper>
            </CSPostHogProvider>
          </MantineProvider>
        </body>
      </html>
    </ClerkProvider>
  )
}

'use client'

import { GoogleOneTap, SignUp } from '@clerk/nextjs'
import { useCallback, useEffect, useRef } from 'react'

export default function Page() {
  const [trigger, clearAll] = useReplaceAlertText()

  // the alert can appear on page load or after the 'continue' button has been pressed
  useEffect(() => {
    trigger()

    const btn = document.querySelector<HTMLButtonElement>('.cl-formButtonPrimary')
    btn?.addEventListener('click', trigger)

    return () => {
      clearAll()
      btn?.removeEventListener('click', trigger)
    }
  }, [trigger, clearAll])

  return (
    <>
      <GoogleOneTap />
      <SignUp />
    </>
  )
}

function useReplaceAlertText() {
  const interval = useRef<NodeJS.Timeout>(null)
  const timeout = useRef<NodeJS.Timeout>(null)

  const clearAll = useCallback(() => {
    _clearAll(interval.current, timeout.current)
  }, [])

  const trigger = useCallback(() => {
    clearAll()

    interval.current = setInterval(() => {
      const alertText = document.querySelector('p.cl-alertText')
      console.log(alertText)
      if (alertText?.innerHTML.includes('is not allowed to access this application')) {
        console.log('replacing')
        alertText.innerHTML = 'Please use a company or organization email to sign up.'
        _clearInterval(interval.current)
      }
    }, 50)

    // we don't want this hack running indefinitely.
    // after 5 seconds we can be quite confident that the alert text is not present on the screen.
    // clear the interval so that we're not needlessly processing.
    timeout.current = setTimeout(() => {
      _clearInterval(interval.current)
    }, 5000)
  }, [clearAll])

  return [trigger, clearAll] as const
}

function _clearAll(interval: NodeJS.Timeout | null, timeout: NodeJS.Timeout | null) {
  _clearInterval(interval)
  _clearTimeout(timeout)
}

function _clearInterval(interval: NodeJS.Timeout | null) {
  if (interval) {
    clearInterval(interval)
  }
}

function _clearTimeout(timeout: NodeJS.Timeout | null) {
  if (timeout) {
    clearTimeout(timeout)
  }
}

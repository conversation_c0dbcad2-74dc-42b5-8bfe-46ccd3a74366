import { ActionIcon, type ActionIconProps } from '@mantine/core'
import styles from 'app/(appshell)/tables/TableActionButton.module.css'
import { forwardRef } from 'react'

export const TableActionButton = forwardRef<HTMLButtonElement, ActionIconProps>((props, ref) => {
  const { ...otherProps } = props

  return (
    <ActionIcon
      ref={ref}
      {...otherProps}
      variant="subtle"
      color="gray"
      size="md"
      className={styles.hover}
      style={{
        color: 'var(--mantine-color-gray-9)',
      }}
    />
  )
})

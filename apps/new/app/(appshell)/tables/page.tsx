'use client'
import {
  ActionIcon,
  Alert,
  Avatar,
  Box,
  Button,
  Center,
  Checkbox,
  Group,
  Menu,
  ScrollArea,
  SimpleGrid,
  Skeleton,
  Stack,
  Table,
  Text,
  TextInput,
  Tooltip,
  Transition,
  UnstyledButton,
} from '@mantine/core'
import { useDebouncedValue, useListState } from '@mantine/hooks'
import { notifications } from '@mantine/notifications'
import {
  IconAlertTriangle,
  IconArrowDown,
  IconArrowRight,
  IconArrowUp,
  IconDotsVertical,
  IconFileTypeCsv,
  IconForms,
  IconPinned,
  IconPinnedFilled,
  IconPinnedOff,
  IconPlayerPlay,
  IconSearch,
  IconTable,
  IconTablePlus,
  IconTrash,
} from '@tabler/icons-react'
import { StaticSelectDropdown } from '@upbound/ui-kit/components/StaticSelectDropdown'
import { NewTableButton } from 'app/(appshell)/tables/NewTableButton'
import { TableActionButton } from 'app/(appshell)/tables/TableActionButton'
import { CreateTableModalsStack } from 'components/Modals/CreateTableModal/CreateTableModalsStack'
import { CreateTableModalId } from 'components/Modals/CreateTableModal/types'
import { renameTableModal } from 'components/Modals/renameTableModal'
import { useTrashTableModal } from 'components/Modals/useTrashTableModal'
import { RelativeDateFormat } from 'components/RelativeDateFormat'
import { blankTableColumnDefinitions, defaultRows } from 'config/new-table-defaults'
import newTableBackground from 'images/new-table-background.svg'
import { useWorkflowTableMutations } from 'mutations/table-mutations'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { type PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react'
import { trpc } from 'utils/trpc'
import styles from './page.module.css'

type SelectedTable = { id: number; name: string }

export default function MainContainer() {
  const [fuzzyName, setFuzzyName] = useState('')
  const [filter, setFilter] = useState<'active' | 'trash'>('active')
  const [selectedTables, selectedTablesHandlers] = useListState<SelectedTable>([])

  const resetSelectedIds = useCallback(() => {
    selectedTablesHandlers.setState([])
  }, [selectedTablesHandlers])

  const handleFilterChange = useCallback(
    (newFilter: 'active' | 'trash') => {
      setFilter(newFilter)
      resetSelectedIds()
    },
    [resetSelectedIds]
  )

  const showTrashTableModal = useTrashTableModal(resetSelectedIds)

  const handleMoveToTrash = useCallback(() => {
    if (selectedTables.length === 0) return

    if (selectedTables.length === 1) {
      const selectedTable = selectedTables[0]!
      showTrashTableModal({
        type: 'single',
        tableName: selectedTable.name,
        workflowTableId: selectedTable.id,
      })
    } else {
      showTrashTableModal({
        type: 'multiple',
        workflowTableIds: selectedTables.map(table => table.id),
      })
    }
  }, [selectedTables, showTrashTableModal])

  return (
    <Stack pt={24} px={24}>
      <Stack flex={1} gap={24} w="100%" maw={1024} mx="auto" style={{ minWidth: 0 }}>
        <NewTableFrom />
        <Box bdrs={16} style={{ border: '1px solid var(--mantine-color-gray-2)', overflow: 'hidden' }} bg="white">
          <Group
            p={12}
            bg="gray.0"
            justify="space-between"
            align="center"
            wrap="nowrap"
            style={{ borderBottom: '1px solid var(--mantine-color-gray-2)' }}
          >
            <Group gap={16} style={{ flexGrow: 1 }}>
              <TextInput
                size="md"
                placeholder="Search"
                leftSection={<IconSearch size={16} color="var(--mantine-color-gray-4)" />}
                w={320}
                maw={400}
                style={{ flexShrink: 1 }}
                value={fuzzyName}
                onChange={e => setFuzzyName(e.currentTarget.value)}
              />
            </Group>
            <Group gap={8}>
              <Transition
                mounted={selectedTables.length > 0}
                transition="fade"
                duration={150}
                timingFunction="ease-out"
              >
                {styles => (
                  <div style={styles}>
                    <Button color="red.5" onClick={handleMoveToTrash} leftSection={<IconTrash size={20} />}>
                      Move to trash
                    </Button>
                  </div>
                )}
              </Transition>
              <StaticSelectDropdown
                items={[
                  {
                    icon: <IconPlayerPlay size={16} color="var(--mantine-color-green-8)" />,
                    value: 'active',
                    label: 'Active',
                  },
                  {
                    icon: <IconTrash size={16} color="var(--mantine-color-red-8)" />,
                    value: 'trash',
                    label: 'Trash',
                  },
                ]}
                value={filter}
                onChange={handleFilterChange}
              />
              <CreateNewTableButton />
            </Group>
          </Group>
          <Stack pt={12}>
            <ScrollArea.Autosize mah="calc(100vh - 400px)" styles={{ scrollbar: { zIndex: 3 } }}>
              <Box px={12} pb={12}>
                <TablesList
                  fuzzyName={fuzzyName}
                  filter={filter}
                  setFilter={handleFilterChange}
                  selectedTables={selectedTables}
                  selectedTablesHandlers={selectedTablesHandlers}
                />
              </Box>
            </ScrollArea.Autosize>
          </Stack>
        </Box>
      </Stack>
    </Stack>
  )
}

const TablesList = ({
  fuzzyName,
  filter,
  setFilter,
  selectedTables,
  selectedTablesHandlers,
}: {
  fuzzyName?: string
  filter: 'active' | 'trash'
  setFilter: (filter: 'active' | 'trash') => void
  selectedTables: SelectedTable[]
  selectedTablesHandlers: ReturnType<typeof useListState<SelectedTable>>[1]
}) => {
  const router = useRouter()
  const {
    rows,
    sortBy,
    reverseSortDirection,
    setSorting,
    debouncedFuzzyName,
    baseCount,
    hasAnyTables,
    isLoading,
    isError,
    isSuccess,
    togglePin,
  } = useFilteredTables(filter, fuzzyName)
  const toggleSelectRow = useCallback(
    (id: number, tableName: string) => {
      const existingIndex = selectedTables.findIndex(table => table.id === id)
      if (existingIndex !== -1) {
        selectedTablesHandlers.remove(existingIndex)
      } else {
        selectedTablesHandlers.append({ id, name: tableName })
      }
    },
    [selectedTables, selectedTablesHandlers]
  )

  const selectedTableIds = useMemo(() => new Set(selectedTables.map(table => table.id)), [selectedTables])

  const allSelectedInView = useMemo(
    () => rows.length > 0 && rows.every(r => selectedTableIds.has(r.workflowTableId)),
    [rows, selectedTableIds]
  )
  const someSelectedInView = useMemo(
    () => rows.some(r => selectedTableIds.has(r.workflowTableId)),
    [rows, selectedTableIds]
  )

  const toggleSelectAllInView = useCallback(() => {
    const currentRowIds = new Set(rows.map(r => r.workflowTableId))

    selectedTablesHandlers.setState(current => {
      if (allSelectedInView) {
        return current.filter(table => !currentRowIds.has(table.id))
      } else {
        const newSelections = rows
          .filter(r => !selectedTableIds.has(r.workflowTableId))
          .map(r => ({ id: r.workflowTableId, name: r.name }))
        return [...current, ...newSelections]
      }
    })
  }, [allSelectedInView, rows, selectedTablesHandlers, selectedTableIds])

  if (isSuccess) {
    if (baseCount === 0) {
      return <EmptyState filter={filter} fuzzyName={fuzzyName} setFilter={setFilter} />
    }

    return (
      <>
        {filter === 'trash' && baseCount > 0 ? (
          <Alert
            icon={<IconAlertTriangle size={20} color="var(--mantine-color-red-8)" />}
            radius={8}
            styles={{
              root: {
                backgroundColor: 'var(--mantine-color-gray-0)',
                border: '1px solid var(--mantine-color-gray-2)',
              },
              wrapper: {
                alignItems: 'center',
              },
              icon: {
                marginInlineEnd: 12,
              },
            }}
          >
            <Text>Items in trash will be deleted forever after 30 days</Text>
          </Alert>
        ) : null}
        <Table horizontalSpacing="md" verticalSpacing="xs" stickyHeader highlightOnHover>
          <Table.Thead>
            <Table.Tr style={{ borderBottom: 'none' }}>
              <Table.Th style={{ boxShadow: 'inset 0 -1px 0 var(--mantine-color-gray-2)' }}>
                <Group gap={8} wrap="nowrap" align="center" style={{}}>
                  {filter !== 'trash' && (
                    <Checkbox
                      mx={6}
                      aria-label="Select all"
                      checked={allSelectedInView}
                      indeterminate={!allSelectedInView && someSelectedInView}
                      onChange={toggleSelectAllInView}
                      style={{
                        '--checkbox-size': '16px',
                      }}
                    />
                  )}
                  <UnstyledButton onClick={() => setSorting('name')} style={{ width: '100%' }}>
                    <Group gap={4} wrap="nowrap">
                      <Text c="gray.6" fw={500} style={{ textWrap: 'nowrap' }}>
                        Name
                      </Text>
                      {sortBy === 'name' ? (
                        reverseSortDirection ? (
                          <IconArrowDown size={16} color="var(--mantine-color-gray-6)" />
                        ) : (
                          <IconArrowUp size={16} color="var(--mantine-color-gray-6)" />
                        )
                      ) : null}
                    </Group>
                  </UnstyledButton>
                </Group>
              </Table.Th>
              <Table.Th style={{ boxShadow: 'inset 0 -1px 0 var(--mantine-color-gray-2)' }}>
                <UnstyledButton onClick={() => setSorting('owner')} style={{ width: '100%' }}>
                  <Group gap={4} wrap="nowrap" justify="flex-start">
                    <Text c="gray.6" fw={500} style={{ textWrap: 'nowrap' }}>
                      Owner
                    </Text>
                    {sortBy === 'owner' ? (
                      reverseSortDirection ? (
                        <IconArrowDown size={16} color="var(--mantine-color-gray-6)" />
                      ) : (
                        <IconArrowUp size={16} color="var(--mantine-color-gray-6)" />
                      )
                    ) : null}
                  </Group>
                </UnstyledButton>
              </Table.Th>
              <Table.Th style={{ boxShadow: 'inset 0 -1px 0 var(--mantine-color-gray-2)' }}>
                <UnstyledButton onClick={() => setSorting('updatedAt')} style={{ width: '100%' }}>
                  <Group gap={4} wrap="nowrap" justify="flex-start">
                    <Text c="gray.6" fw={500} style={{ textWrap: 'nowrap' }}>
                      Last modified
                    </Text>
                    {sortBy === 'updatedAt' ? (
                      reverseSortDirection ? (
                        <IconArrowDown size={16} color="var(--mantine-color-gray-6)" />
                      ) : (
                        <IconArrowUp size={16} color="var(--mantine-color-gray-6)" />
                      )
                    ) : null}
                  </Group>
                </UnstyledButton>
              </Table.Th>
              <HeaderTh />
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {rows.length > 0 ? (
              rows.map(item => {
                const href = `/tables/${item.isOnboardingTable ? 'onboarding' : item.workflowTableId}`
                return (
                  <Table.Tr key={item.workflowTableId} className={styles.tableRow}>
                    <Table.Td style={{ padding: '0 var(--mantine-spacing-md)', height: '100%' }}>
                      <Group
                        gap={10}
                        align="center"
                        style={{ flexWrap: 'nowrap', minWidth: 0, height: '100%', minHeight: 48 }}
                      >
                        <Box className={styles.iconContainer}>
                          <Checkbox
                            className={`${styles.tableCheckbox} ${filter === 'trash' ? styles.checkboxDisabled : ''}`}
                            mx={6}
                            checked={selectedTableIds.has(item.workflowTableId)}
                            onChange={() => toggleSelectRow(item.workflowTableId, item.name)}
                            onClick={e => e.stopPropagation()}
                            disabled={filter === 'trash'}
                            aria-label={selectedTableIds.has(item.workflowTableId) ? 'Deselect table' : 'Select table'}
                            style={{
                              '--checkbox-size': '16px',
                            }}
                          />
                          <Box
                            className={styles.tableIcon}
                            bg="green.0"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRadius: 4,
                              border: '1px solid var(--mantine-color-green-2)',
                              width: 24,
                              height: 24,
                            }}
                            mx={2}
                          >
                            <IconTable size={16} color="var(--mantine-color-green-9)" />
                          </Box>
                        </Box>
                        <TdLink href={href} disabled={filter === 'trash'}>
                          <Text
                            fw={500}
                            className={`${styles.tableName} ${filter === 'trash' ? styles.tableNameDisabled : ''}`}
                          >
                            {item.name}
                          </Text>
                        </TdLink>
                      </Group>
                    </Table.Td>
                    <Table.Td style={{ padding: '0 var(--mantine-spacing-md)', height: '100%' }}>
                      <TdLink href={href} disabled={filter === 'trash'}>
                        <User user={item.owner} />
                      </TdLink>
                    </Table.Td>
                    <Table.Td style={{ padding: '0 var(--mantine-spacing-md)', height: '100%' }}>
                      <TdLink href={href} disabled={filter === 'trash'}>
                        <Tooltip
                          label={`${tooltipDateFormatter.format(item.updatedAt)} @ ${tooltipTimeFormatter.format(item.updatedAt)}`}
                          openDelay={400}
                          closeDelay={150}
                        >
                          <Text component="span">
                            <RelativeDateFormat date={item.updatedAt} />
                          </Text>
                        </Tooltip>
                      </TdLink>
                    </Table.Td>
                    <Table.Td
                      onClick={e => e.stopPropagation()}
                      onMouseDown={e => e.stopPropagation()}
                      style={{ padding: '0 var(--mantine-spacing-md)', height: '100%' }}
                    >
                      <Group
                        gap={0}
                        justify="flex-end"
                        wrap="nowrap"
                        className={styles.actionButtons}
                        style={{ alignItems: 'center' }}
                      >
                        <TableRowActions
                          workflowTableId={item.workflowTableId}
                          tableName={item.name}
                          isPinned={item.isPinned}
                          isArchived={!!item.deletedAt}
                          onTogglePin={() => togglePin(item.workflowTableId, !item.isPinned)}
                          setFilter={setFilter}
                        />
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                )
              })
            ) : (
              <Table.Tr>
                <Table.Td colSpan={4}>
                  <Text fw={500} ta="center">
                    Nothing found
                  </Text>
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>
      </>
    )
  }

  if (isLoading) {
    return (
      <Box mt={16}>
        {Array.from({ length: 3 }, (_, i) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: no reason given
          <Skeleton h={32} mb={16} key={i} />
        ))}
      </Box>
    )
  }

  return isError ? <Text c="red.5">Error loading tables. Please try again soon.</Text> : null
}

const tooltipDateFormatter = new Intl.DateTimeFormat('en-US', {
  month: 'long',
  day: 'numeric',
  year: 'numeric',
})

const tooltipTimeFormatter = new Intl.DateTimeFormat('en-US', {
  hour: 'numeric',
  minute: '2-digit',
  hour12: true,
})

const TableRowActions = ({
  workflowTableId,
  tableName,
  isPinned,
  isArchived,
  onTogglePin,
  setFilter,
}: {
  workflowTableId: number
  tableName: string
  isPinned: boolean
  isArchived: boolean
  onTogglePin: () => void
  setFilter?: (filter: 'active' | 'trash') => void
}) => {
  const showTrashTableModal = useTrashTableModal()
  const { restoreWorkflowTablesMutation } = useWorkflowTableMutations()

  const handleRestore = () => {
    restoreWorkflowTablesMutation.mutate(
      { workflowTableIds: [workflowTableId] },
      {
        onSuccess: () => {
          notifications.show({
            title: 'Table restored',
            message: (
              <Stack gap={8} my={2}>
                <Text c="gray.6">It's now back in your Active tables</Text>
                {setFilter && (
                  <Button variant="outline" style={{ alignSelf: 'flex-start' }} onClick={() => setFilter('active')}>
                    View active tables
                  </Button>
                )}
              </Stack>
            ),
            color: 'green',
            autoClose: 8000,
          })
        },
      }
    )
  }

  if (isArchived) {
    return (
      <Button size="sm" variant="light" color="purple" onClick={handleRestore}>
        Restore
      </Button>
    )
  }

  return (
    <>
      <PinButton isPinned={isPinned} onClick={onTogglePin} />
      <Menu shadow="sm" position="bottom-end">
        <Menu.Target>
          <TableActionButton>
            <IconDotsVertical size={16} />
          </TableActionButton>
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Item
            onClick={() => renameTableModal({ workflowTableId, tableName })}
            leftSection={<IconForms size={14} />}
          >
            <Text size="md">Rename</Text>
          </Menu.Item>
          <Menu.Item
            onClick={onTogglePin}
            leftSection={isPinned ? <IconPinnedOff size={14} /> : <IconPinned size={14} />}
          >
            <Text size="md">{isPinned ? 'Unpin' : 'Pin to top'}</Text>
          </Menu.Item>
          <Menu.Item
            onClick={() => showTrashTableModal({ type: 'single', tableName, workflowTableId })}
            color="red.5"
            leftSection={<IconTrash size={14} />}
          >
            <Text size="md" c="red.5">
              Move to trash
            </Text>
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </>
  )
}

const HeaderTh = ({ children, width }: PropsWithChildren<{ width?: number }>) => (
  <Table.Th
    style={{
      boxShadow: 'inset 0 -1px 0 var(--mantine-color-gray-2)',
      ...(width ? { width } : {}),
    }}
  >
    {children}
  </Table.Th>
)

const NewTableFrom = () => {
  const router = useRouter()
  const { addWorkflowTableMutation } = useWorkflowTableMutations()
  const createBlankTable = useCallback(
    () =>
      addWorkflowTableMutation
        .mutateAsync({
          tableName: 'New table',
          columnDefinitions: blankTableColumnDefinitions,
          rows: defaultRows,
        })
        .then(async data => {
          router.push(`/tables/${data.workflowTable.table.workflowTableId}`)
        }),
    [addWorkflowTableMutation, router]
  )

  return (
    <CreateTableModalsStack>
      {stack => (
        <Stack
          gap={12}
          p={16}
          pos="relative"
          style={{
            backgroundImage: `url(${newTableBackground.src})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            border: '1px solid var(--mantine-color-purple-1)',
            borderRadius: 16,
          }}
        >
          <Text fz={16} fw={500} c="var(--mantine-color-purple-9)">
            New table from...
          </Text>
          <SimpleGrid cols={{ xs: 2, md: 4 }} spacing={16} w="100%">
            <NewTableButton
              color="green"
              icon={<IconFileTypeCsv size={16} color="var(--mantine-color-green-9)" />}
              onClick={() => stack.open(CreateTableModalId.UPLOAD_CSV)}
            >
              CSV
            </NewTableButton>
            <NewTableButton
              color="purple"
              icon={<IconTable size={16} color="var(--mantine-color-purple-9)" />}
              onClick={createBlankTable}
            >
              Scratch
            </NewTableButton>
          </SimpleGrid>
        </Stack>
      )}
    </CreateTableModalsStack>
  )
}

const CreateNewTableButton = () => (
  <CreateTableModalsStack>
    {stack => (
      <Button
        style={{ flexShrink: 0 }}
        onClick={() => stack.open(CreateTableModalId.ENTRY_POINT)}
        leftSection={<IconTablePlus size={20} />}
      >
        New table
      </Button>
    )}
  </CreateTableModalsStack>
)

type SortField = 'name' | 'owner' | 'updatedAt'

const usePinTableMutation = (options?: Parameters<typeof trpc.workflow.setPinnedTable.useMutation>[0]) => {
  const trpcUtils = trpc.useUtils()

  return trpc.workflow.setPinnedTable.useMutation({
    ...options,
    onMutate: async ({ workflowTableId, pinned }) => {
      await trpcUtils.workflow.listPinnedTableIds.cancel()
      const previousPinnedTableIds = trpcUtils.workflow.listPinnedTableIds.getData()
      trpcUtils.workflow.listPinnedTableIds.setData(undefined, old => {
        if (!old) return old

        return {
          pinnedTableIds: pinned
            ? [...old.pinnedTableIds, Number(workflowTableId)]
            : old.pinnedTableIds.filter(x => x !== Number(workflowTableId)),
        }
      })

      return { previousPinnedTableIds }
    },
    onError: (err, newData, context) => {
      if (context?.previousPinnedTableIds) {
        trpcUtils.workflow.listPinnedTableIds.setData(undefined, context.previousPinnedTableIds)
      }
      options?.onError?.(err, newData, context)
    },
    onSettled: (...args) => {
      trpcUtils.workflow.listPinnedTableIds.invalidate()
      options?.onSettled?.(...args)
    },
  })
}

function useFilteredTables(filter: 'active' | 'trash', fuzzyName?: string) {
  const [debouncedFuzzyName] = useDebouncedValue(fuzzyName, 300)
  const workflowTableItems = trpc.workflow.listWorkflowTables.useQuery({
    fuzzyName: debouncedFuzzyName?.trim(),
    filter,
  })
  const pinnedTableIdsQuery = trpc.workflow.listPinnedTableIds.useQuery()
  const setPinned = usePinTableMutation()

  const [hasAnyTables, setHasAnyTables] = useState<boolean>(false)
  const currentTables = workflowTableItems.data?.workflowTables
  const isEmptySearch = !debouncedFuzzyName?.trim()
  useEffect(() => {
    if (workflowTableItems.isSuccess && isEmptySearch && currentTables) {
      setHasAnyTables(currentTables.length > 0)
    }
  }, [workflowTableItems.isSuccess, isEmptySearch, currentTables])

  const [sortBy, setSortBy] = useState<SortField>('updatedAt')
  const [reverseSortDirection, setReverseSortDirection] = useState<boolean>(true)

  const setSorting = (field: SortField) => {
    const reversed = field === sortBy ? !reverseSortDirection : field === 'updatedAt'
    setReverseSortDirection(reversed)
    setSortBy(field)
  }

  const rows = useMemo(() => {
    const base = workflowTableItems.data?.workflowTables ?? []
    const pinnedSet = new Set(pinnedTableIdsQuery.data?.pinnedTableIds ?? [])

    const withPinned = base.map(item => ({ ...item, isPinned: pinnedSet.has(item.workflowTableId) }))

    const sorted = withPinned.toSorted((a, b) => {
      if (a.isPinned !== b.isPinned) {
        return a.isPinned ? -1 : 1
      }
      const dir = reverseSortDirection ? -1 : 1
      if (sortBy === 'name') {
        return a.name.localeCompare(b.name) * dir
      }
      if (sortBy === 'owner') {
        const aName = [a.owner?.firstName, a.owner?.lastName].filter(Boolean).join(' ')
        const bName = [b.owner?.firstName, b.owner?.lastName].filter(Boolean).join(' ')
        return aName.localeCompare(bName) * dir
      }
      const aTime = a.updatedAt.getTime()
      const bTime = b.updatedAt.getTime()
      return (aTime - bTime) * dir
    })

    return sorted
  }, [workflowTableItems.data, pinnedTableIdsQuery.data, sortBy, reverseSortDirection])

  return {
    rows,
    sortBy,
    reverseSortDirection,
    setSorting,
    debouncedFuzzyName,
    baseCount: workflowTableItems.data?.workflowTables.length ?? 0,
    hasAnyTables,
    isLoading: workflowTableItems.isLoading || pinnedTableIdsQuery.isLoading,
    isFetching: workflowTableItems.isFetching || pinnedTableIdsQuery.isFetching,
    isError: workflowTableItems.isError || pinnedTableIdsQuery.isError,
    isSuccess: workflowTableItems.isSuccess && pinnedTableIdsQuery.isSuccess,
    togglePin: (workflowTableId: number, pinned: boolean) => setPinned.mutate({ workflowTableId, pinned }),
  }
}

const User = ({
  user,
}: {
  user: { profileImageUrl: string | null; firstName: string | null; lastName: string | null } | null
}) => {
  if (!user) {
    return <Text>User not found</Text>
  }

  const fullName =
    user.firstName || user.lastName ? [user.firstName, user.lastName].filter(Boolean).join(' ') : 'Unnamed user'
  const avatarProps = { radius: 6, size: 20, src: user.profileImageUrl }
  return (
    <Group gap={8} align="center" wrap="nowrap">
      <Tooltip label={fullName}>
        <Avatar {...avatarProps} hiddenFrom="md" />
      </Tooltip>
      <Avatar {...avatarProps} visibleFrom="md" />
      <Text truncate="end" visibleFrom="md" maw={150}>
        {fullName}
      </Text>
    </Group>
  )
}

const PinButton = ({ isPinned, onClick }: { isPinned: boolean; onClick: () => void }) => {
  const [iconHovered, setIconHovered] = useState(false)

  const icon = useMemo(() => {
    if (isPinned) return <IconPinnedFilled size={16} color={'var(--mantine-color-gray-6)'} />
    if (iconHovered) return <IconPinnedFilled size={16} color={'var(--mantine-color-gray-4)'} />
    return <IconPinned size={16} color={'var(--mantine-color-gray-4)'} />
  }, [isPinned, iconHovered])

  return (
    <Tooltip label={isPinned ? 'Unpin' : 'Pin to top'} openDelay={400} closeDelay={150}>
      <ActionIcon
        variant="transparent"
        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
          e.stopPropagation()
          e.preventDefault()
          onClick()
        }}
        onMouseEnter={() => setIconHovered(true)}
        onMouseLeave={() => setIconHovered(false)}
        display="flex"
        className={`${styles.pinButton} ${isPinned ? styles.pinButtonPinned : ''}`}
      >
        {icon}
      </ActionIcon>
    </Tooltip>
  )
}

const EmptyState = ({
  filter,
  fuzzyName,
  setFilter,
}: {
  filter: 'active' | 'trash'
  fuzzyName?: string
  setFilter: (filter: 'active' | 'trash') => void
}) => {
  const { debouncedFuzzyName, hasAnyTables, isLoading } = useFilteredTables(filter, fuzzyName)

  const title = filter === 'trash' ? 'No tables in trash' : 'No tables'

  const description = useMemo(() => {
    if (filter === 'trash') {
      return 'Tables you’ve deleted will appear here. For now, all your tables are active and ready to work on.'
    }
    if (debouncedFuzzyName) {
      return 'Adjust search or change filter'
    }
    return 'New tables will live here for easy access'
  }, [filter, debouncedFuzzyName])

  const showActiveTablesButton = filter === 'trash'
  const showCreateNewTableButton = !debouncedFuzzyName && (hasAnyTables || isLoading)

  return (
    <Center py={24}>
      <Stack align="center" justify="center">
        <Text fz={16} fw={500}>
          {title}
        </Text>
        <Text c="dimmed">{description}</Text>
        {showActiveTablesButton && (
          <Button
            variant="light"
            color="purple"
            mt={12}
            rightSection={<IconArrowRight size={20} />}
            onClick={() => setFilter('active')}
          >
            View active tables
          </Button>
        )}
        {showCreateNewTableButton && <CreateNewTableButton />}
      </Stack>
    </Center>
  )
}

const TdLink = ({
  href,
  children,
  disabled = false,
}: {
  href: string
  children: React.ReactNode
  disabled?: boolean
}) => {
  if (disabled) return children

  return (
    <Link
      href={href}
      style={{
        textDecoration: 'none',
        color: 'inherit',
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
      }}
    >
      {children}
    </Link>
  )
}

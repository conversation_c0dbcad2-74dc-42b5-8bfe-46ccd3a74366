.tableRow {
  position: relative;

  &:hover {
    .tableCheckbox:not(.checkboxDisabled) {
      opacity: 1;
      visibility: visible;
    }

    .tableCheckbox:not(.checkboxDisabled) + .tableIcon {
      opacity: 0;
      visibility: hidden;
    }

    .tableName:not(.tableNameDisabled) {
      text-decoration: underline;
    }
  }

  &:hover:has(.iconContainer:hover, .actionButtons:hover) .tableName {
    text-decoration: none;
  }
}

.iconContainer {
  position: relative;
  width: 36px;
  height: 24px;
  display: flex;
  align-items: center;
}

.tableCheckbox {
  position: absolute;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s ease, visibility 0.15s ease;

  &[data-checked="true"] {
    opacity: 1 !important;
    visibility: visible !important;

    + .tableIcon {
      opacity: 0 !important;
      visibility: hidden !important;
    }
  }
}

.tableIcon {
  opacity: 1;
  visibility: visible;
  transition: opacity 0.15s ease, visibility 0.15s ease;
}

.tableName {
  text-decoration: none;
  transition: text-decoration 0.15s ease;
}

.tableNameDisabled {
  cursor: default;
}

.pinButton {
  transition: opacity 0.15s ease, visibility 0.15s ease;
}

.tableRow:not(:hover) .pinButton:not(.pinButtonPinned) {
  opacity: 0;
  visibility: hidden;
}

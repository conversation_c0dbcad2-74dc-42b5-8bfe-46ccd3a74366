import { Box, Button, Stack, Text } from '@mantine/core'
import styles from 'app/(appshell)/tables/NewTableButton.module.css'

export const NewTableButton = ({
  children,
  icon,
  color,
  onClick,
}: {
  children: React.ReactNode
  icon: React.ReactNode
  color: 'orange' | 'green' | 'blue' | 'purple'
  onClick: () => void
}) => {
  return (
    <Button
      className={styles[color]}
      fullWidth
      h={72}
      onClick={onClick}
      c="gray.9"
      styles={{
        root: {
          '--button-hover': 'var(--mantine-color-white)',
          '--button-bg': 'var(--mantine-color-white)',
          '--button-bd': '1px solid var(--mantine-color-gray-3)',
          borderRadius: 8,
        },
        label: {
          fontWeight: 400,
        },
      }}
    >
      <Stack gap={8} align="center">
        <Box bg={`${color}.0`} style={{ display: 'flex', borderRadius: 4 }} p={4}>
          {icon}
        </Box>
        <Text size="md">{children}</Text>
      </Stack>
    </Button>
  )
}

'use client'

import {
  ActionIcon,
  Avatar,
  Box,
  Card,
  CopyButton,
  Group,
  Radio,
  SimpleGrid,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from '@mantine/core'
import { IconCheck, IconCopy, IconSearch } from '@tabler/icons-react'
import { PillRadioCard } from 'components/PillRadioCard/PillRadioCard'
import Fuse from 'fuse.js'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { type ChangeEventHandler, use, useState } from 'react'
import { logoFor } from 'utils/logo'
import { getSearchParamValue, type SearchParams } from 'utils/search-param'
import freckleLogoSmall from '../../../images/freckle-logo-small.svg'

const TagPopularEnrichments = 'popular' as const
const TagHubSpotActions = 'hubspot' as const
const TagIntegrations = 'integrations' as const

const Tags = [
  { label: 'All', value: 'all' },
  { label: 'Popular enrichments', value: TagPopularEnrichments },
  { label: 'HubSpot actions', value: TagHubSpotActions },
  { label: 'Integration playbooks', value: TagIntegrations },
] as const

const Demos = [
  {
    label: 'Find email address & phone numbers',
    tag: TagPopularEnrichments,
    loomId: '29a98df4c00543e2ad91a5381072dbdb',
  },
  {
    label: 'Find LinkedIn URLs',
    tag: TagPopularEnrichments,
    loomId: '958079c008f74bd9b7e280849498abef',
  },
  {
    label: 'Clean messy & incomplete data',
    tag: TagPopularEnrichments,
    loomId: 'df6cb2c64a6144beaaefa6004f21da48',
  },
  {
    label: 'Researching companies for rich attributes',
    tag: TagPopularEnrichments,
    loomId: '4df17a31249b4ef18d21a7082bd98ae0',
  },
  {
    label: 'Hubspot - End-to-end walkthrough',
    tag: TagHubSpotActions,
    loomId: '266d46773f5f4de297e90200cde03c0d',
  },
  {
    label: 'Integrating HubSpot',
    tag: TagHubSpotActions,
    loomId: '5f64144de4df42a8b62f50c9a86480ac',
  },
  {
    label: 'Exporting to Hubspot to create and/or update records',
    tag: TagHubSpotActions,
    loomId: 'f04cabb88a824e728074b40eb90fd9bf',
  },
  {
    label: 'Importing HubSpot records with one-time import',
    tag: TagHubSpotActions,
    loomId: '1a0af2d7acb64fc4ad2831ba83755fc7',
  },
  {
    label: 'Importing HubSpot records dynamically with recurring sync',
    tag: TagHubSpotActions,
    loomId: '7ea678da8d1e490fbe4aa87028db4f33',
  },
  {
    label: 'Lookup HubSpot object',
    tag: TagHubSpotActions,
    loomId: 'a8eb6e806a784ac0a31e89cb5049bd36',
  },
  {
    label: 'Send Message to Slack Channel',
    tag: TagIntegrations,
    loomId: '582960f18d5443d6b91fb43e3f77319e',
  },
]

const fuse = new Fuse(Demos, {
  keys: ['label'],
  threshold: 0.3,
  isCaseSensitive: false,
  shouldSort: true,
})

const Logos = [
  { key: 'freckle', logo: <Image src={freckleLogoSmall} alt="Freckle logo" height={16} /> },
  { key: 'hubspot', logo: logoFor('hubspot.com', 16) },
  { key: 'instantly', logo: logoFor('instantly.ai', 16) },
  { key: 'heyreach', logo: logoFor('heyreach.io', 16) },
  { key: 'slack', logo: logoFor('slack.com', 24) }, // slack logo has extra padding in the image
] as const

export default function MainContainer({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const [filterValue, setFilterValue] = useState('')
  const router = useRouter()
  const tag = getSearchParamValue(use(searchParams), 'tag', Tags, 'all')

  const changeFilter: ChangeEventHandler<HTMLInputElement> = evt => {
    setFilterValue(evt.currentTarget.value)
  }

  const changeTag = (next: string) => {
    const params = new URLSearchParams()
    params.set('tag', next)
    router.replace(`/how-to?${params.toString()}`)
  }

  let demos = filterValue ? fuse.search(filterValue).map(r => r.item) : Demos
  if (tag !== 'all') {
    demos = demos.filter(d => d.tag === tag)
  }

  return (
    <Stack align="center" gap={0} mt={24}>
      <Avatar.Group>
        {Logos.map(({ key, logo }, i) => (
          <Avatar
            key={key}
            size={32}
            styles={{
              root: {
                // reverse layers from default avatar group
                zIndex: -i,
                // reduce overlap in avatar group from default -12
                marginInlineStart: -8,
                // copied from figma
                boxShadow:
                  '0px 6px 24px 0px #0000000A, 0px 4px 8px 0px #0000000A, 0px 2px 4px 0px #0000000A, 0px 0px 2px 0px #0000000A',
              },
              placeholder: {
                backgroundColor: 'var(--mantine-white)',
              },
            }}
          >
            {logo}
          </Avatar>
        ))}
      </Avatar.Group>
      <Title order={2} fz={24} lh={28 / 24} fw={500} ta="center" mt="md">
        How to use Freckle
      </Title>
      <Text ta="center" mt={8} maw={520} c="gray.6">
        Whether you&apos;re just getting started or starting to level up, these videos will help you clean, enrich, and
        activate your data with ease.
      </Text>
      <TextInput
        value={filterValue}
        onChange={changeFilter}
        size="md"
        placeholder="Search"
        leftSection={<IconSearch size={16} color="var(--mantine-color-gray-4)" />}
        mt="md"
        w={560}
        maw="100%"
      />
      <Radio.Group mt="md" value={tag} onChange={changeTag}>
        <Group justify="center">
          {Tags.map(t => (
            <PillRadioCard key={t.value} value={t.value}>
              {t.label}
            </PillRadioCard>
          ))}
        </Group>
      </Radio.Group>
      {demos.length === 0 ? (
        <Stack mt={80} gap={8} align="center">
          <Text ta="center" fw={500}>
            No results found
          </Text>
          <Text ta="center" size="sm" c="gray.6">
            Try adjusting your search
          </Text>
        </Stack>
      ) : null}
      <SimpleGrid cols={{ xs: 1, sm: 2, lg: 3 }} spacing={24} mt={40} w="100%" maw={1136}>
        {demos.map(d => {
          const loomUrl = `https://www.loom.com/share/${d.loomId}`

          return (
            <Card
              key={d.label}
              shadow="none"
              radius={12}
              h="100%"
              withBorder
              styles={{ root: { borderColor: 'var(--mantine-color-gray-2)' } }}
            >
              <Card.Section>
                <Box bg="gray.9" h={12} />
                {/* embed code copied from loom share modal */}
                <div style={{ position: 'relative', paddingBottom: '57.***************%', height: 0 }}>
                  <iframe
                    title={d.label}
                    loading="lazy"
                    src={`https://www.loom.com/embed/${d.loomId}?hide_speed=true&hideEmbedTopBar=true`}
                    // biome-ignore lint/nursery/noTsIgnore: must ignore deprecations as they are not an error
                    // @ts-ignore - deprecated but necessary
                    frameBorder="0"
                    // @ts-expect-error - not a known property to jsx
                    webkitallowfullscreen="true"
                    mozallowfullscreen="true"
                    allowFullScreen
                    style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
                  />
                </div>
              </Card.Section>
              <Group justify="space-between" align="center" mt="md">
                <Tooltip label={d.label} position="top" multiline disabled={d.label.length < 40}>
                  <Text
                    fw={500}
                    style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      flex: 1,
                      marginRight: 8,
                    }}
                  >
                    {d.label}
                  </Text>
                </Tooltip>
                <CopyButton value={loomUrl} timeout={1500}>
                  {({ copied, copy }) => (
                    <Tooltip label={copied ? 'Copied!' : 'Share video'} position="left">
                      <ActionIcon variant="transparent" color={copied ? 'green' : 'gray'} size="sm" onClick={copy}>
                        {copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
                      </ActionIcon>
                    </Tooltip>
                  )}
                </CopyButton>
              </Group>
            </Card>
          )
        })}
      </SimpleGrid>
    </Stack>
  )
}

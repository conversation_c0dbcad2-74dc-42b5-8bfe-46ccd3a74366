import type { IntegrationType } from '@upbound/shared/repo/IntegrationCredentialSchema.js'
import { IntegrationIcon } from 'app/(appshell)/IntegrationIcons'
import type { JSX } from 'react'

const iconProps = {
  size: 20,
}

export const Integration: Record<IntegrationType, { name: string; icon: JSX.Element }> = {
  hubspot: { name: 'HubSpot', icon: <IntegrationIcon integration="hubspot" {...iconProps} /> },
  attio: { name: 'Attio', icon: <IntegrationIcon integration="attio" {...iconProps} /> },
  instantly: { name: 'Instantly', icon: <IntegrationIcon integration="instantly" {...iconProps} /> },
  heyreach: { name: 'HeyReach', icon: <IntegrationIcon integration="heyreach" {...iconProps} /> },
  slack: { name: 'Slack', icon: <IntegrationIcon integration="slack" {...iconProps} /> },
}

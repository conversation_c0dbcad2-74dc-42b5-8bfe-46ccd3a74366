.root {
  --app-shell-border-color: var(--mantine-color-default-border) !important;
}

.navbar > div {
  overflow: hidden;
}

.navbar {
  :global(.mantine-NavLink-root) {
    height: 32px;

    color: var(--mantine-color-gray-9);
    border-radius: var(--mantine-radius-sm);

    &[data-active="true"] {
      background-color: var(--mantine-color-gray-1);
    }

    &:hover {
      background-color: var(--mantine-color-gray-1);
    }
  }
}

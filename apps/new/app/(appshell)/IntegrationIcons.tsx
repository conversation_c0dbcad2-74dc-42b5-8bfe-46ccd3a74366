import { IconCodeDots, IconFileTypeCsv, IconMathFunction, IconWebhook } from '@tabler/icons-react'
import { memo, useMemo } from 'react'
import { logoFor } from 'utils/logo'

const getIntegrationIcons = (size: number | string = 16) =>
  ({
    zapier: logoFor('zapier.com', size),
    make: logoFor('make.com', size),
    instantly: logoFor('instantly.ai', size),
    heyreach: logoFor('heyreach.io', size),
    attio: logoFor('attio.com', size),
    hubspot: logoFor('hubspot.com', size),
    slack: logoFor('slack.com', size),
    genericWebhook: <IconWebhook color="var(--mantine-color-purple-5)" size={size} />,
    raw: <IconCodeDots color="var(--mantine-color-purple-5)" size={size} />,
    downloadCsv: <IconFileTypeCsv color="var(--mantine-color-green-9)" size={size} />,
    formula: <IconMathFunction color="var(--mantine-color-purple-5)" size={size} />,
  }) as const

export type SupportedIntegrationIcons = keyof ReturnType<typeof getIntegrationIcons>

export const IntegrationIcon = memo(
  ({ integration, size = 16 }: { integration: SupportedIntegrationIcons; size?: number | string }) =>
    useMemo(() => getIntegrationIcons(size)[integration], [integration, size])
)
IntegrationIcon.displayName = 'IntegrationIcon'

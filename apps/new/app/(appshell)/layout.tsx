'use client'

import { OrganizationSwitcher, useAuth, useClerk, useOrganization, useUser } from '@clerk/nextjs'
import {
  ActionIcon,
  Anchor,
  AppShell,
  Avatar,
  Box,
  Button,
  Group,
  Image,
  Loader,
  Menu,
  NavLink,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core'
import { useDebouncedValue } from '@mantine/hooks'
import {
  IconBulb,
  IconLogout,
  IconPencilMinus,
  IconPlugConnected,
  IconSelector,
  IconSettings,
  IconTable,
  IconUserPlus,
} from '@tabler/icons-react'
import { IntegrationType } from '@upbound/shared/repo/IntegrationCredentialSchema.js'
import { isNotUndefined } from '@upbound/utils'
import { AppTopBanner } from 'components/AppTopBanner'
import { inviteUsersModal } from 'components/Modals/inviteUsersModal'
import { renameTableModal } from 'components/Modals/renameTableModal'
import { useGetCustomer } from 'hooks/useGetCustomer'
import freckleLogoSmall from 'images/freckle-logo-small.svg'
import NextImage from 'next/image'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import type React from 'react'
import { Children, type ReactNode, useEffect, useRef, useState } from 'react'
import { type BreadcrumbItem, Breadcrumbs } from '../../components/Breadcrumbs'
import { trpc } from '../../utils/trpc'
import { Integration } from './Integration'
import classes from './layout.module.css'

const PathBreadcrumbMap: Record<
  string,
  (id?: number | string | undefined, name?: string | undefined, icon?: BreadcrumbItem['icon']) => BreadcrumbItem
> = {
  'how-to': () => ({
    href: '/how-to',
    label: 'How to use Freckle',
    icon: <IconBulb size={16} />,
  }),
  tables: () => ({
    href: '/tables',
    label: 'Tables',
    icon: <IconTable size={16} />,
  }),
  integrations: () => ({
    href: '/integrations',
    label: 'Integrations',
    icon: <IconPlugConnected size={16} />,
  }),
  integrationId: (id, label = '...', icon = null) => ({
    href: `/integrations/${id}`,
    label,
    icon,
  }),
  workflowTableId: (workflowTableId, tableName) => ({
    isCustom: true,
    href: `/tables/${workflowTableId}`,
    label: (
      <Button
        variant="default"
        bd="none"
        loading={tableName === undefined || workflowTableId === undefined}
        onClick={() => {
          if (tableName === undefined || workflowTableId === undefined || typeof workflowTableId === 'string') return
          renameTableModal({ workflowTableId, tableName })
        }}
        rightSection={<IconPencilMinus size={16} color="var(--mantine-color-anchor)" />}
      >
        {tableName ?? '...'}
      </Button>
    ),
  }),
}

const ClerkOrganizationSwitcher = ({ orgId }: { orgId: string | null | undefined }) => {
  const orgIdRef = useRef(orgId)

  useEffect(() => {
    if (!window || !orgId) return

    if (orgIdRef.current !== orgId) {
      location.reload()
    }

    orgIdRef.current = orgId
  }, [orgId])

  return (
    <OrganizationSwitcher
      hidePersonal
      hideSlug
      createOrganizationMode="modal"
      fallback={<Loader color="gray.6" size={16} />}
    />
  )
}

type MaybeUserResource = ReturnType<typeof useUser>['user']
const shouldShowOrgSwitcher = (user: MaybeUserResource) => {
  return (
    user && (user.organizationMemberships.length > 1 || user.primaryEmailAddress?.emailAddress.endsWith('@freckle.io'))
  )
}

const useSignOut = () => {
  const _signOut = useAuth().signOut
  const [isPending, setIsPending] = useState(false)
  const signOut = () => {
    setIsPending(true)
    _signOut()
  }
  return [isPending, signOut] as const
}

const NavbarLink = ({
  children,
  slim = false,
  ...props
}: {
  active: boolean
  href: string
  label: string
  leftSection?: ReactNode
  children?: ReactNode
  slim?: boolean
}) => {
  const navLink = <NavLink {...props} component={Link} bg={props.active ? 'gray.1' : undefined} p={8} h={36} miw={36} />

  const subNav =
    Children.count(children) === 0 ? null : (
      <Group gap={10} ml={18} align="stretch">
        <Box my={2} bg="gray.2" w={1} />
        <Stack gap={4} flex={1}>
          {children}
        </Stack>
      </Group>
    )

  return slim ? (
    <Tooltip label={props.label} position="right">
      {navLink}
    </Tooltip>
  ) : (
    <>
      {navLink}
      {subNav}
    </>
  )
}

/**
 * using next.js' `useSearchParams()` prevents the build from evaluating any pages using this layout.
 * this is a replacement that circumvents that while still providing the same functionality on the client.
 */
const useSearchParams = () => {
  const [search, setSearch] = useState(() => {
    try {
      return location.search
    } catch {
      return ''
    }
  })

  useEffect(() => {
    const listener = () => {
      setSearch(location.search)
    }

    const observer = new MutationObserver(listener)
    observer.observe(document.body, { childList: true, subtree: false })

    return () => {
      observer.disconnect()
    }
  }, [])

  return new URLSearchParams(search)
}

export default function Layout({ children }: { children: React.ReactNode }) {
  useGetCustomer()
  const pathname = usePathname()
  const [delayedPathname] = useDebouncedValue(pathname, 3000)
  const searchParams = useSearchParams()
  const router = useRouter()
  const { userId, orgId, isLoaded } = useAuth()
  const { openUserProfile } = useClerk()
  const { organization } = useOrganization()
  const { user } = useUser()
  const [isSigningOut, signOut] = useSignOut()

  const paths = pathname.split('/').filter(Boolean)
  const [path, subPath] = paths

  const isTablePage = path === 'tables'
  const workflowTableId = (isTablePage ? subPath : undefined) ?? ''
  const isTableWorkflowPage = isTablePage && !!workflowTableId

  const isIntegrationPage = path === 'integrations'
  const integrationType = isIntegrationPage ? IntegrationType.safeParse(subPath).data : undefined
  const enabledIntegration = isIntegrationPage && !!integrationType

  const getWorkflowTable = trpc.workflow.getWorkflowTable.useQuery(
    { workflowTableId },
    { enabled: isTableWorkflowPage }
  )

  const showLearnMoreBanner = delayedPathname === pathname && delayedPathname === '/tables/onboarding'
  const showSlimNav = isTableWorkflowPage

  // If the user is logged in but not in an org, redirect to onboarding
  useEffect(() => {
    if (isLoaded && userId && !orgId) {
      router.push('/onboarding')
    }
  }, [isLoaded, orgId, userId, router])

  const breadcrumbs: BreadcrumbItem[] = paths
    .map((b, i) => {
      if (isTableWorkflowPage && i === 1) {
        return PathBreadcrumbMap['workflowTableId']?.(
          getWorkflowTable.data?.workflowTable?.workflowTableId,
          getWorkflowTable.data?.workflowTable?.name
        )
      } else if (enabledIntegration && i === 1) {
        const integration = Integration[integrationType]
        return PathBreadcrumbMap['integrationId']?.(integrationType, integration.name, integration.icon)
      } else {
        return PathBreadcrumbMap[b]?.(undefined, undefined)
      }
    })
    .filter(isNotUndefined)

  return (
    <AppShell
      classNames={classes}
      navbar={{
        // plus one for border width
        width: (showSlimNav ? 72 : 240) + 1,
        breakpoint: 0,
      }}
      padding="md"
      layout={showLearnMoreBanner ? 'default' : 'alt'}
      header={{ height: 56 + (showLearnMoreBanner ? 32 : 0) }}
    >
      <AppShell.Header>
        <Stack gap={0} h="100%">
          <AppTopBanner show={showLearnMoreBanner}>
            <Anchor
              href="//www.loom.com/share/a8886be2d284425da187cc69c3414669"
              target="_blank"
              p={8}
              bg="purple.0"
              c="purple.9"
              ta="center"
              fz="sm"
              display="block"
            >
              Want help getting started? Watch a 1-min video here
            </Anchor>
          </AppTopBanner>

          <Group align="center" justify="space-between" px="md" flex={1}>
            <Breadcrumbs items={breadcrumbs} />

            <Group gap={8} align="center">
              {shouldShowOrgSwitcher(user) ? <ClerkOrganizationSwitcher orgId={orgId} /> : null}
            </Group>
          </Group>
        </Stack>
      </AppShell.Header>

      <AppShell.Navbar>
        <AppShell.Section
          px={showSlimNav ? 18 : 16}
          py={10}
          h={56}
          styles={{ section: { borderBottom: '1px solid var(--mantine-color-default-border)' } }}
        >
          <ActionIcon href="/" component={Link} variant="default" bg="gray.0" aria-label="Home" radius={8} size={36}>
            <NextImage src={freckleLogoSmall} alt="Freckle logo" height={20} />
          </ActionIcon>
        </AppShell.Section>
        <AppShell.Section
          grow
          component={ScrollArea}
          px={showSlimNav ? 18 : 12}
          py={12}
          styles={{ section: { borderBottom: '1px solid var(--mantine-color-default-border)' } }}
        >
          <Stack gap={8}>
            <NavbarLink
              href="/tables"
              active={pathname.startsWith('/tables')}
              leftSection={<IconTable size={20} />}
              label="Tables"
              slim={showSlimNav}
            />
            <NavbarLink
              href="/integrations"
              active={pathname.startsWith('/integrations')}
              leftSection={<IconPlugConnected size={20} />}
              label="Integrations"
              slim={showSlimNav}
            />
            <NavbarLink
              href="/how-to?tag=all"
              active={pathname === '/how-to' && (showSlimNav || searchParams.get('tag') === 'all')}
              leftSection={<IconBulb size={20} />}
              label="How to use Freckle"
              slim={showSlimNav}
            >
              <NavbarLink
                href="/how-to?tag=popular"
                active={pathname === '/how-to' && searchParams.get('tag') === 'popular'}
                label="Popular enrichments"
              />
              <NavbarLink
                href="/how-to?tag=hubspot"
                active={pathname === '/how-to' && searchParams.get('tag') === 'hubspot'}
                label="HubSpot actions"
              />
              <NavbarLink
                href="/how-to?tag=integrations"
                active={pathname === '/how-to' && searchParams.get('tag') === 'integrations'}
                label="Integration playbooks"
              />
            </NavbarLink>
          </Stack>
        </AppShell.Section>
        <AppShell.Section p={12}>
          <Menu
            width={216}
            styles={{
              dropdown: { borderRadius: 12, paddingInline: 12, paddingBlockStart: 12, paddingBlockEnd: 8 },
              item: { padding: 4 },
              itemSection: { marginInlineEnd: 4 },
            }}
          >
            <Menu.Target>
              <Button
                variant="white"
                h={48}
                w={showSlimNav ? 48 : 216}
                p={8}
                justify="start"
                radius={12}
                leftSection={<Avatar size={32} radius={6} src={user?.imageUrl} />}
                rightSection={<IconSelector size={16} color="var(--mantine-color-gray-9)" />}
                bd="none"
                loading={!isLoaded}
                color="gray"
              >
                <Stack gap={0} align="start" w={136}>
                  <Text size="sm" w="100%" truncate="end" ta="start" fw={500}>
                    {user?.fullName}
                  </Text>
                  <Text size="sm" w="100%" truncate="end" ta="start" c="gray.6">
                    {user?.primaryEmailAddress?.emailAddress}
                  </Text>
                </Stack>
              </Button>
            </Menu.Target>
            <Menu.Dropdown>
              <Group gap={8} mb={8}>
                <Image src={organization?.imageUrl} h={24} w={24} radius={6} />
                <Text size="sm" fw={500}>
                  {organization?.name}
                </Text>
              </Group>
              <Menu.Item fz="sm" mb={4} leftSection={<IconSettings size={12} />} onClick={() => openUserProfile()}>
                Settings
              </Menu.Item>
              <Menu.Item fz="sm" leftSection={<IconUserPlus size={12} />} onClick={inviteUsersModal}>
                Invite members
              </Menu.Item>
              <Menu.Divider mx={-12} mb={8} mt={12} />
              <Menu.Item
                fz="sm"
                leftSection={isSigningOut ? <Loader size={12} color="red" /> : <IconLogout size={12} />}
                onClick={signOut}
                color="red"
                closeMenuOnClick={false}
              >
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </AppShell.Section>
      </AppShell.Navbar>
      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  )
}

import { Text } from '@mantine/core'
import { CardWithGuides, type CardWithGuidesProps } from '@upbound/ui-kit/components/CardWithGuides'
import type { FC } from 'react'

export interface SectionCardProps extends CardWithGuidesProps {
  title: string
}

export const SectionCard: FC<SectionCardProps> = ({ title, children }) => {
  return (
    <CardWithGuides
      top={
        <Text fz="sm" fw={500}>
          {title}
        </Text>
      }
      p={8}
    >
      {children}
    </CardWithGuides>
  )
}

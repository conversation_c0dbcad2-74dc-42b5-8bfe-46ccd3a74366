import { ActionIcon, Button, Group, Breadcrumbs as MantineBreadcrumbs } from '@mantine/core'
import { IconChevronRight } from '@tabler/icons-react'
import Link from 'next/link'
import type { ReactNode } from 'react'

export interface BreadcrumbItem {
  label: ReactNode
  href: string
  icon?: ReactNode
  isCustom?: boolean
}

export const Breadcrumbs = ({ items }: { items: BreadcrumbItem[] }) => {
  const anchors = items.map(item => {
    const key = item.href

    const icon = item.icon ? (
      <ActionIcon key={`${key}-icon`} variant="default" bg="gray.0" radius={4} size={26}>
        {item.icon}
      </ActionIcon>
    ) : null

    return item.isCustom ? (
      <Group key={key} gap={8}>
        {icon}
        {item.label}
      </Group>
    ) : (
      <Button
        key={key}
        component={Link}
        href={item.href}
        variant="default"
        bd="none"
        leftSection={icon}
        // padding is even around the icon if present
        pl={icon ? 3 : 8}
        ml={icon ? 5 : 0}
      >
        {item.label}
      </Button>
    )
  })

  return (
    <MantineBreadcrumbs
      separator={<IconChevronRight size={12} color="var(--mantine-color-gray-4)" />}
      // we want the content of the left-most button to be in-line with the page
      ml={-8}
      separatorMargin={0}
    >
      {anchors}
    </MantineBreadcrumbs>
  )
}

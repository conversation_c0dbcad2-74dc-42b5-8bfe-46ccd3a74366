import { Stack, TextInput } from '@mantine/core'
import { FaviconForCompany } from '@upbound/ui-kit/components/FaviconForCompany'
import { SectionCard } from 'components/SectionCard'
import type { FC } from 'react'

export interface DataProvider {
  name: string
  domain: string
  label?: string
}

export interface DataProvidersCardProps {
  title?: string
  dataProviders: DataProvider[]
}

export const DataProvidersCard: FC<DataProvidersCardProps> = ({ title = 'Data providers', dataProviders }) => {
  return (
    <SectionCard title={title}>
      <Stack gap={8}>
        {dataProviders.map(({ name, domain, label }) => (
          <TextInput
            variant="filled"
            size="sm"
            readOnly
            pointer
            label={label}
            value={name}
            leftSection={<FaviconForCompany domain={domain} w={12} h={12} />}
            key={domain}
          />
        ))}
      </Stack>
    </SectionCard>
  )
}

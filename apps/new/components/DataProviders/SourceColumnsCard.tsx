import { Stack } from '@mantine/core'
import { SectionCard } from 'components/SectionCard'
import type { FC, PropsWithChildren } from 'react'

interface SourceColumnsCardProps extends PropsWithChildren {}

export const SourceColumnsCard: FC<SourceColumnsCardProps> = ({ children }) => {
  return (
    <SectionCard title="Source columns">
      <Stack gap={8}>{children}</Stack>
    </SectionCard>
  )
}

import { Box, Group, type GroupProps, Stack } from '@mantine/core'
import type { ReactNode } from 'react'
import classes from './ModalShell.module.css'

export interface ModalShellProps {
  sidebar?: ReactNode
  content?: ReactNode
  footer?: ReactNode
  wrapperProps?: GroupProps
}

export const ModalShell = ({ sidebar, content, footer, wrapperProps }: ModalShellProps) => {
  return (
    <Stack className={classes.shellRoot} gap={0}>
      <Group gap={0} align="stretch" wrap="nowrap" {...wrapperProps}>
        {sidebar ? (
          <Stack className={classes.sidebar} gap={0}>
            {sidebar}
          </Stack>
        ) : null}
        <Box className={classes.content}>{content}</Box>
      </Group>
      <Box className={classes.footer}>{footer}</Box>
    </Stack>
  )
}

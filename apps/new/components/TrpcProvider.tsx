'use client'

import { useAuth } from '@clerk/nextjs'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createWSClient, httpBatchLink, loggerLink, splitLink, wsLink } from '@trpc/client'
import { useState } from 'react'
import superjson from 'superjson'
import { API_SERVER, trpc } from '../utils/trpc'

const url = `${API_SERVER}/rpc`

const queryClient = new QueryClient()

export const TrpcProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { getToken } = useAuth()

  const [wsClient] = useState(() =>
    createWSClient({
      url: url.replace('http', 'ws'),
      lazy: {
        enabled: true,
        closeMs: 2000,
      },
      connectionParams: async () => {
        return { token: (await getToken()) ?? void 0 }
      },
    })
  )

  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        loggerLink({
          enabled: opts =>
            (process.env['NODE_ENV'] === 'development' && typeof window !== 'undefined') ||
            (opts.direction === 'down' && opts.result instanceof Error),
        }),
        splitLink({
          condition: operation => operation.type === 'subscription',
          true: wsLink({ client: wsClient, transformer: superjson }),
          false: httpBatchLink({
            maxURLLength: 4096,
            url,
            headers: async () => ({
              Authorization: `Bearer ${await getToken()}`,
            }),
            transformer: superjson,
          }),
        }),
      ],
    })
  )

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  )
}

import { Box, Transition } from '@mantine/core'
import type { PropsWithChildren } from 'react'

export interface AppTopBannerProps extends PropsWithChildren {
  show?: boolean
}

export const AppTopBanner = ({ show, children }: AppTopBannerProps) => {
  return (
    <Transition mounted={show ?? false} transition="fade-down" duration={300}>
      {styles => <Box style={styles}>{children}</Box>}
    </Transition>
  )
}

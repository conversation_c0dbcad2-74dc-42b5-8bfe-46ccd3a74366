'use client'

import { useOrganization, useSession } from '@clerk/nextjs'
import posthog from 'posthog-js'
import { PostHogProvider } from 'posthog-js/react'
import { useEffect } from 'react'

const env = process.env.NEXT_PUBLIC_POSTHOG_KEY
const shouldInit = typeof window !== 'undefined' && !!env

if (shouldInit) {
  posthog.init(env, {
    // Check next.config.mjs for posthog related rewrite rules
    api_host: '/gluttony',
    ui_host: 'https://us.posthog.com',
    person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
    session_recording: {
      maskAllInputs: false,
      maskInputOptions: {
        password: true,
      },
    },
  })
}

const ConfigurePosthog = () => {
  const org = useOrganization()
  const { session } = useSession()

  useEffect(() => {
    // Ignore sessions from impersonated users
    if (org.organization?.id && session && !session.actor?.sub) {
      posthog.identify(session.user.primaryEmailAddress?.emailAddress ?? session.user.id, {
        name: session.user.fullName ?? '',
        email: session.user.primaryEmailAddress?.emailAddress ?? '',
        orgName: org.organization?.name ?? '',
        orgId: org.organization?.id ?? '',
      })

      posthog.group('company', org.organization?.id, {
        name: org.organization?.name,
      })
    }
  }, [org.organization?.id, org.organization?.name, session])

  return null
}

export function CSPostHogProvider({ children }: { children: React.ReactNode }) {
  return (
    <PostHogProvider client={posthog}>
      {shouldInit ? <ConfigurePosthog /> : null}
      {children}
    </PostHogProvider>
  )
}

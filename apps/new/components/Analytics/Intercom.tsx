'use client'

import { useOrganization, useSession } from '@clerk/nextjs'
import Intercom from '@intercom/messenger-js-sdk'
import type { ReactNode } from 'react'

const ConfigureIntercom = () => {
  const { organization } = useOrganization()
  const { session } = useSession()

  // If this is an impersonated user, don't show the intercom widget
  if (session?.actor?.sub) {
    return null
  }

  Intercom({
    app_id: 'evgfv8dc',
    user_id: session?.user.id,
    name: session?.user?.fullName ?? '',
    orgId: organization?.id,
    orgName: organization?.name,
    email: session?.user?.primaryEmailAddress?.emailAddress,
    created_at: session?.user.createdAt?.valueOf(),
  })

  return null
}

export const IntercomWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <>
      {typeof window === 'undefined' ? null : <ConfigureIntercom />}
      {children}
    </>
  )
}

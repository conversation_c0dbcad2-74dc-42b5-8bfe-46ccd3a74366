import { Radio } from '@mantine/core'
import type { ComponentProps, PropsWithChildren } from 'react'
import classes from './PillRadioCard.module.css'

export const PillRadioCard = ({
  children,
  key,
  value,
}: PropsWithChildren<Pick<ComponentProps<typeof Radio.Card>, 'key' | 'value'>>) => {
  return (
    <Radio.Card
      key={key}
      value={value}
      flex="0 0 0"
      p={8}
      radius="md"
      className={classes.root}
      styles={{ card: { whiteSpace: 'nowrap' } }}
    >
      {children}
    </Radio.Card>
  )
}

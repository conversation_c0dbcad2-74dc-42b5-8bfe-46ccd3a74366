'use client'

import { useClerk } from '@clerk/nextjs'
import { <PERSON>ert, Button, Group, Stack, TextInput } from '@mantine/core'
import { useForm } from '@mantine/form'
import { modals } from '@mantine/modals'
import { notifications } from '@mantine/notifications'
import { useCallback, useState } from 'react'

const Form = ({ close }: { close: () => void }) => {
  const clerk = useClerk()
  const [reqStatus, setReqStatus] = useState<{ status: 'idle' | 'running' } | { status: 'error'; error: string }>({
    status: 'idle',
  })

  const inviteUser = useCallback(
    async (emailAddress: string) => {
      setReqStatus({ status: 'running' })

      try {
        await clerk.organization?.inviteMember({
          emailAddress,
          role: 'org:member',
        })

        void notifications.show({
          title: 'Success!',
          color: 'green',
          message: 'Your teammate has been invited and will receive an email to join your account.',
          position: 'top-right',
          autoClose: 4000,
        })

        void close()
      } catch (err) {
        // biome-ignore lint/suspicious/noExplicitAny: no reason given
        const e = err as any
        if (e.clerkError) {
          console.error([e])
          setReqStatus({ status: 'error', error: e.errors[0].longMessage ?? 'Unknown error' })
        } else {
          console.error(err)
        }
      }
    },
    [clerk.organization, close]
  )

  const form = useForm({
    mode: 'controlled',
    initialValues: {
      email: '',
    },
    validate: {
      email: value => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
    },
  })

  return (
    <Stack gap={8}>
      <form onSubmit={form.onSubmit(values => inviteUser(values.email))}>
        <TextInput
          size="md"
          label="Email"
          placeholder="Email address of the user you want to invite"
          key={form.key('email')}
          {...form.getInputProps('email')}
        />

        {reqStatus.status === 'error' && (
          <Alert mt="sm" key="errorPanel" color="red">
            {reqStatus.error}
          </Alert>
        )}

        <Group key="buttons" justify="space-between" mt="md">
          <Button variant="default" onClick={close}>
            Cancel
          </Button>
          <Button type="submit" loading={reqStatus.status === 'running'} disabled={!form.isValid()}>
            Submit
          </Button>
        </Group>
      </form>
      {/* <Title key="invites" order={2}>
        Invitations
      </Title>
      {invitations.data?.map(invitation => (
        <Fragment key={invitation.email}>
          <Text key={invitation.email}>
            {invitation.email} | {invitation.status}
          </Text>
          <Button onClick={() => revoke(invitation.invitationId)}>Delete</Button>
        </Fragment>
      ))} */}
    </Stack>
  )
}

export const inviteUsersModal = () => {
  const modalId = 'invite-users-modal'

  modals.open({
    modalId,
    title: 'Invite teammates',
    children: <Form close={() => modals.close(modalId)} />,
    centered: true,
  })
}

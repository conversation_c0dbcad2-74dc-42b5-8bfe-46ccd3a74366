import { But<PERSON>, Group, Stack, Text } from '@mantine/core'
import { modals } from '@mantine/modals'
import { notifications } from '@mantine/notifications'
import { IconTrash } from '@tabler/icons-react'
import { useWorkflowTableMutations } from 'mutations/table-mutations'

const TABLE_TRASHED_NOTIFICATION_ID = 'table-trashed'

type TrashTableParams =
  | { type: 'single'; tableName: string; workflowTableId: number }
  | { type: 'multiple'; workflowTableIds: number[] }

export const useTrashTableModal = (onTablesTrashed?: () => void) => {
  const { trashWorkflowTablesMutation, restoreWorkflowTablesMutation } = useWorkflowTableMutations()

  return (params: TrashTableParams) => {
    const notificationId =
      params.type === 'multiple'
        ? `${TABLE_TRASHED_NOTIFICATION_ID}-bulk-${params.workflowTableIds.join('-')}`
        : `${TABLE_TRASHED_NOTIFICATION_ID}-${params.workflowTableId}`

    const handleUndo = () => {
      restoreWorkflowTablesMutation.mutate({
        workflowTableIds: params.type === 'multiple' ? params.workflowTableIds : [params.workflowTableId],
      })
      notifications.hide(notificationId)
    }

    const onSuccess = () => {
      onTablesTrashed?.()

      notifications.show({
        id: notificationId,
        title: params.type === 'multiple' ? 'Tables moved to trash' : 'Table moved to trash',
        message: (
          <Stack gap={8} my={2}>
            <Text c="gray.6">{params.type === 'multiple' ? 'They' : 'It'} will be deleted forever in 30 days</Text>
            <Button variant="outline" style={{ alignSelf: 'flex-start' }} onClick={handleUndo}>
              Undo
            </Button>
          </Stack>
        ),
        color: 'red',
        autoClose: 8000,
      })
    }

    return modals.openConfirmModal({
      title: (
        <Group gap={8}>
          <IconTrash size={24} color="var(--mantine-color-red-5)" />
          <Text fz={18} fw={500}>
            Move to trash
          </Text>
        </Group>
      ),
      centered: true,
      children: (
        <Text span>
          <Text span fw={500}>
            {params.type === 'multiple' ? `${params.workflowTableIds.length} tables` : params.tableName}
          </Text>{' '}
          will be deleted forever after 30 days. You can still view and make copies of tables until they're permanently
          deleted.
        </Text>
      ),
      labels: {
        confirm: (
          <Group gap={8}>
            <IconTrash size={20} />
            Move to trash
          </Group>
        ),
        cancel: 'Cancel',
      },
      confirmProps: { color: 'red.5' },
      groupProps: { justify: 'space-between' },
      onConfirm: () => {
        trashWorkflowTablesMutation.mutate(
          { workflowTableIds: params.type === 'multiple' ? params.workflowTableIds : [params.workflowTableId] },
          { onSuccess }
        )
      },
    })
  }
}

'use client'

import { Button, Group, TextInput } from '@mantine/core'
import { isNotEmpty, useForm } from '@mantine/form'
import { modals } from '@mantine/modals'
import { useWorkflowTableMutations } from 'mutations/table-mutations'
import { trpc } from 'utils/trpc'

export interface UpdateWorkflowTableProps {
  workflowTableId: number
  tableName: string
}

const Form = ({ close, workflowTableId, tableName }: { close: () => void } & UpdateWorkflowTableProps) => {
  const { updateWorkflowTableMutation } = useWorkflowTableMutations()
  const trpcUtils = trpc.useUtils()
  const form = useForm({
    mode: 'uncontrolled',
    initialValues: {
      tableName,
    },
    validate: {
      tableName: isNotEmpty('Table name cannot be empty'),
    },
  })

  const onSubmit = async (values: typeof form.values) => {
    await updateWorkflowTableMutation.mutateAsync(
      {
        ...values,
        workflowTableId,
      },
      { onSuccess: close }
    )
    trpcUtils.workflow.getWorkflowTable.setData(
      { workflowTableId: String(workflowTableId) },
      old =>
        old && {
          ...old,
          workflowTable: {
            ...old.workflowTable!,
            name: values.tableName,
          },
        }
    )
  }

  return (
    <form onSubmit={form.onSubmit(onSubmit)}>
      <TextInput
        size="md"
        withAsterisk
        label="What should we call this table?"
        placeholder="AE's in NYC"
        key={form.key('tableName')}
        {...form.getInputProps('tableName')}
        data-autofocus
      />
      <Group justify="space-between" mt="md">
        <Button variant="default" onClick={close}>
          Cancel
        </Button>
        <Button type="submit" loading={updateWorkflowTableMutation.isPending}>
          Save
        </Button>
      </Group>
    </form>
  )
}

export const renameTableModal = (props: UpdateWorkflowTableProps) => {
  const modalId = 'rename-table-modal'

  modals.open({
    modalId,
    title: 'Rename table',
    children: <Form close={() => modals.close(modalId)} {...props} />,
    centered: true,
  })
}

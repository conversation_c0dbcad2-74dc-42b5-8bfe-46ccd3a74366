import { Button, Stack, Text } from '@mantine/core'

interface NoPreviewResultsFoundProps {
  onEditSearch: () => void
}

export const NoPreviewResultsFound = ({ onEditSearch }: NoPreviewResultsFoundProps) => {
  return (
    <Stack align="center" justify="center" h={200} gap={0}>
      <Text fw={500}>No results found</Text>
      <Text c="dimmed" ta="center" maw={280} mt={4}>
        Add more lookalike URLs or remove filters to expand your search criteria
      </Text>
      <Button size="sm" onClick={onEditSearch} mt={16}>
        Edit search
      </Button>
    </Stack>
  )
}

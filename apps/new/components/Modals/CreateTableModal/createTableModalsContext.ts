import { createModalsStackContext } from 'contexts/ModalStackContext'
import type { CreateTableModalId, CreateTableModalsData } from './types'

const {
  Provider: CreateTableModalsStackProvider,
  Consumer: CreateTableModalsStackConsumer,
  useModalsStackContext: useCreateTableModalsStackContext,
} = createModalsStackContext<CreateTableModalId, CreateTableModalsData>()

export { CreateTableModalsStackProvider, CreateTableModalsStackConsumer, useCreateTableModalsStackContext }

export const splitString = (value: string, separator = '\n') =>
  value
    .split(separator)
    .filter(Boolean)
    .map(value => value.trim())

export const toLowerCase = (values?: string[]) => values?.map(value => value.toLowerCase()) ?? []

export const toUpperCase = (values?: string[]) => values?.map(value => value.toUpperCase()) ?? []

export const countFilters = (values: (string | string[])[]) =>
  values.reduce((acc, value) => acc + (value.length > 0 ? 1 : 0), 0)

export const convertToNumberRange = (range?: { from?: string | number; to?: string | number }) => {
  if (range?.from && range?.to) {
    return {
      from: Number(range.from),
      to: Number(range.to),
    }
  }
  return undefined
}

export const isValidUrl = (string?: string) => {
  if (!string) {
    return false
  }

  try {
    // Try parsing with original string first
    try {
      // Check for spaces
      if (/\s/.test(string)) {
        return false
      }

      new URL(string)

      return true
    } catch {
      // If that fails, try prepending https:// and parsing again
      new URL(`https://${string}`)
      return true
    }
  } catch (err) {
    return false
  }
}

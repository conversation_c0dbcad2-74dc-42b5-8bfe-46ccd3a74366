import { Button, type ButtonProps } from '@mantine/core'
import type { IconProps } from '@tabler/icons-react'
import type { ForwardRefExoticComponent } from 'react'
import classes from './SidebarNavButton.module.css'

interface SidebarNavButtonProps extends ButtonProps {
  Icon?: ForwardRefExoticComponent<IconProps>
  count?: number
  onClick?: () => void
  active?: boolean
}

export const SidebarNavButton = ({
  Icon,
  count,
  onClick,
  active = false,
  children,
  ...props
}: SidebarNavButtonProps) => {
  return (
    <Button
      variant="transparent"
      size="md"
      fullWidth
      onClick={onClick}
      classNames={{
        root: classes.root,
        inner: classes.inner,
        label: classes.label,
        section: classes.section,
      }}
      leftSection={Icon ? <Icon size={16} className={classes.icon} /> : null}
      rightSection={<span>{count ?? 0}</span>}
      justify="flex-start"
      data-active={active}
      data-has-count={count && count > 0}
      {...props}
    >
      {children}
    </Button>
  )
}

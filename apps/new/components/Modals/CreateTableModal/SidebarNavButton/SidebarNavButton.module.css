.root {
  padding: 4px 8px 4px 4px;
  border-radius: var(--mantine-radius-xs);
  border: 1px solid var(--mantine-color-gray-2);
  transition: border-color 150ms ease-in-out, background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--mantine-color-gray-0);
  }

  &[data-active='true'] {
    border: 1px solid var(--mantine-color-purple-5);
  }
}

.label {
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-gray-9);
}

/* Icon */
.section[data-position='left'] {
  padding: 4px;
  background-color: var(--mantine-color-gray-1);
  border-radius: 2px;

  .icon {
    color: var(--mantine-color-gray-9);
  }

  .root[data-active='true'] & {
    background-color: var(--mantine-color-purple-0);

    .icon {
      color: var(--mantine-color-purple-5);
    }
  }
}

/* Counter */
.section[data-position='right'] {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  background-color: var(--mantine-color-gray-1);
  border-radius: var(--mantine-radius-xl);
  font-size: 10px;
  color: var(--mantine-color-gray-6);

  .root[data-has-count='true'] & {
    color: var(--mantine-color-gray-9);
  }
}

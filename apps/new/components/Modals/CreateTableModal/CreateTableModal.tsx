import { Button, Group, Modal, type ModalProps, Pill, Stack, Text } from '@mantine/core'
import { IconBuilding, IconFileUpload, type IconProps, IconTable, IconWebhook } from '@tabler/icons-react'
import googleMapsLogo from 'images/google-maps-logo.svg'
import { useWorkflowTableMutations } from 'mutations/table-mutations'
import Image from 'next/image'
import type { ElementType, ForwardRefExoticComponent } from 'react'
import { useMemo } from 'react'
import { logoFor } from 'utils/logo'
import { useCreateTableModalsStackContext } from './createTableModalsContext'
import { useCreateBlankTable } from './hooks'
import { CreateTableModalId } from './types'

interface Action {
  Icon: ForwardRefExoticComponent<IconProps> | ElementType
  label: string
  subtitle?: string
  isBeta?: boolean
  isNew?: boolean
  action: () => void
  isLoading?: () => boolean
  phCaptureAttribute?: string
}

interface CreateTableModalProps extends ModalProps {}

const HubspotIcon = () => logoFor('hubspot.com', 24)
const GoogleMapsImage = () => <Image src={googleMapsLogo} alt="Google Maps" width={24} height={24} />

export const CreateTableModal = (props: CreateTableModalProps) => {
  const { stack } = useCreateTableModalsStackContext()
  const { addWorkflowTableMutation } = useWorkflowTableMutations()
  const createBlankTable = useCreateBlankTable()

  const actions: Action[] = useMemo(
    () => [
      {
        Icon: HubspotIcon,
        label: 'Import from HubSpot',
        subtitle: 'Import objects from HubSpot - one-time or with automatic syncing',
        action: () => stack.open(CreateTableModalId.ONE_OR_RECURRING),
        isNew: true,
        isBeta: false,
        phCaptureAttribute: 'hubspot',
      },
      {
        Icon: IconWebhook,
        label: 'Inbound webhooks',
        subtitle: 'Receive data from external sources',
        action: () => stack.open(CreateTableModalId.INBOUND_WEBHOOKS),
        isNew: false,
        isBeta: false,
        phCaptureAttribute: 'webhook',
      },
      {
        Icon: IconTable,
        label: 'Start from scratch',
        subtitle: 'Create a blank table you can customize and build however you like',
        action: createBlankTable,
        isLoading: () => addWorkflowTableMutation.isPending,
        phCaptureAttribute: 'blank',
      },
      {
        Icon: IconFileUpload,
        label: 'Upload a CSV file',
        subtitle: 'Bring in your data from a spreadsheet',
        action: () => stack.open(CreateTableModalId.UPLOAD_CSV),
        phCaptureAttribute: 'csv',
      },
      {
        Icon: IconBuilding,
        label: 'Find company lookalikes',
        subtitle: 'Build a lead list by finding companies similar to ones you already work with',
        action: () => stack.open(CreateTableModalId.COMPANY_LOOKALIKES_BUILD),
        phCaptureAttribute: 'ocean-company-lookalikes',
      },
      {
        Icon: GoogleMapsImage,
        label: 'Search local businesses',
        subtitle: 'Use Google Maps to find and add businesses based on location and category',
        action: () => stack.open(CreateTableModalId.GOOGLE_MAPS_SEARCH_BUILD),
        isNew: false,
        phCaptureAttribute: 'google-maps-places',
      },
    ],
    [createBlankTable, addWorkflowTableMutation, stack]
  )

  return (
    <Modal
      title="Create new table"
      size={640}
      padding={24}
      styles={{
        header: {
          paddingBottom: 16,
        },
      }}
      {...props}
    >
      <Stack gap={8}>
        {actions.map(({ Icon, label, subtitle, action, isLoading, isBeta, isNew, phCaptureAttribute }) => (
          <Button
            key={label}
            variant="default"
            size="xl"
            h="auto"
            p={15}
            fullWidth
            leftSection={<Icon size={20} />}
            justify="flex-start"
            onClick={action}
            loading={isLoading?.()}
            data-ph-capture-attribute-feature={phCaptureAttribute}
          >
            <Stack gap={4} align="flex-start">
              <Group gap={4}>
                <Text size="md" fw={500}>
                  {label}
                </Text>
                {isBeta ? (
                  <Pill size="xs" c="gray.6">
                    BETA
                  </Pill>
                ) : null}
                {isNew ? (
                  <Pill size="xs" c="blue.6">
                    NEW
                  </Pill>
                ) : null}
              </Group>
              {subtitle ? (
                <Text size="md" c="dimmed">
                  {subtitle}
                </Text>
              ) : null}
            </Stack>
          </Button>
        ))}
      </Stack>
    </Modal>
  )
}

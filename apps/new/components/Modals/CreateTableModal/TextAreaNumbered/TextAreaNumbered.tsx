import { Box, Group, Textarea, type TextareaProps } from '@mantine/core'
import { IconAsterisk } from '@tabler/icons-react'
import inputClasses from '@upbound/ui-kit/theme/styles/Input.module.css'
import { useCallback, useRef } from 'react'
import classes from './TextAreaNumbered.module.css'

interface TextAreaNumberedProps extends TextareaProps {
  height?: number
  requiredFirstLinesCount?: number
}

export const TextAreaNumbered = ({ height, requiredFirstLinesCount, ...props }: TextAreaNumberedProps) => {
  const lineCount = props.maxRows ?? 10
  const lineNumbers = Array.from({ length: lineCount }, (_, i) => i + 1)
  const ref = useRef<HTMLTextAreaElement>(null)

  const scrollToStart = useCallback(() => ref.current?.scrollTo({ left: 0 }), [])

  return (
    <Box pos="relative">
      <Box
        className={classes.lineNumbers}
        style={{
          height: height ? height - 2 : undefined,
          fontFamily: 'var(--mantine-font-family-monospace)',
        }}
      >
        {lineNumbers.map(num => (
          <Group gap={2} key={num} wrap="nowrap">
            <Box component="span">{num}</Box>
            {requiredFirstLinesCount && num <= requiredFirstLinesCount ? (
              <IconAsterisk size={8} color="var(--mantine-color-red-5)" />
            ) : null}
          </Group>
        ))}
      </Box>
      <Textarea
        ref={ref}
        classNames={{
          input: classes.input + ' ' + inputClasses.input,
        }}
        styles={{
          input: {
            height,
            whiteSpace: props.value ? 'nowrap' : 'pre-line', // fixes placeholder new lines not showing
          },
        }}
        {...props}
        onChange={e => {
          const lines = e.target.value.split('\n')

          if (e.nativeEvent instanceof InputEvent && e.nativeEvent.inputType === 'insertFromPaste') {
            // Insert new line on paste if it's not already there
            if (!e.target.value.endsWith('\n') && lines.length < lineCount) {
              e.target.value += '\n'
            } else {
              // Limit the number of lines to the max line count
              e.target.value = lines.slice(0, lineCount).join('\n')
            }

            scrollToStart()
            props.onChange?.(e)
          } else {
            // Only allow changes if the number of lines is within the max line count
            if (lines.length <= lineCount) {
              props.onChange?.(e)
            }
          }
        }}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            scrollToStart()
          }
        }}
      />
    </Box>
  )
}

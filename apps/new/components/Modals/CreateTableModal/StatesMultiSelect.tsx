import {
  MultiSelectWithCheckbox,
  type MultiSelectWithCheckboxProps,
} from '@upbound/ui-kit/components/MultiSelectWithCheckbox'
import { iso31661 as countries, iso31662 as states } from 'iso-3166'
import { useMemo } from 'react'

export interface StatesMultiSelectProps extends MultiSelectWithCheckboxProps {
  countryCodes: string[]
}

const countryCodeToCountry = Object.fromEntries(countries.map(country => [country.alpha2, country]))

export const StatesMultiSelect = ({ countryCodes = ['US'], ...props }: StatesMultiSelectProps) => {
  const countryCodesUpperCase = countryCodes.map(code => code.toUpperCase())
  const stateOptions = useMemo(
    () =>
      countryCodesUpperCase.map(countryCode => ({
        group: countryCodeToCountry[countryCode]!.name,
        items: states
          .filter(state => state.parent === countryCode)
          .map(state => ({
            value: state.code,
            label: state.name,
          })),
      })),
    [countryCodesUpperCase]
  )

  return <MultiSelectWithCheckbox data={stateOptions} size="sm" {...props} autoComplete="nope" />
}

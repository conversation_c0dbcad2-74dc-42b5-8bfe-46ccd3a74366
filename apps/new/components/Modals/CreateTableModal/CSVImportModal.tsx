'use client'

import { ActionIcon, Button, Group, Modal, type ModalProps, rem, Stack, Text } from '@mantine/core'
import type { FileWithPath } from '@mantine/dropzone'
import { Dropzone, MIME_TYPES } from '@mantine/dropzone'
import { IconFileTypeCsv, IconTrash, IconUpload, IconX } from '@tabler/icons-react'
import { isNonEmpty, type NonEmptyArray } from '@upbound/utils'
import { parse as parseCsv } from 'csv-parse'
import { useCallback, useEffect, useState } from 'react'
import { useCreateTableModalsStackContext } from './createTableModalsContext'
import { useCreateTableFromCsv } from './hooks'

const MAX_FILE_SIZE = 10 * 1024 ** 2

function bytesToSize(bytes: number) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Byte'
  const i = Number.parseInt(String(Math.floor(Math.log(bytes) / Math.log(1024))))
  return Math.round(bytes / Math.pow(1024, i)) + ' ' + sizes[i]
}

type ParseCsvResult =
  | {
      status: 'invalid'
      detail: string
    }
  | {
      status: 'valid'
      columns: NonEmptyArray<string>
      rows: string[][]
    }
  | {
      status: 'empty'
    }

const parseCsvFile = async (file: FileWithPath): Promise<ParseCsvResult> => {
  const buf = await file.arrayBuffer()

  return new Promise<ParseCsvResult>(resolve => {
    parseCsv(
      Buffer.from(buf),
      {
        relax_quotes: true,
        skipEmptyLines: true,
      },
      (err, parsed: string[][]) => {
        if (err) {
          return resolve({ status: 'invalid' as const, detail: err.message })
        }

        if (isNonEmpty(parsed) && isNonEmpty(parsed[0])) {
          const columns = parsed[0]
          const rows = parsed.slice(1)

          return resolve({
            status: 'valid' as const,
            columns,
            rows,
          })
        } else {
          return resolve({ status: 'empty' as const })
        }
      }
    )
  })
}

const ImportCsvDropZone = ({ setFile }: { setFile: (file: FileWithPath) => void }) => {
  return (
    <Dropzone accept={[MIME_TYPES.csv]} onDrop={files => setFile(files[0]!)} maxSize={MAX_FILE_SIZE} multiple={false}>
      <Stack
        justify="center"
        align="center"
        gap={8}
        style={{ border: '1px dashed var(--mantine-color-gray-3)', borderRadius: rem(8), cursor: 'pointer' }}
        p="md"
      >
        <Dropzone.Accept>
          <IconUpload style={{ width: rem(24), height: rem(24), color: 'var(--mantine-color-black)' }} stroke={1.5} />
        </Dropzone.Accept>
        <Dropzone.Reject>
          <IconX style={{ width: rem(24), height: rem(24), color: 'var(--mantine-color-red-6)' }} stroke={1.5} />
        </Dropzone.Reject>
        <Dropzone.Idle>
          <IconFileTypeCsv
            style={{ width: rem(24), height: rem(24), color: 'var(--mantine-color-black)' }}
            stroke={1.5}
          />
        </Dropzone.Idle>

        <Text size="md" span>
          Upload CSV
        </Text>
        <Text size="md" c="dimmed" span>
          Drop CSV to upload. File should not exceed {bytesToSize(MAX_FILE_SIZE)}.
        </Text>
        <Button size="sm" variant="filled">
          Select file
        </Button>
      </Stack>
    </Dropzone>
  )
}

const ImportCsvFilePreview = ({
  file,
  errorMsg,
  removeFile,
}: {
  file: FileWithPath
  errorMsg: string | null
  removeFile: () => void
}) => {
  return (
    <Group
      align="center"
      p="md"
      style={{
        background: errorMsg ? 'var(--mantine-color-red-0)' : 'var(--mantine-color-purple-0)',
        borderRadius: 'var(--mantine-radius-md)',
        border: errorMsg ? '1px solid var(--mantine-color-red-5)' : '1px solid var(--mantine-color-purple-5)',
      }}
      justify="space-between"
    >
      <div>
        <Text size="sm" truncate="end">
          {file.name}
        </Text>
        {errorMsg ? (
          <Text c="red.5" size="sm" truncate="end">
            {errorMsg}
          </Text>
        ) : (
          <Text c="dimmed" size="sm">
            {bytesToSize(file.size)}
          </Text>
        )}
      </div>

      <ActionIcon variant="transparent" onClick={removeFile}>
        <IconTrash size="1rem" />
      </ActionIcon>
    </Group>
  )
}

export interface CSVImportModalProps extends ModalProps {}

export const CSVImportModal = (props: CSVImportModalProps) => {
  const { stack } = useCreateTableModalsStackContext()
  const [file, setFile] = useState<FileWithPath | null>(null)
  const [csvErrorMessage, setCsvErrorMessage] = useState<string | null>(null)
  const { createFromCsv, isCreating } = useCreateTableFromCsv()

  useEffect(() => {
    if (!file) {
      setCsvErrorMessage(null)
      return
    }

    parseCsvFile(file)
      .then(parsed => {
        switch (parsed.status) {
          case 'empty':
            setCsvErrorMessage('Upload failed. The CSV file is empty')
            break
          case 'invalid':
            setCsvErrorMessage(`Upload failed. Invalid CSV. ${parsed.detail}`)
            break
          case 'valid':
            setCsvErrorMessage(null)
            break
        }
      })
      .catch(() => {
        setCsvErrorMessage('Upload failed. Unknown error.')
      })
  }, [file])

  const onClick = useCallback(() => {
    if (csvErrorMessage === null && file) {
      createFromCsv(file)
    }
  }, [csvErrorMessage, createFromCsv, file])

  return (
    <Modal
      title="Upload CSV"
      size={640}
      padding={24}
      styles={{
        header: {
          paddingBottom: 16,
        },
      }}
      {...props}
    >
      <Stack gap={16}>
        {file && <ImportCsvFilePreview file={file} errorMsg={csvErrorMessage} removeFile={() => setFile(null)} />}
        {!file && <ImportCsvDropZone setFile={setFile} />}
        <Group justify="space-between" mt="md">
          <Button variant="default" onClick={stack.closeAll}>
            Cancel
          </Button>
          <Button disabled={!file || !!csvErrorMessage || isCreating} onClick={onClick} loading={isCreating}>
            Create table
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

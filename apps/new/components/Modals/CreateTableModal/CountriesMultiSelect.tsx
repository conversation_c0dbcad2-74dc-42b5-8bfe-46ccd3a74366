import type { MultiSelectProps } from '@mantine/core'
import { MultiSelectWithCheckbox } from '@upbound/ui-kit/components/MultiSelectWithCheckbox'
import { iso31661 as countries } from 'iso-3166'

const countryOptions = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'au', label: 'Australia' },
  { value: 'gb', label: 'United Kingdom' },
  ...countries
    .filter(country => !['US', 'AU', 'GB', 'CA'].includes(country.alpha2))
    .map(country => ({
      value: country.alpha2.toLowerCase(),
      label: country.name,
    })),
]

export const CountriesMultiSelect = (props: MultiSelectProps) => (
  <MultiSelectWithCheckbox data={countryOptions} size="sm" {...props} autoComplete="nope" />
)

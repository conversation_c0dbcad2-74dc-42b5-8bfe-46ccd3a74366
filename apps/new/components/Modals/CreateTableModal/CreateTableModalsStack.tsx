import { Modal, useModalsStack } from '@mantine/core'
import type { ModalsStackContextValue } from 'contexts/ModalStackContext'
import type { ReactNode } from 'react'
import { CreateTableModal } from './CreateTableModal'
import { CSVImportModal } from './CSVImportModal'
import { CreateTableModalsStackProvider } from './createTableModalsContext'
import { DiscardChangesModal } from './DiscardChangesModal'
import { CreateTableModalId, type CreateTableModalsData } from './types'

const modalsIds = Object.values(CreateTableModalId)

export interface CreateTableModalsStackProps
  extends Omit<CreateTableModalsData, 'previewPeopleLookalikeMutation' | 'previewCompanyLookalikeMutation'> {
  children: (stack: ModalsStackContextValue<CreateTableModalId>['stack']) => ReactNode
}

export const CreateTableModalsStack = ({ children, ...modalsData }: CreateTableModalsStackProps) => {
  const stack = useModalsStack<CreateTableModalId>(modalsIds)

  return (
    <CreateTableModalsStackProvider
      value={{
        stack,
        data: {
          ...modalsData,
        },
      }}
    >
      <Modal.Stack>
        <CreateTableModal {...stack.register(CreateTableModalId.ENTRY_POINT)} />
        <CSVImportModal {...stack.register(CreateTableModalId.UPLOAD_CSV)} />
        <DiscardChangesModal
          {...stack.register(CreateTableModalId.DISCARD_CHANGES)}
          onCloseWithoutSaving={() => void 0}
        />
      </Modal.Stack>

      {children(stack)}
    </CreateTableModalsStackProvider>
  )
}

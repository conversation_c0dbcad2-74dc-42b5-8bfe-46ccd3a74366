import { Button, Group, Modal, type ModalProps, Stack, Text } from '@mantine/core'
import { useCallback } from 'react'
import { useCreateTableModalsStackContext } from './createTableModalsContext'

export interface DiscardChangesModalProps extends ModalProps {
  onCloseWithoutSaving?: () => void
}

export const DiscardChangesModal = ({ onCloseWithoutSaving, ...props }: DiscardChangesModalProps) => {
  const { stack } = useCreateTableModalsStackContext()

  const closeWithoutSaving = useCallback(() => {
    onCloseWithoutSaving?.()
    stack.closeAll()
  }, [onCloseWithoutSaving, stack])

  return (
    <Modal {...props} title="Discard changes?" centered size="md">
      <Stack gap="lg">
        <Text>
          Your changes have not been saved. Are you sure you want to close this search? Your progress will be lost.
        </Text>

        <Group gap="xs" justify="space-between">
          <Button variant="outline" onClick={props.onClose}>
            Continue building list
          </Button>
          <Button color="red" onClick={closeWithoutSaving}>
            Close without saving
          </Button>
        </Group>
      </Stack>
    </Modal>
  )
}

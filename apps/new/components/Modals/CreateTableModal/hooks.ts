import { useAuth } from '@clerk/nextjs'
import type { FileWithPath } from '@mantine/dropzone'
import { useMutation } from '@tanstack/react-query'
import { blankTableColumnDefinitions, defaultRows } from 'config/new-table-defaults'
import { useWorkflowTableMutations } from 'mutations/table-mutations'
import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { API_SERVER, trpc } from 'utils/trpc'

const defaultTableName = 'New table'

export const useCreateBlankTable = () => {
  const router = useRouter()
  const { addWorkflowTableMutation } = useWorkflowTableMutations()

  const createBlankTable = useCallback(
    () =>
      addWorkflowTableMutation
        .mutateAsync({
          tableName: defaultTableName,
          columnDefinitions: blankTableColumnDefinitions,
          rows: defaultRows,
        })
        .then(async data => {
          router.push(`/tables/${data.workflowTable.table.workflowTableId}`)
        }),
    [addWorkflowTableMutation, router]
  )

  return createBlankTable
}

export const useCreateTableFromCsv = () => {
  const trpcUtils = trpc.useUtils()
  const router = useRouter()
  const { getToken } = useAuth()

  const { mutateAsync: createFromCsv, isPending: isCreating } = useMutation({
    mutationFn: async (csvFile: FileWithPath) => {
      const url = `${API_SERVER}/upload-csv`
      const body = new FormData()
      body.append('csv', csvFile)

      const res = await fetch(url, {
        headers: {
          Authorization: `Bearer ${await getToken()}`,
        },
        method: 'POST',
        body,
      })
      if (res.status !== 200) {
        throw new Error('CSV upload failed')
      }
      return res.text()
    },
    onSuccess: workflowTableId => {
      trpcUtils.workflow.listWorkflowTables.invalidate()
      router.push(`/tables/${workflowTableId}`)
    },
  })

  return {
    createFromCsv,
    isCreating,
  }
}

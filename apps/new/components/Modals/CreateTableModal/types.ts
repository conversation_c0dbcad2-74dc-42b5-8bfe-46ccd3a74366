export enum CreateTableModalId {
  ENTRY_POINT = 'create-new-table-entry-point',
  BLANK_TABLE = 'blank-table',
  PEOPLE_LOOKALIKES_BUILD = 'people-lookalikes-build',
  PEOPLE_LOOKALIKES_PREVIEW = 'people-lookalikes-preview',
  COMPANY_LOOKALIKES_BUILD = 'company-lookalikes-build',
  COMPANY_LOOKALIKES_PREVIEW = 'company-lookalikes-preview',
  GOOGLE_MAPS_SEARCH_BUILD = 'google-maps-search-build',
  GOOGLE_MAPS_SEARCH_PREVIEW = 'google-maps-search-preview',
  UPLOAD_CSV = 'upload-csv',
  INBOUND_WEBHOOKS = 'inbound-webhooks',
  HUBSPOT_EVENTS = 'hubspot-events',
  ONE_OR_RECURRING = 'one-or-recurring',
  HUBSPOT_AUTH = 'hubspot-auth',
  IMPORT_SETTINGS = 'import-settings',
  DISCARD_CHANGES = 'discard-changes',
}

export type CreateTableModalsData = {
  workflowTableId?: string
}

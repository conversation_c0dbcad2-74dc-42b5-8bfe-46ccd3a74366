import { Loader, Stack, type StackProps, Text, Transition } from '@mantine/core'
import { useEffect, useState } from 'react'

export interface PreviewLoaderProps extends StackProps {
  messages?: string[]
}

const animationDuration = 200

export const PreviewLoader = ({ messages = [], ...props }: PreviewLoaderProps) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)
  const [mounted, setMounted] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      // First unmount the current message
      setMounted(false)

      // After the unmount animation, change the message and mount the new one
      setTimeout(() => {
        setCurrentMessageIndex(prev => (prev + 1) % messages.length)
        setMounted(true)
      }, animationDuration)
    }, 3500)

    return () => clearInterval(interval)
  }, [messages.length])

  return (
    <Stack align="center" justify="center" gap={16} p={40} {...props}>
      <Loader size={40} />
      <Transition mounted={mounted} transition="fade" duration={animationDuration} timingFunction="ease">
        {styles => (
          <Text style={{ ...styles, textAlign: 'center' }} size="sm" c="dimmed">
            {messages[currentMessageIndex]}
          </Text>
        )}
      </Transition>
    </Stack>
  )
}

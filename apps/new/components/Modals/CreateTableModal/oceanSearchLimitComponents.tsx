import { NumberInput, type NumberInputProps, Pill, type PillProps } from '@mantine/core'
import { Label, type LabelProps } from '@upbound/ui-kit/components/Label'

export const OceanSearchLimitLabel = (props: LabelProps) => (
  <Label
    size="sm"
    tooltip="Specify the maximum number of people to include in your search results, up to 5,000. Each result uses 3 credits."
    {...props}
  />
)

export const OceanSearchLimitNumberInput = (props: NumberInputProps) => (
  <NumberInput
    size="md"
    w={80}
    min={1}
    max={5000}
    clampBehavior="strict"
    rightSection={<span />}
    rightSectionWidth={0}
    {...props}
  />
)

export const OceanSearchLimitPill = ({ limit, ...props }: PillProps & { limit: number }) => (
  <Pill size="sm" {...props}>
    {3 * limit} credits
  </Pill>
)

import {
  IconAlignJustified,
  IconAt,
  IconBaseline,
  IconCurrencyDollar,
  IconHash,
  IconLink,
  IconListCheck,
  IconPhone,
  type IconProps,
  IconSelect,
  IconSquareCheck,
} from '@tabler/icons-react'
import type { AgentColumnTypeKey } from '@upbound/shared/agents'
import { FreckleIcon, type FreckleIconProps } from '@upbound/ui-kit/components/FreckleIcon/FreckleIcon'
import type { ForwardRefExoticComponent } from 'react'

export const agentTypeIconMap: Record<AgentColumnTypeKey | 'unconfigured', ForwardRefExoticComponent<IconProps>> = {
  singleLineText: IconBaseline,
  unconfigured: IconBaseline,
  multiLineText: IconAlignJustified,
  email: IconAt,
  number: IconHash,
  url: IconLink,
  phoneNumber: IconPhone,
  select: IconSelect,
  multiSelect: IconListCheck,
  boolean: IconSquareCheck,
  currency: IconCurrencyDollar,
}

export const AgentTypeIcon = ({
  type,
  size = 12,
  withWrapper = true,
  freckleIconProps,
}: {
  type: AgentColumnTypeKey | 'unconfigured'
  size?: number
  withWrapper?: boolean
  freckleIconProps?: FreckleIconProps
}) => {
  const Icon = agentTypeIconMap[type]

  return withWrapper ? (
    <FreckleIcon Icon={Icon} color="gray" dimmed size={size} wrapperSize={16} radius={4} {...freckleIconProps} />
  ) : (
    <Icon size={size} />
  )
}

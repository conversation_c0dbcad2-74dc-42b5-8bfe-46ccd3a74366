import { memo } from 'react'

const formatter = new Intl.RelativeTimeFormat('en', { numeric: 'auto' })

const toSentenceCase = (str: string) => str.charAt(0).toUpperCase() + str.slice(1)

function formatRelativeTimeString(then: Date) {
  const now = new Date()
  const diffSeconds = (then.getTime() - now.getTime()) / 1000

  const units: [Intl.RelativeTimeFormatUnit, number][] = [
    ['year', 60 * 60 * 24 * 365],
    ['month', 60 * 60 * 24 * 30],
    ['week', 60 * 60 * 24 * 7],
    ['day', 60 * 60 * 24],
    ['hour', 60 * 60],
    ['minute', 60],
    ['second', 1],
  ]

  for (const [unit, secondsInUnit] of units) {
    const delta = diffSeconds / secondsInUnit
    if (Math.abs(delta) >= 1) {
      return toSentenceCase(formatter.format(Math.round(delta), unit))
    }
  }

  return 'Just now'
}
export const RelativeDateFormat = memo(({ date }: { date: Date | string }) =>
  formatRelativeTimeString(date instanceof Date ? date : new Date(date))
)
RelativeDateFormat.displayName = 'RelativeDateFormat'

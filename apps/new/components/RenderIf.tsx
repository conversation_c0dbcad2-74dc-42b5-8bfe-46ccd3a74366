import { useAuth } from '@clerk/nextjs'
import type { ReactNode } from 'react'
import { useIsFreckleEmployee } from 'utils/auth'

export const RenderIfEngineer = (props: { children: ReactNode }) => {
  const { actor } = useAuth()
  const isFreckleEmployee = useIsFreckleEmployee()

  if ((actor?.sub || isFreckleEmployee) && localStorage.getItem('isEngineer') === '1') {
    return <>{props.children}</>
  } else {
    return null
  }
}

export const RenderIfAdmin = (props: { children: ReactNode }) => {
  const { orgRole } = useAuth()

  if (orgRole === 'admin') {
    return <>{props.children}</>
  } else {
    return null
  }
}

export const RenderIfInternal = (props: { children: ReactNode }) => {
  const { actor } = useAuth()
  const isFreckleEmployee = useIsFreckleEmployee()

  if (actor?.sub || isFreckleEmployee) {
    return <>{props.children}</>
  } else {
    return null
  }
}

export const RenderIfAdminOrInternal = (props: { children: ReactNode }) => {
  const { actor, orgRole } = useAuth()
  const isFreckleEmployee = useIsFreckleEmployee()

  if (actor?.sub || isFreckleEmployee || orgRole === 'admin') {
    return <>{props.children}</>
  } else {
    return null
  }
}

export const RenderIfImpersonating = (props: { children: ReactNode }) => {
  const { actor } = useAuth()

  if (actor?.sub) {
    return <>{props.children}</>
  } else {
    return null
  }
}

import { Group, Text, type TextProps, Transition } from '@mantine/core'
import { IconLoader, type IconProps } from '@tabler/icons-react'
import type { ReactNode } from 'react'

export interface LoadingIndicatorProps {
  loading?: boolean
  children?: ReactNode
  textProps?: TextProps
  iconProps?: IconProps
  size?: 'xxs' | 'xs' | 'sm'
}

const sizes = {
  xxs: { iconSize: 12, textSize: 'xs', gap: 2 },
  xs: { iconSize: 14, textSize: 'xs', gap: 2 },
  sm: { iconSize: 16, textSize: 'sm', gap: 4 },
}

export const LoadingIndicator = ({ loading, children, textProps, iconProps, size = 'sm' }: LoadingIndicatorProps) => (
  <Transition mounted={loading ?? false} transition="fade">
    {styles => (
      <Group gap={sizes[size].gap} style={styles}>
        <IconLoader
          size={sizes[size].iconSize}
          color="var(--mantine-color-gray-6)"
          style={{ animation: 'spin 1.5s linear infinite', transformOrigin: 'center' }}
          {...iconProps}
        />
        {children ? (
          <Text c="dimmed" fs="italic" fz={sizes[size].textSize} {...textProps}>
            {children}
          </Text>
        ) : null}
      </Group>
    )}
  </Transition>
)

import { Group, Radio, Text } from '@mantine/core'
import classes from './RadioGroup.module.css'

interface RadioGroupProps {
  value: string
  onChange: (v: string) => void
  items: {
    label: string
    description: string
    value: string
  }[]
}

export const RadioGroup = ({ value, onChange, items }: RadioGroupProps) => {
  return (
    <Radio.Group value={value} onChange={onChange}>
      <Group justify="space-between" wrap="nowrap">
        {items.map(item => (
          <Radio.Card key={item.value} className={classes.root} value={item.value} w={286}>
            <Group wrap="nowrap" align="flex-start">
              <Radio.Indicator />
              <div>
                <Text className={classes.label}>{item.label}</Text>
                <Text className={classes.description}>{item.description}</Text>
              </div>
            </Group>
          </Radio.Card>
        ))}
      </Group>
    </Radio.Group>
  )
}

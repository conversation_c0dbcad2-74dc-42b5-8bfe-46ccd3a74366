{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "root": true, "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false, "defaultBranch": "dev"}, "files": {"includes": ["**/apps/**/*.ts", "**/apps/**/*.tsx", "**/packages/**/*.ts", "**/packages/**/*.tsx", "!**/debugChatHistory.ts", "!**/node_modules", "!**/build", "!**/dist", "!**/.next"], "maxSize": 10000000}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120, "lineEnding": "lf", "formatWithErrors": false}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "error", "noUselessStringConcat": "error", "noUselessSwitchCase": "error", "useLiteralKeys": "off", "useWhile": "error"}, "correctness": {"noPrivateImports": "error", "noRenderReturnValue": "error", "noUnusedFunctionParameters": "off", "noUnusedImports": "error", "noUnusedPrivateClassMembers": "off", "noUnusedVariables": "off", "useExhaustiveDependencies": {"level": "error", "options": {"hooks": [{"name": "useForm", "stableResult": true}]}}, "useJsxKeyInIterable": "error"}, "nursery": {"noMisusedPromises": "error", "noNestedComponentDefinitions": "error", "noNoninteractiveElementInteractions": "error", "noReactPropAssign": "error", "noRestrictedElements": "error", "noTsIgnore": "error", "noUnassignedVariables": "error", "noUselessEscapeInString": "error", "useConsistentObjectDefinition": "error", "useExhaustiveSwitchCases": "error", "useIndexOf": "error", "useIterableCallbackReturn": "error", "useNumericSeparators": "error", "useObjectSpread": "error", "useUniqueElementIds": "off", "useUnifiedTypeSignature": "error"}, "performance": {"noDelete": "error", "noImgElement": "off"}, "style": {"noUnusedTemplateLiteral": "off", "useTemplate": "off", "noUselessElse": "off", "useExponentiationOperator": "off", "noNonNullAssertion": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "useConsistentCurlyBraces": "off"}, "suspicious": {"noAlert": "error", "noEvolvingTypes": "error", "noVar": "error"}}}, "javascript": {"formatter": {"semicolons": "asNeeded", "quoteStyle": "single", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto", "trailingCommas": "es5"}}}
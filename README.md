# Superday - Product Engineer

The goal of this task is to build an onboarding experience for <PERSON><PERSON><PERSON> using the API(s) available in this project :

**Designs** : https://www.figma.com/design/cCj2cyJn3hDqMj6aoZ7Tf8/Vlad---Onboarding-designs---24.08.25?node-id=0-1&t=9F6ibaV8ox6r2ADQ-1
**Password**: super random password

## Goal

The designs are quite detailed. However, the goal is to build a working MVP **up-to the end of Step 2** including actually running our agent to resolve results as shown in **Step 2** of the designs.

- Build this on the `/onboarding` route.
- Focus on building an end-to-end working MVP and identify any blockers as early as possible so that you can get them resolved.
- Ideally, try to build the UI as faithfully as possible, adding any animations or transitions that you feel can improve the UX. ( Hint: <PERSON><PERSON> has a `Transition` module)
- Use an AI tools that you prefer.

## Relevant API methods ( mapped against designs )
Step 1a
- Create or retrieve a reference to an existing onboarding table when a user loads `/onboarding`. Reference : `WorkflowRouter -> addOnboardingTable/resolveOnboardingTable`.

Step 1b
- Generate a unique id for each of the potentially 5 inputs the user can provide using randomUUID. ( You'll need this later ).
- "Fill with sample data" can just fill it with hard-coded data points that you can come up with.

Step 2b
- Column input box : When typing something into the column definition input box, it should call `AgentRouter -> evalAiAgentCell` to generate the column name and compute a column type.

- Run Column : When run is hit,
  - use `WorkflowRouter -> addColumn` to add the column.
  - use `WorkflowRouter -> addRow` to add the collected data-points as individual rows.
  - use `WorkflowRouter -> run` to run the actual cells to start generating the result.
  - Use 1-second polling against `WorkflowRouter -> getRows` to get the updated state for each cell and render the result.


## Project Details

- The App uses Mantine components.
- The API is a `tRPC` server using zod schemas for inputs
- The App communicates with `tRPC` using the classic react-query integration detailed here : https://trpc.io/docs/client/react

## Core dependencies

- Node.JS v20
- Postgres 16
- Redis 7

## Package Management

We use `pnpm`.

```sh
corepack enable
corepack prepare --activate
pnpm i
pnpm build

# Updating packages across the codebase
pnpm upgrade --interactive --recursive
```

## Local Postgres

If you're on a Mac, using **Postgres for Mac** is recommended.
Alternatively, you can use docker.

```sh
docker run --name superday-postgres -e POSTGRES_DB=superday -e POSTGRES_USER=upbound -e POSTGRES_PASSWORD=upbound -p 5432:5432 -d postgres
```

Then **run migrations** from the root folder:

```sh
pnpm migrate
```

Once you have the environment variables set-up for clerk, also run this to enrich your local users table

```sh
cd apps/api
pnpm backfillClerkUsersToDb
```

## Local Redis

```sh
brew install redis
brew services start redis
```

Alternatively, you can use docker.

```sh
docker run --name freckle-redis -p 6379:6379 -d redis
```

### Run

```sh
pnpm dev
```

Web app should be running at http://localhost:3000

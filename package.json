{"private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev --filter=@app/new... --filter=@app/api... --filter=@app/worker...", "dev:api": "turbo run dev --filter=@app/api...", "dev:all": "turbo run dev --filter=@app/api... --filter=@app/new... --filter=@app/worker... --filter=@app/webhooks...", "dev:worker": "turbo run dev --filter=@app/worker...", "migrate": "pnpm run --filter=@app/api migrate", "format-and-lint": "biome check .", "format-and-lint:fix": "biome check . --write", "test": "turbo run test", "storybook": "turbo run storybook --filter=@upbound/ui-kit", "storybook:build": "turbo run build-storybook --filter=@upbound/ui-kit"}, "devDependencies": {"@biomejs/biome": "^2.1.2", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417", "engines": {"node": ">=20.0.0"}, "name": "upbound", "workspaces": ["apps/*", "packages/*"]}
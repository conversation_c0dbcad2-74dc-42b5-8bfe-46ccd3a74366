# Superday Database User Setup

This guide explains how to create a user for the Superday database and grant all necessary permissions.

## Database Configuration

Based on the project configuration:
- **Database name**: `superday`
- **Default admin user**: `upbound`
- **Default admin password**: `upbound`
- **Connection string**: `postgres://upbound:upbound@localhost:5432/superday`

## Option 1: Using the SQL Script

1. **Run the SQL script directly**:
   ```bash
   # Connect to PostgreSQL as the admin user
   psql -h localhost -p 5432 -U upbound -d superday -f create-superday-user.sql
   ```

2. **Customize the script** by editing `create-superday-user.sql`:
   - Change `superday_user` to your desired username
   - Change `your_secure_password` to a secure password

## Option 2: Manual Commands

Connect to PostgreSQL and run these commands:

```sql
-- Connect to the superday database
\c superday

-- Create the user
CREATE USER your_username WITH PASSWORD 'your_secure_password';

-- Grant all privileges
GRANT CONNECT ON DATABASE superday TO your_username;
GRANT USAGE ON SCHEMA public TO your_username;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_username;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_username;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO your_username;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO your_username;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO your_username;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO your_username;
GRANT CREATE ON SCHEMA public TO your_username;
```

## Option 3: Using Docker

If you're using the Docker setup from the project:

```bash
# Start the PostgreSQL container
docker run --name superday-postgres -e POSTGRES_DB=superday -e POSTGRES_USER=upbound -e POSTGRES_PASSWORD=upbound -p 5432:5432 -d postgres

# Execute the SQL script in the container
docker exec -i superday-postgres psql -U upbound -d superday < create-superday-user.sql
```

## Verification

After creating the user, verify the setup:

```sql
-- Check if user exists
SELECT usename FROM pg_user WHERE usename = 'your_username';

-- Check user privileges
\du your_username

-- Test connection with new user
\c superday your_username
```

## Security Considerations

1. **Use strong passwords**: Replace `your_secure_password` with a strong, unique password
2. **Limit privileges**: If you don't need all privileges, consider granting only specific ones:
   - `SELECT, INSERT, UPDATE, DELETE` for data operations
   - `CREATE` for creating new objects
   - `USAGE` for schema access

3. **Environment-specific users**: Consider creating different users for different environments (dev, staging, prod)

## Connection String Format

After creating the user, your connection string will be:
```
postgres://your_username:your_secure_password@localhost:5432/superday
```

Update your `.env.local` file accordingly:
```
POSTGRES_URL=postgres://your_username:your_secure_password@localhost:5432/superday
```

import { MantineProvider } from '@mantine/core'
import { cssVariablesResolver, theme } from '../src/theme'

import '@mantine/core/styles.css'
import '@mantine/notifications/styles.css'

export const decorators = [
  Story => (
    <MantineProvider theme={theme} cssVariablesResolver={cssVariablesResolver}>
      <Story />
    </MantineProvider>
  ),
]

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
}

export default preview

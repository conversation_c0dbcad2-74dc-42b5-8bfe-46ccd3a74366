.input {
  border-color: var(--mantine-color-gray-2);
  border-radius: 6px;

  &:active,
  &:focus-within {
    border: 1px solid var(--mantine-color-purple-5);
    box-shadow: var(--mantine-shadow-input-outline) !important;
  }

  &[data-error='true'] {
    border-color: var(--mantine-color-red-5);

    &::placeholder {
      color: var(--mantine-color-gray-6);
    }
  }
}

.section {
  color: var(--mantine-color-gray-9);
}

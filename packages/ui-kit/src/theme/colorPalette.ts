import type { FreckleColor, FreckleColorPalette } from './types'

export const getColorPalette = (color: FreckleColor, dimmed?: boolean): FreckleColorPalette => {
  if (color === 'gray' && dimmed) {
    return {
      strokeColor: 'gray.9',
      borderColor: 'gray.2',
      backgroundColor: 'gray.0',
    }
  }

  return {
    strokeColor: `${color}.9`,
    borderColor: `${color}.${color === 'gray' ? '5' : '2'}`, // special case for gray palette
    backgroundColor: `${color}.0`,
  }
}

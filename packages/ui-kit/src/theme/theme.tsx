'use client'

import type {
  CSSVariablesResolver,
  DefaultMantineColor,
  InputProps,
  MantineColorsTuple,
  MantineTheme,
  MantineThemeOverride,
  MultiSelectProps,
  TagsInputProps,
} from '@mantine/core'
import {
  ActionIcon,
  Anchor,
  Autocomplete,
  Button,
  createTheme,
  DEFAULT_THEME,
  Divider,
  Input,
  InputWrapper,
  Modal,
  ModalTitle,
  MultiSelect,
  NavLink,
  Notification,
  NumberInput,
  Paper,
  rem,
  Select,
  Switch,
  Table,
  TagsInput,
  Text,
  Textarea,
  ThemeIcon,
  Title,
  Tooltip,
} from '@mantine/core'
import Link from 'next/link' // TODO(mbek<PERSON><PERSON>ov): Pass Link as an external argument to the theme
import { InterFont, RobotoMonoFont } from '../fonts/GoogleFonts'
import InputClasses from './styles/Input.module.css'

const gray = [
  '#F9FAFA',
  '#F4F5F5',
  '#ECEDED',
  '#DEDFDF',
  '#BBBCBC',
  '#9C9D9D',
  '#737474',
  '#5F6060',
  '#404141',
  '#1F2020',
] as const

const purple = [
  '#F3EDFF',
  '#E3D6FB',
  '#C2AAF1',
  '#A07CE8',
  '#8354E0',
  '#713BDB',
  '#672EDA',
  '#5721C2',
  '#4C1CAE',
  '#411699',
] as const

const blue = [
  '#E6F2FF',
  '#D0E0FF',
  '#A0BFFC',
  '#6E9BF6',
  '#437DF2',
  '#276AF0',
  '#1460F0',
  '#0251D6',
  '#0048C0',
  '#003DAB',
] as const

const green = [
  '#F0FAF6',
  '#E2F1EB',
  '#BEE3D4',
  '#98D5BC',
  '#79C9A8',
  '#64C19A',
  '#59BD94',
  '#49A77F',
  '#3D9471',
  '#2D8060',
] as const

const red = [
  '#FFEAEC',
  '#FDD4D6',
  '#F4A7AC',
  '#EC777E',
  '#E64F57',
  '#E3353F',
  '#E22732',
  '#C91A25',
  '#B31220',
  '#9E0419',
] as const

const orange = [
  '#FFF4E2',
  '#FFE9CC',
  '#FFD09C',
  '#FDB766',
  '#FCA13A',
  '#FB931D',
  '#FC8C0C',
  '#E17900',
  '#C86A00',
  '#AE5A00',
] as const

const yellow = [
  '#FFFAE1',
  '#FDF4CD',
  '#F8E79F',
  '#F5DA6D',
  '#F2CF43',
  '#F0C827',
  '#EFC416',
  '#D4AC03',
  '#BD9900',
  '#A38400',
] as const

const colors = {
  gray,
  purple,
  blue,
  green,
  red,
  orange,
  yellow,
} as const

const ValidSizes = new Set(['xs', 'sm', 'md', 'lg', 'xl'])

const inputVars = (_theme: MantineTheme, props: InputProps | TagsInputProps | MultiSelectProps) => {
  if (props.size === 'sm') {
    return {
      wrapper: {
        '--input-height': rem(24),
        '--input-fz': rem(12),
      },
      input: {
        '--input-radius': rem(4),
      },
    }
  }

  if (props.size === 'md') {
    return {
      wrapper: {
        '--input-height': rem(32),
        '--input-fz': rem(14),
      },
      input: {
        '--input-radius': rem(6),
      },
    }
  }

  if (props.size === 'xl') {
    return {
      wrapper: {
        '--input-height': rem(40),
        '--input-fz': rem(16),
      },
      input: {
        '--input-radius': rem(8),
      },
    }
  }

  return { wrapper: {} }
}

export const theme: MantineThemeOverride = createTheme({
  /* Put your mantine theme override here */
  activeClassName: '',
  cursorType: 'pointer',
  fontFamily: InterFont.style.fontFamily,
  fontFamilyMonospace: RobotoMonoFont.style.fontFamily,
  headings: {
    fontFamily: `${InterFont.style.fontFamily}, ${DEFAULT_THEME.fontFamily}`,
    fontWeight: '500',
    sizes: {
      h2: { fontSize: rem('18'), lineHeight: rem('22') },
    },
  },
  components: {
    NavLink: NavLink.extend({
      defaultProps: {
        fz: 'sm',
        fw: 500,
      },
      styles: {
        body: {
          // pushes down text 1px for 2px smaller font - keeps center align
          marginTop: 1,
        },
        section: {
          // reduce distance between icon and label
          marginInlineEnd: 8,
        },
      },
    }),
    Text: Text.extend({
      defaultProps: {
        c: 'gray.9',
      },
    }),
    Paper: Paper.extend({
      defaultProps: {
        styles: {
          root: {
            '--paper-shadow': 'var(--mantine-shadow-floating)',
          },
        },
      },
    }),
    Tooltip: Tooltip.extend({
      defaultProps: {
        closeDelay: 200,
      },
    }),
    Modal: Modal.extend({
      defaultProps: {
        c: 'gray.9',
        size: '560px',
        shadow: 'var(--mantine-shadow-floating)',
      },
    }),
    ModalTitle: ModalTitle.extend({
      defaultProps: {
        fz: rem('18'),
        lh: rem('22'),
        c: 'gray.9',
        fw: 500,
      },
    }),
    Title: Title.extend({
      defaultProps: {
        c: 'gray.9',
        fw: 500,
      },
    }),
    Textarea: Textarea.extend({
      defaultProps: {
        classNames: InputClasses,
        labelProps: {
          c: 'gray.9',
        },
      },
    }),
    Divider: Divider.extend({
      defaultProps: {
        color: 'gray.2',
        size: 'xs',
      },
    }),
    Anchor: Anchor.extend({
      defaultProps: {
        // @ts-expect-error - The types say `component` cannot be extended. But this does work.
        component: Link,
      },
    }),
    ThemeIcon: ThemeIcon.extend({
      defaultProps: {
        size: 'md',
      },
      vars: (_theme, props) => {
        if (typeof props.size === 'string' && ValidSizes.has(props.size)) {
          const gteMd = props.size === 'lg' || props.size === 'xl'
          return {
            root: {
              '--ti-size': `var(--custom-component-height-${props.size ?? 'md'})`,
              '--ti-radius': `var(--mantine-radius-${gteMd ? 'md' : props.size === 'md' ? 'sm' : (props.size ?? 'sm')})`,
            },
          }
        }

        return { root: {} }
      },
    }),
    ActionIcon: ActionIcon.extend({
      defaultProps: {
        size: 'md',
      },
      vars: (_theme, props) => {
        if (typeof props.size === 'string' && ValidSizes.has(props.size)) {
          const gteMd = props.size === 'lg' || props.size === 'xl'
          return {
            root: {
              '--ai-size': `var(--custom-component-height-${props.size ?? 'md'})`,
              '--ai-radius': `var(--mantine-radius-${gteMd ? 'md' : props.size === 'md' ? 'sm' : (props.size ?? 'sm')})`,
            },
          }
        }

        return { root: {} }
      },
    }),
    Button: Button.extend({
      defaultProps: {
        fw: 500,
        size: 'md',
      },
      vars: (_theme, props) => {
        let root: Record<string, string> = {}
        const gteMd = props.size === 'lg' || props.size === 'xl'
        const bigSizes = {
          '--button-height': `var(--custom-component-height-${props.size ?? 'md'})`,
          '--button-radius': `var(--mantine-radius-${gteMd ? 'md' : props.size === 'md' ? 'sm' : (props.size ?? 'sm')})`,
          '--button-padding-x': `var(--custom-component-padding-${props.size ?? 'md'})`,
          // Spacing between leftSection->label and label->rightSection uses `--mantine-spacing-xs` internally in Button
          // Therefore, we're just overriding that variable here just for this use-case.
          '--mantine-spacing-xs': `var(--custom-button-gap-${props.size ?? 'md'})`,
        }

        if (typeof props.size === 'string' && ValidSizes.has(props.size)) {
          root = { ...root, ...bigSizes }
        }

        if (props.variant === 'outline') {
          root = {
            ...root,
            '--button-bd': '1px solid var(--mantine-color-gray-3)',
            '--button-bg': 'var(--mantine-color-gray-0)',
            '--button-color': 'var(--mantine-color-gray-9)',
          }
        }

        if (props.variant === 'white') {
          root['--button-hover'] = 'var(--mantine-color-gray-1)'
        }

        return { root }
      },
    }),
    TagsInput: TagsInput.extend({
      defaultProps: {
        classNames: InputClasses,
        styles: {
          label: {
            marginBottom: rem(4),
          },
        },
      },
      vars: inputVars,
    }),
    InputWrapper: InputWrapper.extend({
      defaultProps: {
        styles: {
          label: {
            color: 'var(--mantine-color-gray-9)',
            fontWeight: 500,
          },
        },
      },
      vars: (_theme, props) => {
        if (props.size === 'sm') {
          return {
            label: {
              '--input-label-size': rem('12'),
              lineHeight: rem('16'),
              marginBottom: rem(2),
            },
            error: {},
            description: {},
          }
        }

        if (props.size === 'md') {
          return {
            label: {
              '--input-label-size': rem('14'),
              lineHeight: rem('16'),
              marginBottom: rem(2),
            },
            error: {},
            description: {},
          }
        }

        if (props.size === 'xl') {
          return {
            label: {
              '--input-label-size': rem('16'),
              lineHeight: rem('20'),
              marginBottom: rem(2),
            },
            error: {},
            description: {},
          }
        }

        return { label: {}, error: {}, description: {} }
      },
    }),
    Input: Input.extend({
      defaultProps: {
        classNames: InputClasses,
      },
      vars: inputVars,
    }),
    Notification: Notification.extend({
      defaultProps: {
        radius: 'lg',
        withCloseButton: false,
        withBorder: true,
      },
      styles(theme) {
        return {
          title: {
            fontSize: theme.fontSizes.md,
          },
          description: {
            fontSize: theme.fontSizes.md,
          },
        }
      },
    }),
    NumberInput: NumberInput.extend({
      defaultProps: {
        classNames: InputClasses,
        styles: {
          label: {
            marginBottom: rem(4),
          },
        },
      },
    }),
    MultiSelect: MultiSelect.extend({
      defaultProps: {
        classNames: InputClasses,
      },
      vars: inputVars,
    }),
    Select: Select.extend({
      defaultProps: {
        radius: 'xs',
        // rightSection: <IconSelector size={16} />,
        // rightSectionPointerEvents: 'none',
        comboboxProps: { offset: 3 },
        checkIconPosition: 'right',
        classNames: InputClasses,
        styles: {
          dropdown: {
            border: '1px solid var(--mantine-color-gray-2)',
            boxShadow: 'var(--mantine-shadow-floating)',
          },
          option: {
            fontSize: 'var(--mantine-font-size-sm)',
            fontWeight: 400,
          },
          label: {
            marginBottom: rem(4),
          },
        },
        autoComplete: 'off',
        'data-1p-ignore': true,
        'data-lpignore': true,
        'data-form-type': 'other',
        'data-bwignore': true,
      },
    }),
    Autocomplete: Autocomplete.extend({
      defaultProps: {
        styles: {
          input: {
            border: '1px solid var(--mantine-color-gray-2)',
          },
        },
      },
    }),
    Switch: Switch.extend({
      defaultProps: {
        size: 'xs',
      },
      vars: (_theme, props) => {
        if (props.size === 'xs') {
          return {
            root: {
              '--switch-width': rem('28'),
              '--label-offset-start': rem('8'),
            },
          }
        }

        return {
          root: {},
        }
      },
    }),
    Table: Table.extend({
      styles: {
        table: {
          '--table-border-color': 'var(--mantine-color-gray-2)',
          '--table-highlight-on-hover-color': 'var(--mantine-color-gray-0)',
        },
      },
    }),
  },
  scale: 1,
  fontSizes: {
    xs: rem('10'),
    sm: rem('12'),
    md: rem('14'),
    lg: rem('16'),
    xl: rem('18'),
  },
  lineHeights: {
    xs: rem('14'),
    sm: rem('16'),
    md: rem('16'),
    lg: rem('20'),
    xl: rem('22'),
  },
  radius: {
    xs: rem('4'),
    sm: rem('6'),
    md: rem('8'),
    lg: rem('16'),
    xl: rem('24'),
  },
  defaultRadius: 'sm',
  colors,
  primaryColor: 'purple',
  primaryShade: {
    light: 5,
    dark: 5,
  },
  shadows: {
    floating:
      '0px 0px 2px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.04), 0px 4px 8px 0px rgba(0, 0, 0, 0.04), 0px 6px 24px 0px rgba(0, 0, 0, 0.04);',
    'input-outline': '0px 0px 0px var(--freckle-input-outline-size) #F3EDFF',
  },
})

export const cssVariablesResolver: CSSVariablesResolver = _theme => {
  return {
    variables: {
      '--custom-component-height-xs': rem('16'),
      '--custom-component-height-sm': rem('24'),
      '--custom-component-height-md': rem('32'),
      '--custom-component-height-lg': rem('40'),
      '--custom-component-height-xl': rem('48'),
      '--custom-component-padding-xs': rem('4'),
      '--custom-component-padding-sm': rem('8'),
      '--custom-component-padding-md': rem('8'),
      '--custom-component-padding-lg': rem('16'),
      '--custom-component-padding-xl': rem('16'),
      '--custom-button-gap-xs': rem('4'),
      '--custom-button-gap-sm': rem('4'),
      '--custom-button-gap-md': rem('8'),
      '--custom-button-gap-lg': rem('8'),
      '--custom-button-gap-xl': rem('16'),
      '--freckle-input-outline-size': rem('2'),
      '--overlay-bg': 'rgba(31, 32, 32, 0.32)',
      '--popover-shadow': 'var(--mantine-shadow-floating)',
    },
    dark: {
      '--mantine-color-default-border': gray[2],
    },
    light: {
      '--mantine-color-default-border': gray[2],
    },
  }
}

declare module '@mantine/core' {
  type AdditionalColors = keyof typeof colors
  export interface MantineThemeColorsOverride {
    colors: Record<AdditionalColors | DefaultMantineColor, MantineColorsTuple>
  }
}

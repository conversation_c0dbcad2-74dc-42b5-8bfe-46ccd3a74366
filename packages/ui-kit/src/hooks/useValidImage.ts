import { useEffect, useState } from 'react'

/**
 * Custom hook to validate an image URL.
 * @param {string} imageUrl - The image URL to validate.
 * @returns {string | null} - The valid image URL or null if invalid.
 */
export const useValidImage = (imageUrl?: string | null) => {
  const [validImageUrl, setValidImageUrl] = useState<string | null>(null)

  useEffect(() => {
    if (!imageUrl) {
      setValidImageUrl(null)
      return
    }

    const img = new Image()
    const handleLoad = () => setValidImageUrl(imageUrl)
    const handleError = () => setValidImageUrl(null)

    img.onload = handleLoad
    img.onerror = handleError
    img.src = imageUrl

    // Cleanup listeners when component unmounts or URL changes
    return () => {
      img.onload = null
      img.onerror = null
    }
  }, [imageUrl])

  return validImageUrl
}

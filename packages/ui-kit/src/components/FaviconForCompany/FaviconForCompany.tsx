import { Image, type ImageProps } from '@mantine/core'
import { IconBuilding } from '@tabler/icons-react'
import { type FC, memo, type ReactNode } from 'react'
import { useValidImage } from '../../hooks/useValidImage'
import { type FaviconSize, getFaviconForCompanyUrl } from './getFaviconForCompanyUrl'

interface FaviconForCompanyProps extends Omit<ImageProps, 'src' | 'alt'> {
  domain: string
  size?: number
  faviconSize?: FaviconSize
  fallbackImage?: ReactNode
}

export const FaviconForCompany: FC<FaviconForCompanyProps> = memo(
  ({ domain, size = 16, faviconSize = 64, fallbackImage = <IconBuilding size={size} />, ...props }) => {
    const faviconUrl = useValidImage(getFaviconForCompanyUrl(domain, faviconSize))

    return faviconUrl ? (
      <Image src={faviconUrl} alt={`Icon for ${domain}`} w={size} h={size} {...props} />
    ) : (
      fallbackImage
    )
  }
)

import { Group, InputLabel, type InputLabelProps, Text, Tooltip } from '@mantine/core'
import { IconInfoCircleFilled } from '@tabler/icons-react'
import type { PropsWithChildren } from 'react'

export interface LabelProps extends PropsWithChildren<InputLabelProps> {
  optional?: boolean
  tooltip?: string
}

export const Label = ({ optional, children, tooltip, ...props }: LabelProps) => {
  return (
    <Group gap={4} wrap="nowrap" mb={4}>
      <InputLabel fw={500} mb={0} {...props}>
        {children}
        {optional ? (
          <Text span c="dimmed" size={props.size}>
            &nbsp;– Optional
          </Text>
        ) : null}
      </InputLabel>
      {tooltip ? (
        <Tooltip label={tooltip} inline multiline styles={{ tooltip: { maxWidth: 256 } }}>
          <IconInfoCircleFilled size={12} color="var(--mantine-color-gray-4)" style={{ cursor: 'pointer' }} />
        </Tooltip>
      ) : null}
    </Group>
  )
}

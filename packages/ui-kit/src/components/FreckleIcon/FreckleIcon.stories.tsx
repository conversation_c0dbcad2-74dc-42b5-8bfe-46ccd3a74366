import { Stack } from '@mantine/core'
import type { <PERSON>a, StoryObj } from '@storybook/react'
import {
  IconApi,
  IconCalendarTime,
  IconCheck,
  IconHash,
  IconLetterT,
  IconQuestionMark,
  IconSparkles,
} from '@tabler/icons-react'
import { FreckleIcon } from './FreckleIcon'

const meta: Meta<typeof FreckleIcon> = {
  title: 'Components/FreckleIcon',
  component: FreckleIcon,
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    color: { control: 'color' },
  },
}

export default meta
type Story = StoryObj<typeof FreckleIcon>

export const SampleIcons: Story = {
  render: args => (
    <Stack gap="xs">
      {[IconLetterT, IconHash, IconCheck, IconQuestionMark, IconSparkles, IconCalendarTime, IconApi].map(
        (Icon, index) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: no reason given
          <FreckleIcon key={index} {...args} Icon={Icon} />
        )
      )}
    </Stack>
  ),
  args: {
    color: 'blue',
    size: 'sm',
  },
}

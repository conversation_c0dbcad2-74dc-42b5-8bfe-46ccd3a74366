import { type MantineSize, ThemeIcon, type ThemeIconProps } from '@mantine/core'
import type { IconProps } from '@tabler/icons-react'
import type { CSSProperties, ForwardRefExoticComponent } from 'react'
import { getColorPalette } from '../../theme/colorPalette'
import type { FreckleColor, FreckleColorPalette } from '../../theme/types'

export interface FreckleIconProps extends ThemeIconProps {
  Icon: ForwardRefExoticComponent<IconProps>
  color: FreckleColor
  colorPalette?: FreckleColorPalette
  size?: MantineSize | number | string
  wrapperSize?: ThemeIconProps['size']
  dashed?: boolean
  dimmed?: boolean
  withBorder?: boolean
  radius?: CSSProperties['borderRadius']
}

const iconSizes: Record<MantineSize, number> = {
  xs: 10,
  sm: 12,
  md: 16,
  lg: 20,
  xl: 24,
}

export const FreckleIcon = ({
  Icon,
  color,
  colorPalette,
  size = 'xs',
  wrapperSize = size,
  dashed = false,
  dimmed = false,
  radius,
  withBorder = true,
  ...props
}: FreckleIconProps) => {
  const { strokeColor, borderColor, backgroundColor } = colorPalette ?? getColorPalette(color, dimmed)

  return (
    <ThemeIcon
      size={wrapperSize}
      c={strokeColor}
      bd={withBorder ? `1px ${dashed ? 'dashed' : 'solid'} ${borderColor}` : undefined}
      bg={backgroundColor}
      radius={radius}
      {...props}
    >
      <Icon size={typeof size === 'string' && size in iconSizes ? iconSizes[size as MantineSize] : size} />
    </ThemeIcon>
  )
}

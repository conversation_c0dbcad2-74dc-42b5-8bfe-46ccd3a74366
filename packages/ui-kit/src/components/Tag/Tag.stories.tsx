import { Stack } from '@mantine/core'
import type { Meta, StoryObj } from '@storybook/react'
import { IconAdjustments, IconAlarm, IconAnchor, IconApps } from '@tabler/icons-react'
import { Tag } from './Tag'

const meta: Meta<typeof Tag> = {
  title: 'Components/Tag',
  component: Tag,
  argTypes: {
    color: {
      control: 'select',
      options: ['gray', 'purple', 'blue', 'green', 'red', 'orange', 'yellow'],
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg'],
    },
    variant: {
      control: 'radio',
      options: ['default', 'dashed'],
    },
  },
}

export default meta
type Story = StoryObj<typeof Tag>

export const MultipleTagsDemo: Story = {
  render: ({ color, size, variant }) => (
    <Stack gap="xs" align="flex-start">
      <Tag color={color} Icon={IconAdjustments} size={size} variant={variant}>
        Adjustments Tag
      </Tag>
      <Tag color={color} Icon={IconAlarm} size={size} variant={variant}>
        Alarm Tag
      </Tag>
      <Tag color={color} Icon={IconAnchor} size={size} variant={variant}>
        Anchor Tag
      </Tag>
      <Tag color={color} Icon={IconApps} size={size} variant={variant}>
        Apps Tag
      </Tag>
    </Stack>
  ),
  args: {
    color: 'blue',
    size: 'md',
    variant: 'default',
  },
}

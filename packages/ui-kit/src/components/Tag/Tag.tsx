import { Group, type GroupProps, type MantineStyleProps, Text, type TextProps } from '@mantine/core'
import type { IconProps } from '@tabler/icons-react'
import type { CSSProperties, ForwardRefExoticComponent, PropsWithChildren } from 'react'
import { forwardRef } from 'react'
import { getColorPalette } from '../../theme/colorPalette'
import type { FreckleColor } from '../../theme/types'

type TagSize = 'xs' | 'sm' | 'md' | 'lg'

export interface TagProps extends PropsWithChildren<GroupProps> {
  size?: TagSize
  color?: FreckleColor
  variant?: 'default' | 'dashed'
  Icon?: ForwardRefExoticComponent<IconProps>
  iconColor?: string
  cursor?: CSSProperties['cursor']
  textProps?: TextProps
  withBorder?: boolean
}

const tagStyles: Record<TagSize, MantineStyleProps & { iconSize: number }> = {
  xs: { px: 4, py: 0, fz: 10, iconSize: 8 },
  sm: { px: 4, py: 0, fz: 10, iconSize: 12 },
  md: { px: 4, py: 2, fz: 12, iconSize: 12 },
  lg: { px: 4, py: 4, fz: 14, iconSize: 16 },
}

export const Tag = forwardRef<HTMLDivElement, TagProps>(
  (
    {
      children,
      Icon,
      color = 'blue',
      iconColor,
      variant = 'default',
      cursor,
      textProps,
      size = 'md',
      withBorder = true,
      style,
      ...props
    },
    ref
  ) => {
    const { strokeColor, borderColor, backgroundColor } = getColorPalette(color)
    const { h, px, py, fz, iconSize } = tagStyles[size]

    return (
      <Group
        component="span"
        display="inline-flex"
        w="fit-content"
        gap={4}
        px={px}
        py={py}
        bd={`${withBorder ? `1px ${variant === 'dashed' ? 'dashed' : 'solid'} ${borderColor}` : 'none'}`}
        bg={backgroundColor}
        c={strokeColor}
        style={{ borderRadius: 'var(--mantine-radius-xs)', cursor, ...style }}
        ref={ref}
        {...props}
      >
        {Icon && <Icon size={iconSize} color={iconColor} />}
        <Text span fw={400} fz={fz} lh={withBorder ? '14px' : '16px'} c={strokeColor} {...textProps}>
          {children}
        </Text>
      </Group>
    )
  }
)

Tag.displayName = 'Tag'

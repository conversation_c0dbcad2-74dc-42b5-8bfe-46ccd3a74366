import { Checkbox, Group, MultiSelect, type MultiSelectProps } from '@mantine/core'
import inputClasses from '../../theme/styles/Input.module.css'
import { Label } from '../Label'
import classes from './MultiSelectWithCheckbox.module.css'

export interface MultiSelectWithCheckboxProps extends MultiSelectProps {
  tooltip?: string
}

export const MultiSelectWithCheckbox = ({ tooltip, ...props }: MultiSelectWithCheckboxProps) => {
  return (
    <MultiSelect
      searchable
      clearable
      renderOption={({ option }) => (
        <Group wrap="nowrap" justify="space-between" w="100%">
          <Label fz="sm">{option.label}</Label>
          <Checkbox
            checked={props.value?.includes(option.value)}
            onChange={() => {}}
            style={{ pointerEvents: 'none' }}
            size="xs"
            radius="xs"
          />
        </Group>
      )}
      comboboxProps={{
        offset: 2,
      }}
      styles={{
        input: {
          borderRadius: 6,
        },
        dropdown: {
          boxShadow: 'var(--mantine-shadow-floating)',
        },
        pill: {
          '--pill-height': '18px',
          '--pill-fz': '10px',
        },
      }}
      classNames={{
        label: classes.label,
        pill: classes.pill,
        input: inputClasses.input,
      }}
      {...props}
      label={<Label tooltip={tooltip}>{props.label}</Label>}
    />
  )
}

MultiSelectWithCheckbox.withProps = (props: Partial<MultiSelectWithCheckboxProps>) => {
  return (nestedProps: MultiSelectWithCheckboxProps) => <MultiSelectWithCheckbox {...props} {...nestedProps} />
}

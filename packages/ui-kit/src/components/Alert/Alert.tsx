import { Center, Group, Text, type TextProps } from '@mantine/core'
import { IconAlertTriangle, IconInfoCircle, type IconProps } from '@tabler/icons-react'
import type { ForwardRefExoticComponent, PropsWithChildren } from 'react'
import type { FreckleColor } from '../../theme/types'
import { getColorPalette } from '../FreckleIcon'

export interface AlertProps extends PropsWithChildren {
  type: 'info' | 'warning' | 'error'
  textProps?: TextProps
}

const colorMap: Record<AlertProps['type'], FreckleColor> = {
  info: 'blue',
  warning: 'orange',
  error: 'red',
}

const iconMap: Record<AlertProps['type'], ForwardRefExoticComponent<IconProps> | null> = {
  info: IconInfoCircle,
  warning: IconAlertTriangle,
  error: null, // TODO: add error icon
}

export const Alert = ({ children, type, textProps }: AlertProps) => {
  const { strokeColor, backgroundColor, borderColor } = getColorPalette(colorMap[type])
  const Icon = iconMap[type]

  return (
    <Group
      p={8}
      gap={4}
      align="flex-start"
      wrap="nowrap"
      bd={`1px solid ${borderColor}`}
      bg={backgroundColor}
      style={{ borderRadius: 4 }}
    >
      {Icon && (
        <Center component="span" c={strokeColor} mt={1}>
          <Icon size={12} />
        </Center>
      )}
      <Text span c={strokeColor} fz="sm" {...textProps}>
        {children}
      </Text>
    </Group>
  )
}

export default Alert

import { ScrollArea, type <PERSON>rollAreaProps, Stack, type StackProps } from '@mantine/core'
import type { FC } from 'react'

interface ScrollableContentWithInputOutlineFixProps extends StackProps {
  scrollAreaProps?: ScrollAreaProps
}

// Apply negative margin equal to the input outline size to the
// scrollable parent, then restore it with padding. This fixes
// box-shadow (input outline) clipping in `overflow: scroll` containers.
export const ScrollableContentWithInputOutlineFix: FC<ScrollableContentWithInputOutlineFixProps> = ({
  children,
  scrollAreaProps,
  ...props
}) => {
  return (
    <ScrollArea type="never" m="calc(var(--freckle-input-outline-size) * -1)" {...scrollAreaProps}>
      <Stack p="var(--freckle-input-outline-size)" {...props}>
        {children}
      </Stack>
    </ScrollArea>
  )
}

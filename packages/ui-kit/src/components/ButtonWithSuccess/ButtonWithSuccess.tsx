import { useProps } from '@mantine/core'
import { isPromise } from 'radash'
import { useCallback, useState } from 'react'

export interface ButtonWithSuccessProps {
  /** Children callback, provides current status and copy function as an argument */
  children: (payload: { actioned: boolean; onClick: () => void }) => React.ReactNode
  onClick: () => void | Promise<void>
  /** Success status timeout in ms **/
  timeout?: number
}

const defaultProps: Partial<ButtonWithSuccessProps> = {
  timeout: 1500,
}

export function ButtonWithSuccess(props: ButtonWithSuccessProps) {
  const { children, timeout, onClick: propsOnClick, ...others } = useProps('ButtonWithSuccess', defaultProps, props)
  const [actioned, setActioned] = useState(false)
  const [actionTimeout, setActionTimeout] = useState<number | null>(null)

  const handleActionResult = useCallback(
    (value: boolean) => {
      window.clearTimeout(actionTimeout!)
      setActionTimeout(window.setTimeout(() => setActioned(false), timeout))
      setActioned(value)
    },
    [timeout, actionTimeout]
  )

  const onClick = useCallback(() => {
    const res = propsOnClick()

    if (isPromise(res)) {
      res.then(() => handleActionResult(true))
    } else {
      handleActionResult(true)
    }
  }, [propsOnClick, handleActionResult])

  return <>{children({ actioned, onClick, ...others })}</>
}

ButtonWithSuccess.displayName = 'ButtonWithSuccess'

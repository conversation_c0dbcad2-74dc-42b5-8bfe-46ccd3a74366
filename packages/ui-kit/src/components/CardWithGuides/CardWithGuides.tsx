import { Card, CardSection, type CardSectionProps } from '@mantine/core'
import type { FC, PropsWithChildren, ReactNode } from 'react'

export interface CardWithGuidesProps extends PropsWithChildren<Omit<CardSectionProps, 'top' | 'bottom'>> {
  top?: ReactNode
  bottom?: ReactNode
}

export const CardWithGuides: FC<CardWithGuidesProps> = ({ top, bottom, children, ...props }) => {
  return (
    <Card shadow="none" padding="none" radius="md" withBorder>
      {top && (
        <CardSection p={8} bg="gray.0" withBorder>
          {top}
        </CardSection>
      )}
      <CardSection withBorder {...props}>
        {children}
      </CardSection>
      {bottom && <CardSection p={8}>{bottom}</CardSection>}
    </Card>
  )
}

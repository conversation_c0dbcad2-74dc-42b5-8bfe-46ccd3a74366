import { Box, Group, Image, Text } from '@mantine/core'
import type { Meta, StoryObj } from '@storybook/react'
import { IconSparkles } from '@tabler/icons-react'
import { FreckleIcon } from '../FreckleIcon'
import { CardWithGuides } from './CardWithGuides'

const meta: Meta<typeof CardWithGuides> = {
  title: 'Components/CardWithGuides',
  component: CardWithGuides,
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof CardWithGuides>

const decorator = (Story: React.ComponentType) => (
  <Box maw={400}>
    <Story />
  </Box>
)

const children = <Image src="https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/images/bg-7.png" />

const topGuide = (
  <Group gap={4}>
    <Text size="md" fw="bold">
      Top guide
    </Text>
    <FreckleIcon Icon={IconSparkles} color="purple" borderColor="gray.2" bg="white" size="sm" />
  </Group>
)

export const Default: Story = {
  decorators: [decorator],
  args: {
    top: topGuide,
    bottom: <div>Bottom guide</div>,
    children,
  },
}

export const WithoutTopGuide: Story = {
  decorators: [decorator],
  args: {
    bottom: <div>Bottom guide only</div>,
    children,
  },
}

export const WithoutBottomGuide: Story = {
  decorators: [decorator],
  args: {
    top: topGuide,
    children,
  },
}

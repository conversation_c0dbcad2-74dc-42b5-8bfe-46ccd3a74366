import { Text, type TextProps } from '@mantine/core'
import type { PropsWithChildren } from 'react'

export interface ActionTextProps extends PropsWithChildren<TextProps> {
  onClick?: () => void
}

export const ActionText = ({ children, onClick, ...props }: ActionTextProps) => {
  return (
    <Text span fz="sm" fw={500} td="underline" style={{ cursor: 'pointer' }} tabIndex={0} onClick={onClick} {...props}>
      {children}
    </Text>
  )
}

import { Button, type ButtonProps, Group, type GroupProps } from '@mantine/core'
import type { ReactNode } from 'react'
import classNames from './ToggleGroup.module.css'

export interface ToggleGroupItem {
  content?: ReactNode
  value: string
  buttonProps?: Omit<ButtonProps, 'onClick' | 'data-selected'>
}

interface ToggleGroupProps extends Omit<GroupProps, 'onChange'> {
  items: ToggleGroupItem[]
  selectedValues?: string[]
  onChange: (values: string[]) => void
  allowMultiple?: boolean
}

export const ToggleGroup = ({
  items,
  selectedValues = [],
  onChange,
  allowMultiple = false,
  ...props
}: ToggleGroupProps) => {
  const toggleValue = (value: string) => {
    let newSelection: string[]

    if (allowMultiple) {
      newSelection = selectedValues.includes(value)
        ? selectedValues.filter(v => v !== value)
        : [...selectedValues, value]
    } else {
      newSelection = selectedValues.includes(value) ? [] : [value]
    }

    onChange(newSelection)
  }

  return (
    <Group gap={16} {...props}>
      {items.map(item => (
        <Button
          key={item.value}
          radius="md"
          className={classNames.toggleButton}
          data-selected={selectedValues.includes(item.value)}
          onClick={() => toggleValue(item.value)}
          {...item.buttonProps}
        >
          {item.content ?? item.value}
        </Button>
      ))}
    </Group>
  )
}

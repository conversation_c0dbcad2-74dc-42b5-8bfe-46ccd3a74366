.toggleButton {
  background-color: transparent;
  border-color: var(--mantine-color-gray-2);
  color: var(--mantine-color-gray-9);
  transition: background 0.15s ease-in-out;

  &:active {
    transform: none;
  }

  &:hover {
    color: var(--mantine-color-gray-9);
    background-color: var(--mantine-color-purple-0);
  }

  &[data-selected="true"] {
    border-color: var(--mantine-color-purple-5);
    background-color: var(--mantine-color-purple-0);

    &:hover {
      background-color: var(--mantine-color-purple-1);
    }
  }
}

import { Group, Select, Text } from '@mantine/core'
import { IconCheck, IconSelector } from '@tabler/icons-react'
import { useCallback, useMemo } from 'react'
import styles from './StaticSelectDropdown.module.css'

export const StaticSelectDropdown = <T extends string>({
  items,
  value,
  onChange,
  width = 116,
  dropdownWidth = 130,
}: {
  items: { icon: React.ReactNode; value: T; label: string }[]
  value: T
  onChange: (value: T) => void
  width?: number
  dropdownWidth?: number
}) => {
  const getIcon = useCallback((value: T) => items.find(item => item.value === value)?.icon, [items])

  const inputIcon = useMemo(() => getIcon(value), [getIcon, value])

  const renderSelectOption = useCallback(
    ({ option, checked }: { option: { value: string; label: string }; checked?: boolean | undefined }) => {
      const icon = getIcon(option.value as T)
      return (
        <Group flex={1} gap={6} justify="space-between">
          {icon}
          <Text fz={14} flex={1}>
            {option.label}
          </Text>
          {checked ? <IconCheck size={16} style={{ marginLeft: 'auto' }} /> : null}
        </Group>
      )
    },
    [getIcon]
  )

  return (
    <Select
      w={width}
      size="md"
      value={value}
      onChange={value => onChange(value as T)}
      data={items}
      renderOption={renderSelectOption}
      leftSection={inputIcon}
      rightSection={<IconSelector size={16} color="var(--mantine-color-gray-9)" />}
      allowDeselect={false}
      classNames={{
        input: styles.input,
      }}
      styles={{
        input: {
          fontSize: 14,
          borderRadius: 6,
        },
        option: {
          padding: 8,
        },
      }}
      comboboxProps={{ width: dropdownWidth, position: 'bottom-end' }}
    />
  )
}

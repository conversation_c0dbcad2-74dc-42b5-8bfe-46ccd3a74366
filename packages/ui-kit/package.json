{"name": "@upbound/ui-kit", "version": "1.0.0", "description": "Upbound UI Kit", "main": "src/index.ts", "scripts": {"storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "license": "UNLICENSED", "private": true, "dependencies": {"@mantine/core": "^8.1.3", "@tabler/icons-react": "^3.34.1", "next": "15.3.4", "postcss": "^8.5.6", "postcss-preset-mantine": "1.18.0", "postcss-simple-vars": "^7.0.1", "radash": "^12.1.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.7", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/test": "^8.6.14", "@types/react": "19.1.6", "prop-types": "^15.8.1", "storybook": "^8.6.14", "tsconfig": "workspace:*"}, "exports": {".": {"import": "./src"}}}
import PgConnectionString from 'pg-connection-string'
import type { Row, RowList, Sql, TransactionSql } from 'postgres'
import postgres from 'postgres'

export interface NativePGConnectionOptions {
  connectionString?: string
  maxPoolSize?: number
  defaultQueryTimeoutMs?: number
}

const defaultOptions = {
  debugMode: false,
  maxPoolSize: 10,
}

export type SqlClient = Sql<{
  [key: string]: unknown
}>

export type TransactionSqlClient = TransactionSql

export async function connectToPG(opts: NativePGConnectionOptions, applicationName: string): Promise<SqlClient> {
  const loggerName = 'postgres.js'

  const { connectionString, debugMode, maxPoolSize } = { ...defaultOptions, ...opts }
  const conn = connectionString ? PgConnectionString.parse(connectionString) : null

  const sql = postgres({
    // Connection parameters
    host: conn?.host ?? void 0,
    port: conn?.port ? Number.parseInt(conn.port) : void 0,
    user: conn?.user ?? void 0,
    pass: conn?.password ?? void 0,
    database: conn?.database ?? void 0,
    ssl: (conn?.ssl as unknown as boolean) ?? false,
    /** Connection Lifecycle  **/
    // Total concurrent connections in the pool
    max: maxPoolSize,
    // Connection timeout in seconds.
    connect_timeout: 10,
    // Enable TCP keepAlive
    keep_alive: 60,
    // Application Name
    connection: {
      application_name: applicationName,
    },
    // Use "prepared" connections
    // Set this to false if we want to use a
    // connection pooler between us and postgres.
    prepare: true,
    // When a "notice" is raised within postgres
    // Log it as a warning
    onnotice: notice => {
      console.warn(JSON.stringify({ message: 'PGNotice', notice, loggerName }))
    },
    // Debug logging
    debug: debugMode
      ? (connection, query, params, paramTypes) =>
          console.debug(JSON.stringify({ message: 'PGDebug', connection, query, params, paramTypes, loggerName }))
      : false,
  })

  // Establish a connection pool ahead of time
  // to verify that we can connect to the database properly.
  const _: RowList<Row[]>[] = await Promise.all(
    Array.from(Array(Math.min(Math.floor(sql.options.max / 2), 2))).map(() => sql`select 1`.execute())
  )

  return sql
}

import type { SqlClient } from '../connectToPG.js'

export class DBFunctions {
  constructor(private readonly sql: SqlClient) {}

  async getNormalisedTextFrom(text: string[]) {
    const [words] = await this.sql<[{ words: string[] }]>`
      SELECT tsvector_to_array(to_tsvector('english', ${text.join(' ')})) as words
    `
    return words.words
  }

  async getTimezones() {
    return this.sql<{ name: string; abbrev: string; isDst: boolean; offset: string; offsetMinutes: number }[]>`
      SELECT
        name,
        abbrev,
        is_dst as "isDst",
        (EXTRACT(EPOCH FROM utc_offset)/60)::INT as "offsetMinutes",
        to_char(EXTRACT(hour FROM utc_offset), 'SG09') || ':' || LPAD(ABS(EXTRACT(minute FROM utc_offset))::TEXT, 2, '0') as "offset"
      FROM pg_timezone_names
      WHERE name NOT LIKE 'Etc/%'
        AND name NOT LIKE 'GMT%'
        AND name ~ '.+/.+'
    `.then(x => Array.from(x))
  }

  async getTimezoneForName(name: string) {
    return this.sql<{ name: string; abbrev: string; isDst: boolean; offset: string; offsetMinutes: number }[]>`
      SELECT
        name,
        abbrev,
        is_dst as "isDst",
        (EXTRACT(EPOCH FROM utc_offset)/60)::INT as "offsetMinutes",
        to_char(EXTRACT(hour FROM utc_offset), 'SG09') || ':' || LPAD(ABS(EXTRACT(minute FROM utc_offset))::TEXT, 2, '0') as "offset"
      FROM pg_timezone_names
      WHERE name = ${name}
      LIMIT 1;
    `.then(x => x[0])
  }
}

import { setTimeout } from 'node:timers/promises'
import { Result } from '@swan-io/boxed'
import type { RedisClient } from '../redis/RedisClient.js'

export type LockedMemoizeError = 'TimedOut'

export type CacheOpts = {
  ttlMs?: number
}

/**
 * An abstract interface describing the basic behaviour expected from a `cache`
 * that can be used for performance optimization to avoid doing expensive work that
 * returns deterministic results for up-to <TTL> duration.
 */
export interface RedisPersistentCache {
  /**
   * Invalidate the value assigned to a key from the underlying data-store.
   */
  invalidate(key: string): Promise<boolean>

  /**
   * Returns a "memoized" function which caches the results of the function.
   * NOTE: This will turn synchronous methods into "async" methods.
   */
  memoize<T extends unknown[], U, V>(
    fn: (...args: T) => PromiseLike<Result<U, V>>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs?: number } | null,
    codec?: { encoder: (x: U) => string; decoder: (x: string) => U }
  ): (...args: T) => Promise<Result<U, V>>

  /**
   * Returns a "memoized" function which caches the results of the function execution within a lock.
   *
   * The function is executed after obtaining an exclusive lock in Redis.
   * However, "lock extensions" are not verified since the verification only happens
   * after the fn execution is complete and we do not want to waste the result of a computation
   * that has already been performed.
   *
   * TLDR; This can be used as a best-effort "efficiency lock". If the computation has completed
   * successfully, the result will not be discarded and the memoize will complete successfully.
   *
   * NOTE: This will turn synchronous methods into "async" methods.
   */
  memoizeLocked<T extends unknown[], U, V>(
    fn: (...args: T) => PromiseLike<Result<U, V>>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs?: number } | null,
    options?: Partial<{
      codec: {
        encoder: (x: U) => string
        decoder: (x: string) => U
      }
      maxRetries: number
      waitBetweenRetriesMs: number
    }>
  ): (...args: T) => Promise<Result<U, V | Error | LockedMemoizeError>>

  /**
   * An "unsafe" wrapper implementation of the `memoize` method.
   * This method is unsafe because it does not wrap the result in a `Result` monad
   * and instead throws an error if the result is an `Error`.
   * @param fn
   * @param keyResolver
   * @param getOpts
   * @param codec
   */
  memoizeUnsafe<T extends unknown[], U>(
    fn: (...args: T) => PromiseLike<U>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs?: number } | null,
    codec?: { encoder: (x: U) => string; decoder: (x: string) => U }
  ): (...args: T) => Promise<U>

  /**
   * An "unsafe" wrapper implementation of the `memoize` method.
   * This method is unsafe because it does not wrap the result in a `Result` monad
   * and instead throws an error if the result is an `Error`.
   * @param fn
   * @param keyResolver
   * @param getOpts
   * @param codec
   */
  memoizeLockedUnsafe<T extends unknown[], U>(
    fn: (...args: T) => PromiseLike<U>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs?: number } | null,
    options?: {
      codec?: { encoder: (x: U) => string; decoder: (x: string) => U }
      maxRetries: number
      waitBetweenRetriesMs: number
    }
  ): (...args: T) => Promise<U>
}

// biome-ignore lint/suspicious/noUnsafeDeclarationMerging: legacy
export class RedisPersistentCache implements RedisPersistentCache {
  static apply(namespace: string, redisClient: RedisClient): RedisPersistentCache {
    return new RedisPersistentCache(namespace, redisClient)
  }

  private constructor(
    private namespace: string,
    private redisClient: RedisClient
  ) {}

  private getNamespacedKey(forKey: string) {
    return `${this.namespace}-${forKey}`
  }

  async invalidate(sourceKey: string): Promise<boolean> {
    const key = this.getNamespacedKey(sourceKey)
    // Use `unlink` instead of `del` since `del` is a blocking operation.
    // https://redis.io/commands/unlink
    const result = await this.redisClient.redis.unlink(key)
    return !!result
  }

  memoizeUnsafe<T extends unknown[], U>(
    fn: (...args: T) => Promise<U>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs: number } | null,
    options: {
      encoder: (x: U) => string
      decoder: (x: string) => U
    } = {
      encoder: JSON.stringify,
      decoder: JSON.parse,
    }
  ): (...args: T) => Promise<U> {
    const liftFnToResult = async (...args: T) => {
      try {
        const result = await fn(...args)
        return Result.Ok(result)
      } catch (e) {
        return Result.Error(e as Error)
      }
    }

    const memoizedFn = this.memoize(liftFnToResult, keyResolver, getOpts, options)

    return async (...args: T) => {
      const result = await memoizedFn(...args)
      return result.match({
        Ok(value) {
          return value
        },
        Error(error) {
          throw error
        },
      })
    }
  }

  memoizeLockedUnsafe<T extends unknown[], U>(
    fn: (...args: T) => Promise<U>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs: number } | null,
    options: {
      codec?: { encoder: (x: U) => string; decoder: (x: string) => U }
      maxRetries: number
      waitBetweenRetriesMs: number
    }
  ): (...args: T) => Promise<U> {
    const liftFnToResult = async (...args: T) => {
      try {
        const result = await fn(...args)
        return Result.Ok(result)
      } catch (e) {
        return Result.Error(e as Error)
      }
    }

    const memoizedFn = this.memoizeLocked(liftFnToResult, keyResolver, getOpts, options)

    return async (...args: T) => {
      const result = await memoizedFn(...args)
      return result.match({
        Ok(value) {
          return value
        },
        Error(error) {
          if (error instanceof Error) {
            throw error
          } else {
            throw new Error(error)
          }
        },
      })
    }
  }

  memoize<T extends unknown[], U, V>(
    fn: (...args: T) => Promise<Result<U, V>>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs: number } | null,
    options: {
      encoder: (x: U) => string
      decoder: (x: string) => U
    } = {
      encoder: JSON.stringify,
      decoder: JSON.parse,
    }
  ): (...args: T) => Promise<Result<U, V>> {
    const { encoder, decoder } = options

    const cachedFn = async (...args: T): Promise<Result<U, V>> => {
      const key = this.getNamespacedKey(keyResolver(...args))

      // Attempt to retrieve value from cache. If the value does not exist,
      // evaluate the function and store the result in Redis.
      const redis = this.redisClient.redis
      const cacheResult = await redis.get(key)

      if (cacheResult) {
        const data = decoder(cacheResult)
        return Result.Ok(data)
      } else {
        const result = await fn(...args)

        if (result.isOk()) {
          const opts = getOpts(result.value)

          if (opts !== null) {
            const stringifiedData = encoder(result.value)
            await redis.set(key, stringifiedData, 'PX', opts.ttlMs)
          }
        }

        return result
      }
    }

    return cachedFn
  }

  memoizeLocked<T extends unknown[], U, V>(
    fn: (...args: T) => Promise<Result<U, V>>,
    keyResolver: (...args: T) => string,
    getOpts: (result: U) => { ttlMs: number } | null,
    options: Partial<{
      codec: {
        encoder: (x: U) => string
        decoder: (x: string) => U
      }
      maxRetries: number
      waitBetweenRetriesMs: number
    }> = {}
  ): (...args: T) => Promise<Result<U, V | Error | LockedMemoizeError>> {
    const { maxRetries, waitBetweenRetriesMs, codec } = {
      codec: {
        encoder: (x: U): string => JSON.stringify(x),
        decoder: (x: string) => JSON.parse(x) as U,
      },
      // Represents a total wait time of 30 seconds
      maxRetries: 20,
      waitBetweenRetriesMs: 1500,
      ...options,
    }

    const cachedFn = async (...args: T): Promise<Result<U, V | Error | LockedMemoizeError>> => {
      const key = this.getNamespacedKey(keyResolver(...args))

      // Attempt to retrieve value from cache.
      // If the value does not exist, create a "lock" before attempting
      // to acquire and set data. If a lock already exists,
      // wait and attempt to fetch data from redis again.
      const resolveToResult = async (): Promise<Result<U, V | Error | 'ALREADY_LOCKED'>> => {
        const cacheResult = await this.redisClient.redis.get(key)

        if (cacheResult) {
          const data = codec.decoder(cacheResult)
          return Result.Ok(data)
        } else {
          const redis = this.redisClient.redis

          const finalResult = await this.redisClient.lockAndRun(
            [key, 'lock'],
            async () => {
              const result = await fn(...args)

              if (result.isOk()) {
                const opts = getOpts(result.value)

                if (opts !== null) {
                  const stringifiedData = codec.encoder(result.value)
                  await redis.set(key, stringifiedData, 'PX', opts.ttlMs)
                }
              }

              return result
            },
            { lockDurationMs: 5000, automaticExtensionThreshold: 400 }
          )

          // biome-ignore lint/complexity/noFlatMapIdentity: This is a `Result` type and not an `Array`.
          return finalResult.flatMap(x => x)
        }
      }

      // A trampoline-recursive evaluation of `resolveToResult`
      for (let i = 0; i < maxRetries; i++) {
        const result = await resolveToResult()

        if (result.isOk()) {
          return Result.Ok(result.value)
        }

        const error = result.value

        if (error instanceof Error) {
          return Result.Error(error)
        } else {
          // When the resource is "already locked" and there are retries remaining,
          // Wait for <waitBetweenRetriesInMs> and try again.
          if (error === 'ALREADY_LOCKED' && i < maxRetries - 1) {
            await setTimeout(waitBetweenRetriesMs)
          }
        }
      }

      return Result.Error('TimedOut')
    }

    return cachedFn
  }
}

import { Deferred, Duration, Effect, type Either, MutableHashMap, Option, Schema } from 'effect'
import { RedisClient } from './RedisClient.js'

type MemoizeParams<Input, Value, Error, Environment> = {
  namespace: string
  lookup: (input: Input) => Effect.Effect<Value, Error, Environment>
  keyResolver: (input: Input) => string
  ttlForResult: (value: Either.Either<Value, Error>) => { ttl: Duration.DurationInput } | null
}

const CacheSchema = Schema.Either({
  left: Schema.Unknown,
  right: Schema.Unknown,
})

const encoder = Schema.encodeSync(CacheSchema)
const decoder = Schema.decodeUnknownSync(CacheSchema)

const encode = (value: Either.Either<unknown, unknown>) => JSON.stringify(encoder(value))
const decode = (value: string) => decoder(JSON.parse(value))

export const makeRedisMemoize = <Input, Value, Error, Environment>(
  params: MemoizeParams<Input, Value, Error, Environment>
) => {
  return Effect.gen(function* () {
    const { get, set } = yield* RedisClient
    const pending = MutableHashMap.empty<string, Deferred.Deferred<Either.Either<Value, Error>>>()
    const keyResolver = (input: Input) =>
      params.namespace ? `${params.namespace}:${params.keyResolver(input)}` : params.keyResolver(input)

    const getValue = (key: string) => get(key).pipe(Effect.map(x => (x ? decode(JSON.parse(x)) : null)))
    const setValue = (key: string, value: Either.Either<Value, Error>, ttlMs: number) =>
      set(key, JSON.stringify(encode(value)), ttlMs)

    const memoize = (input: Input) =>
      Effect.gen(function* () {
        const key = keyResolver(input)
        const maybeDef = MutableHashMap.get(pending, key)

        if (Option.isSome(maybeDef)) {
          const def = maybeDef.value
          const result = yield* Deferred.await(def)

          return yield* result
        } else {
          const value = yield* getValue(key)

          if (value) {
            return yield* value as Either.Either<Value, Error>
          }

          const def = yield* Deferred.make<Either.Either<Value, Error>>()
          MutableHashMap.set(pending, key, def)

          yield* params.lookup(input).pipe(
            Effect.catchAllDefect(defect => Effect.fail(defect as Error)),
            Effect.either,
            Effect.flatMap(res => {
              const ttl = params.ttlForResult(res)?.ttl

              return Effect.zipLeft(
                Deferred.succeed(def, res),
                ttl ? setValue(key, res, Duration.toMillis(ttl)) : Effect.void
              )
            }),
            Effect.tap(() => Effect.sync(() => MutableHashMap.remove(pending, key))),
            Effect.fork
          )

          const result = yield* Deferred.await(def)
          return yield* result
        }
      })

    return memoize
  })
}

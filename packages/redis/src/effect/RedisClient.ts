import { Config, Effect, Redacted } from 'effect'
import { Redis as Redis_, type RedisOptions } from 'ioredis'

const makeRedis = (connectionString: string, options: RedisOptions = {}) =>
  Effect.acquireRelease(
    Effect.gen(function* () {
      const r = new Redis_(connectionString, {
        lazyConnect: true,
        retryStrategy: times => Math.min(times * 50, 2000),
        // Check "server" status before emitting "READY" event
        enableReadyCheck: true,

        // Auto pipe-line messages to Redis to reduce "Head-of-line" blocking
        enableAutoPipelining: true,

        // Disable "offlineQueue".
        // Service should not start-up until connection is "ready"
        enableOfflineQueue: false,

        // Enable keepAlive and disable nagle's algorithm
        noDelay: true,
        keepAlive: 5000,
        ...options,
      })

      yield* Effect.tryPromise(() => r.connect())
      yield* Effect.logInfo('🟥 Redis connected')
      return r
    }),
    redis => {
      return Effect.tryPromise(() => redis.quit()).pipe(Effect.ignoreLogged)
    }
  )

export class RedisClient extends Effect.Service<RedisClient>()('db/RedisClient', {
  scoped: Effect.gen(function* () {
    const redisUrlSecret = yield* Config.redacted('REDIS_URL').pipe(
      Config.withDefault(Redacted.make('redis://localhost:6379'))
    )

    const redis = yield* makeRedis(Redacted.value(redisUrlSecret))

    const set = (key: string, value: string, ttlMs: number) =>
      Effect.tryPromise(() => redis.set(key, value, 'PX', ttlMs))

    const get = (key: string) => Effect.tryPromise(() => redis.get(key))

    const del = (key: string) => Effect.tryPromise(() => redis.unlink(key))

    return {
      set,
      get,
      del,
    } as const
  }),
}) {}

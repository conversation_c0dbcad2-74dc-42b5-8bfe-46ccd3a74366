import type { Redis } from 'ioredis'
import { Observable } from 'rxjs'

/**
 * Redis Pub/Sub maintains a single persistent connection to the redis-server
 * Therefore, this redis-client cannot be used for anything else other than for
 * pub/sub operations. Therefore, all subscriptionss will enforce creation of a separate redis-client for
 * this service.
 *
 * https://github.com/luin/ioredis#pubsub
 */
export class RedisPubSub {
  static apply(namespace: string, redis: Redis): RedisPubSub {
    return new RedisPubSub(namespace, redis)
  }

  private constructor(
    private readonly namespace: string,
    private readonly redisClient: Redis
  ) {}

  getNamespace(channel: string): string {
    return `${this.namespace}:${channel}`
  }

  publish(channel: string, message: string | Buffer): Promise<number> {
    return this.redisClient.publish(this.getNamespace(channel), message)
  }

  // This creates a new redis connection under-the-hood exclusive to this subscription
  subscribe(channelName: string): Observable<{ channel: string; message: string }> {
    return new Observable(observer => {
      const namespacedChannel = this.getNamespace(channelName.trim())
      const scopedRedis = this.redisClient.duplicate()

      void scopedRedis.subscribe(namespacedChannel, err => {
        if (err) {
          observer.error(err)
        }
      })

      scopedRedis.on('message', (channel, message) => {
        if (namespacedChannel === channel) {
          observer.next({ channel, message })
        }
      })

      scopedRedis.on('error', (error: Error) => {
        observer.error(error)
      })

      return () => {
        void scopedRedis.unsubscribe(namespacedChannel).then(() => scopedRedis.disconnect())
      }
    })
  }

  // This creates a new redis connection under-the-hood exclusive to this subscription
  psubscribe(globPattern: string): Observable<{ channel: string; message: string }> {
    return new Observable(observer => {
      const namespacedPattern = this.getNamespace(globPattern.trim())
      const scopedRedis = this.redisClient.duplicate()

      void scopedRedis.psubscribe(namespacedPattern, err => {
        if (err) {
          observer.error(err)
        }
      })

      scopedRedis.on('pmessage', (pattern, channel, message) => {
        if (namespacedPattern === pattern) {
          observer.next({ channel, message })
        }
      })

      scopedRedis.on('error', (error: Error) => {
        observer.error(error)
      })

      return () => {
        void scopedRedis.punsubscribe(namespacedPattern).then(() => scopedRedis.disconnect())
      }
    })
  }

  disconnect(): void {
    this.redisClient.disconnect()
  }
}

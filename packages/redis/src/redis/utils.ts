import { randomBytes } from 'node:crypto'

// eslint-disable-next-line @typescript-eslint/no-empty-function
const AsyncGeneratorFunction = async function* () {}.constructor

export function isAsyncGeneratorFunction<T>(
  fn: (() => AsyncGenerator<unknown, T, unknown>) | (() => Promise<T>)
): fn is () => AsyncGenerator<unknown, T, unknown> {
  return fn instanceof AsyncGeneratorFunction
}

/**
 * Generate a "random" 16 character string.
 */
export function getRandomString(): string {
  return randomBytes(16).toString('hex')
}

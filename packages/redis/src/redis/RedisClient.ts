import { Result } from '@swan-io/boxed'
import type { Redis, RedisOptions } from 'ioredis'
import IORedis from 'ioredis'
import { RedisPubSub } from './RedisPubSub.js'
import { ACQUIRE_SCRIPT, EXTEND_SCRIPT, RELEASE_SCRIPT } from './RedisScripts.js'
import { getRandomString, isAsyncGeneratorFunction } from './utils.js'

/**
 *`lockDurationMs` is the duration for which we attempt to acquire the lock.
 * All locks should have a TTL. Without a TTL, if the application crashes, we'll just have a lock
 * sitting around that can never be released.
 *
 * Consider a function that takes anywhere between 1 second to 60 seconds to execute.
 * If we configure lockDurationMs to 5 seconds, then all requests that come-in between the
 * 5 second -> 60 second mark would not be locked and will still execute.
 *
 * To account for that, we have to extend the lock when its nearing the expiry.
 * The `automaticExtensionThreshold` determines how long before the expiry of the current lock the
 * locking mechanism should attempt to extend the lock. By extending the lock, we're accounting for the
 * computation that has not finished yet and we retain the lock until it does.
 *
 * If the computation fails part-way or the service fatally crashes, the lock gets automatically
 * released when it reaches `lockDurationMs`.
 *
 * In general, `lockDurationMs` should not be too large. The `automaticExtensionThreshold` should be a few hundred ms.
 *
 * The defaults for these values are generally reasonable in most cases.
 */
export interface LockSettings {
  lockDurationMs: number
  driftFactor: number
  automaticExtensionThreshold: number
}

export type AlreadyLocked = 'ALREADY_LOCKED'
export type LockError = AlreadyLocked | 'FAILED_TO_EXTEND'

export interface RedisClient {
  redis: Redis

  /**
   * Perform a computation within a deterministic "lock" backed by Redis.
   * This implementation only guarantees optimistic locking. This cannot be used for verifying
   * "correctness". However, this is good enough for "efficiency" purposes where we can state that
   * most of the time, computation will be correctly deduplicated in a horizontally scaled cluster.
   *
   * The method can either be an `async` function or an `AsyncGenerator` function.
   *
   * Async =>
   * ```typescript
   * const response = await client.lockAndRun(LOCK_KEY, async () => {
   *   const value1 = await <some-promise>
   *   const value2 = await <some-promise>
   *
   *   ...
   *   return result
   * }
   * ```
   *
   * AsyncGenerator =>
   * ```typescript
   * const response = await client.lockAndRun(LOCK_KEY, async function * () {
   *   const value1 = await <some-promise>
   *   const value2 = await <some-promise>
   *
   *   // Use a "yield" statement to verify existence of the lock.
   *   yield
   *   const value3 = await <some-promise>
   *   ...
   *   return result
   * }
   * ```
   *
   * When the function is an `AsyncGenerator`, note the deliberate use of `yield` in the function body.
   *
   * At every `yield`, the runtime checks to make sure that we still retain the "lock".
   * If the existence of the lock cannot be reasonably guaranteed or if the lock extension fails,
   * execution will stop.
   *
   * To execute batches of "uncancellable" code, do not "yield" within those batches.
   *
   * See tests for more examples.
   */
  lockAndRun<T>(
    lockKeys: string[],
    runWhenLocked: () => Promise<T>,
    lockSettings?: Partial<LockSettings>
  ): Promise<Result<T, AlreadyLocked | Error>>
  lockAndRun<T>(
    lockKeys: string[],
    runWhenLocked: () => AsyncGenerator<unknown, T, unknown>,
    lockSettings?: Partial<LockSettings>
  ): Promise<Result<T, LockError | Error>>
}

const defaultLockSettings: Readonly<LockSettings> = {
  lockDurationMs: 5000,
  driftFactor: 0.01,
  automaticExtensionThreshold: 400,
}

/**
 * A highly opinionated set of default options for Redis
 */
const defaultRedisClientOptions: Readonly<RedisOptions> = {
  tls: process.env['NODE_ENV'] === 'production' ? { rejectUnauthorized: false } : undefined,
  commandTimeout: 5000,
  connectTimeout: 10_000,
  lazyConnect: true,
  // Check "server" status before emitting "READY" event
  enableReadyCheck: true,

  // Auto pipe-line messages to Redis to reduce "Head-of-line" blocking
  enableAutoPipelining: true,

  // Disable "offlineQueue".
  // Service should not start-up until connection is "ready"
  enableOfflineQueue: false,

  // Enable keepAlive and disable nagle's algorithm
  noDelay: true,
  keepAlive: 1000,
}

/**
 * A utility class which wraps `IORedis` and provides some simple
 * locking primitives.
 */
// biome-ignore lint/suspicious/noUnsafeDeclarationMerging: Legacy
export class RedisClient implements RedisClient {
  /**
   * Connect to Redis and expose an instance of `RedisClient`.
   */
  static async create(connectionString: string, ioRedisOpts?: RedisOptions): Promise<RedisClient> {
    const opts: RedisOptions = { ...defaultRedisClientOptions, ...ioRedisOpts, lazyConnect: true }
    const client = new IORedis.Redis(connectionString, opts)

    await client.connect()
    return new RedisClient(client)
  }

  /**
   * Use an existing instance of `Redis` to create an instance of `RedisClient`.
   */
  static apply(redis: Redis): RedisClient {
    return new RedisClient(redis)
  }

  /**
   * A map of custom command lua-scripts that can be executed by name.
   * https://github.com/luin/ioredis#lua-scripting
   */
  private customCommands: {
    acquireLock: (key: string, token: string, ttlMs: number) => PromiseLike<number>
    extendLock: (key: string, token: string, ttlMs: number) => PromiseLike<number>
    releaseLock: (key: string, token: string) => PromiseLike<number>
  }

  private constructor(public redis: Redis) {
    // Each "command" has a random suffix appended to it to give it sufficient entropy
    // so that multiple instances of this client do not end up having overlapping "command names".
    // This is important if we use the same `IORedis` instance for multiple instances
    // of this class.
    const acquireCommandName = `acquire_lock_${getRandomString()}`
    this.redis.defineCommand(acquireCommandName, { numberOfKeys: 1, lua: ACQUIRE_SCRIPT })

    const releaseCommandName = `release_lock_${getRandomString()}`
    this.redis.defineCommand(releaseCommandName, { numberOfKeys: 1, lua: RELEASE_SCRIPT })

    const extendCommandName = `extend_lock_${getRandomString()}`
    this.redis.defineCommand(extendCommandName, { numberOfKeys: 1, lua: EXTEND_SCRIPT })

    // Map commands created above to a strongly-typed custom-commands object so that dynamic allocation
    // is limited to this constructor.
    //
    // We have to bypass some lint-rules here because of the use of `any`. Since we use
    // dynamic names for each command, we won't be able to pre-define strong types for these names
    // ahead of time.
    this.customCommands = {
      // biome-ignore lint/suspicious/noExplicitAny: no reason given
      acquireLock: (this.redis as any)[acquireCommandName].bind(this.redis),
      // biome-ignore lint/suspicious/noExplicitAny: no reason given
      extendLock: (this.redis as any)[extendCommandName].bind(this.redis),
      // biome-ignore lint/suspicious/noExplicitAny: no reason given
      releaseLock: (this.redis as any)[releaseCommandName].bind(this.redis),
    }
  }

  private async acquireLockFor(key: string, token: string, ttlMs: number): Promise<boolean> {
    return this.customCommands.acquireLock(key, token, ttlMs).then((x: number) => !!x)
  }

  private async extendLockFor(key: string, token: string, ttlMs: number): Promise<boolean> {
    return this.customCommands.extendLock(key, token, ttlMs).then((x: number) => !!x)
  }

  private async releaseLockFor(key: string, token: string): Promise<boolean> {
    return this.customCommands.releaseLock(key, token).then((x: number) => !!x)
  }

  lockAndRun<T>(
    lockKeys: string[],
    runWhenLocked: () => Promise<T>,
    lockSettings?: Partial<LockSettings>
  ): Promise<Result<T, AlreadyLocked | Error>>
  lockAndRun<T>(
    lockKeyParts: string[],
    runWhenLocked: () => AsyncGenerator<unknown, T, unknown>,
    lockSettings?: Partial<LockSettings>
  ): Promise<Result<T, LockError | Error>>
  async lockAndRun<T>(
    lockKeyParts: string[],
    runWhenLocked: (() => AsyncGenerator<unknown, T, unknown>) | (() => Promise<T>),
    lockSettings?: Partial<LockSettings>
  ): Promise<Result<T, LockError | Error>> {
    const { automaticExtensionThreshold, driftFactor, lockDurationMs } = { ...defaultLockSettings, ...lockSettings }

    const lockKey = lockKeyParts.join('-')
    const token = getRandomString()
    const acquireTriggeredAtMs = Date.now()

    const isLockAcquired = await this.acquireLockFor(lockKey, token, lockDurationMs)

    if (isLockAcquired) {
      // Add 2 milliseconds to the drift to account for Redis expires precision,
      // which is 1 ms, plus the configured allowable drift factor.
      const drift = Math.round(driftFactor * lockDurationMs) + 2
      let autoExtendFailed = false

      const autoExtend = () => {
        let triggeredAtMs = acquireTriggeredAtMs
        let timer: NodeJS.Timeout

        const fn = () => {
          const expiresAt = triggeredAtMs + lockDurationMs - drift

          // Trigger extension.
          const triggerExtendInMs = expiresAt - automaticExtensionThreshold - Date.now()

          // Schedule "lock extension" at `triggerExtendInMs`
          const computation = async () => {
            const extendTriggeredAtMs = Date.now()
            const isLockExtended = await this.extendLockFor(lockKey, token, lockDurationMs)

            if (isLockExtended) {
              triggeredAtMs = extendTriggeredAtMs
              timer = fn()
            } else {
              autoExtendFailed = true
            }
          }

          return setTimeout(() => void computation(), triggerExtendInMs)
        }

        timer = fn()
        return () => clearTimeout(timer)
      }

      const stopAutoExtension = autoExtend()

      try {
        if (isAsyncGeneratorFunction(runWhenLocked)) {
          const generator = runWhenLocked()

          let nextResult = await generator.next()

          while (!nextResult.done) {
            if (autoExtendFailed) {
              // Close the generator.
              const _ = await generator.return({} as never)
              return Result.Error('FAILED_TO_EXTEND')
            }

            nextResult = await generator.next(nextResult.value)
          }

          return Result.Ok(nextResult.value)
        } else {
          const result = (await runWhenLocked()) as T
          return Result.Ok(result)
        }
      } catch (error) {
        return Result.Error(error as Error)
      } finally {
        // Ensure "timer" has been cleared.
        stopAutoExtension()

        // Ensure "lock" has been released.
        await this.releaseLockFor(lockKey, token)
      }
    } else {
      return Result.Error('ALREADY_LOCKED')
    }
  }

  createPubSubClient(namespace: string): RedisPubSub {
    return RedisPubSub.apply(namespace, this.redis)
  }

  /**
   * Disconnect from the underlying "redis" instance.
   * Gracefully drains closes the connection. Use with caution.
   */
  disconnect(): Promise<'OK'> {
    return this.redis.quit()
  }
}

/**
 * Attempt to acquire a lock on the given key.
 * If lock acquisition was succesful, return 1 ( AKA True ).
 * If lock acquisition failed, return 0 ( AKA False ).
 *
 * KEYS:
 * [1] - Key to lock
 * ARGV:
 * [1] - Value associated with the key
 * [2] - TTL(ms) of the lock
 */
export const ACQUIRE_SCRIPT = `
-- Return 0 if an entry already exists.
if redis.call("exists", KEYS[1]) == 1 then
  return 0
end

-- Create an entry for each provided key.
redis.call("set", KEYS[1], ARGV[1], "PX", ARGV[2])
return 1
`

/**
 * Attempt to extend an already existing lock.
 *
 * Gets the "value" associated with the key and ensures that the values are the same
 * before extending the lock.
 *
 * If lock extension was succesful, return 1 ( AKA True ).
 * If lock extension failed, return 0 ( AKA False ).
 *
 * KEYS:
 * [1] - Key to lock
 * ARGV:
 * [1] - Value associated with the key
 * [2] - TTL(ms) of the lock
 */
export const EXTEND_SCRIPT = `
-- Return 0 if an entry exists with a *different* lock value.
if redis.call("get", KEYS[1]) ~= ARGV[1] then
  return 0
end

-- Update the entry for the provided key.
redis.call("set", KEYS[1], ARGV[1], "PX", ARGV[2])

return 1
`

/**
 * Attempt to release an acquired lock.
 *
 * Gets the "value" associated with the key and ensures that the values are the same
 * before releasing the lock.
 *
 * If lock release was succesful, return 1 ( AKA True ).
 * If lock release failed, return 0 ( AKA False ).
 *
 * KEYS:
 * [1] - Key to lock
 * ARGV:
 * [1] - Value associated with the key
 */
export const RELEASE_SCRIPT = `
-- Return 0 if an entry exists with a *different* lock value.
if redis.call("get", KEYS[1]) ~= ARGV[1] then
  return 0
end

-- Return 1 if the key exists with the correct value.
redis.pcall("del", KEYS[1])

return 1
`

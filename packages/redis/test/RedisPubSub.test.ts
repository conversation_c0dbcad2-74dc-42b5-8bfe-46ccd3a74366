import { setTimeout } from 'node:timers/promises'
import type { Redis } from 'ioredis'
import IORedis from 'ioredis'
import { bufferCount, firstValueFrom, take } from 'rxjs'
import type { StartedTestContainer } from 'testcontainers'
import { afterAll, beforeAll, describe, expect, test } from 'vitest'
import { RedisClient } from '../src/redis/RedisClient.js'
import type { RedisPubSub } from '../src/redis/RedisPubSub.js'
import { createRedisContainer } from './test-utils.js'

describe('RedisPubSub', () => {
  let ioRedis: Redis
  let client: RedisClient
  let redisPubSub: RedisPubSub
  let container: StartedTestContainer

  beforeAll(async () => {
    container = await createRedisContainer().start()

    ioRedis = new IORedis({ port: container.getMappedPort(6379) })
    client = RedisClient.apply(ioRedis)
    redisPubSub = client.createPubSubClient('test')
  }, 60_000)

  afterAll(async () => {
    await client.disconnect()
    await container.stop()
  })

  describe('Pattern Subscribe - psubscribe', () => {
    test('Receives an event', async () => {
      const channelName = 'muppet.rocks'
      const eventsFromChannel = firstValueFrom(redisPubSub.psubscribe('muppet.*').pipe(take(1)))

      await setTimeout(100)
      await redisPubSub.publish(channelName, 'hello world')

      await expect(eventsFromChannel).resolves.toStrictEqual({ channel: 'test:muppet.rocks', message: 'hello world' })
    })

    test('Receives a lot of events', async () => {
      const channelName = 'muppet.rocks'
      const eventCount = 1000
      const eventsFromChannel = firstValueFrom(
        redisPubSub.psubscribe('muppet.*').pipe(bufferCount(eventCount), take(eventCount))
      )

      const publishMessages = Array.from(Array(eventCount)).map((_, i) => `hello ${i}`)

      await setTimeout(100)
      await Promise.all(publishMessages.map((x, i) => redisPubSub.publish(channelName + `-${i}`, x)))

      expect((await eventsFromChannel).map(x => x.message)).toStrictEqual(publishMessages)
    })
  })

  describe('Subscribe - subscribe', () => {
    test('Receives events', async () => {
      const channelName = 'muppet.rocks'
      const eventsFromChannel = firstValueFrom(redisPubSub.subscribe(channelName).pipe(take(1)))

      await setTimeout(100)
      await redisPubSub.publish(channelName, 'hello world')

      await expect(eventsFromChannel).resolves.toStrictEqual({ channel: 'test:muppet.rocks', message: 'hello world' })
    })

    test('Receives a lot of events', async () => {
      const channelName = 'muppet.rocks'
      const eventCount = 1000
      const eventsFromChannel = firstValueFrom(
        redisPubSub.subscribe(channelName).pipe(bufferCount(eventCount), take(eventCount))
      )

      const publishMessages = Array.from(Array(eventCount)).map((_, i) => `hello ${i}`)

      await setTimeout(100)
      await Promise.all(publishMessages.map(x => redisPubSub.publish(channelName, x)))

      expect((await eventsFromChannel).map(x => x.message)).toStrictEqual(publishMessages)
    })
  })
})

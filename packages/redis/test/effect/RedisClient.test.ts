import { afterAll, beforeAll, describe, expect, it } from '@effect/vitest'
import { ConfigProvider, Effect, Exit, identity, Layer } from 'effect'
import type { StartedTestContainer } from 'testcontainers'
import { makeRedisMemoize } from '../../src/effect/makeRedisMemoize.js'
import { RedisClient } from '../../src/effect/RedisClient.js'
import { createRedisContainer } from '../test-utils.js'

describe('Redis - Effect', () => {
  let container: StartedTestContainer
  let testLayer: Layer.Layer<never, never, never> = Layer.empty

  beforeAll(async () => {
    container = await createRedisContainer().start()
    const mockConfigProvider = ConfigProvider.fromJson({
      REDIS_URL: `redis://${container.getHost()}:${container.getMappedPort(6379)}`,
    })

    testLayer = Layer.setConfigProvider(mockConfigProvider)
  }, 60_000)

  afterAll(async () => await container.stop())

  const testLayer_ = Layer.suspend(() => RedisClient.Default.pipe(Layer.provide(testLayer)))

  describe('Memoize', () => {
    it.live('Caching works', () =>
      Effect.gen(function* () {
        let counter = 0

        const memoized = yield* makeRedisMemoize({
          lookup: (x: string) => {
            counter++
            return Effect.succeed(`${x} world`)
          },
          ttlForResult: () => ({ ttl: 1000 }),
          keyResolver: identity,
          namespace: Date.now().toString(),
        })

        const result = yield* memoized('hello')
        expect(result).toStrictEqual('hello world')
        expect(counter).toBe(1)

        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(counter).toBe(1)
      }).pipe(Effect.provide(testLayer_))
    )

    it.live('Respects TTL', () =>
      Effect.gen(function* () {
        let counter = 0

        const memoized = yield* makeRedisMemoize({
          lookup: (x: string) => {
            counter++
            return Effect.sleep(10).pipe(Effect.flatMap(() => Effect.succeed(`${x} world`)))
          },
          ttlForResult: () => ({ ttl: '100 millis' }),
          keyResolver: identity,
          namespace: Date.now().toString(),
        })

        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(counter).toBe(1)

        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(counter).toBe(1)

        yield* Effect.sleep(110)
        expect(yield* memoized('hello')).toStrictEqual('hello world')
        expect(counter).toBe(2)
      }).pipe(Effect.provide(testLayer_))
    )

    it.live('Concurrent access / protects against cache stampedes', () =>
      Effect.gen(function* () {
        let counter = 0

        const memoized = yield* makeRedisMemoize({
          lookup: (x: string) => {
            counter++
            return Effect.sleep(100).pipe(Effect.flatMap(() => Effect.succeed(`${x} world`)))
          },
          ttlForResult: () => ({ ttl: 500 }),
          keyResolver: identity,
          namespace: Date.now().toString(),
        })

        // Concurrent 10
        const concurrent = Array.from(Array(10)).map(() => memoized('hello'))
        // Another 10 after a short delay
        const concurrent2 = Array.from(Array(10)).map((_, i) => Effect.zipRight(Effect.sleep(i + 1), memoized('hello')))
        const results = yield* Effect.all([...concurrent, ...concurrent2])

        expect(results).toStrictEqual(Array(20).fill('hello world'))
        expect(counter).toBe(1)
      }).pipe(Effect.provide(testLayer_))
    )

    it.live('Works with complex structures', () =>
      Effect.gen(function* () {
        let counter = 0

        const memoized = yield* makeRedisMemoize({
          lookup: (x: { type: 'string' | 'number'; value: { arr: number[] } }) => {
            counter++
            return Effect.sleep(100).pipe(Effect.flatMap(() => Effect.succeed(x)))
          },
          ttlForResult: () => ({ ttl: 500 }),
          keyResolver: x => x.type,
          namespace: Date.now().toString(),
        })

        expect(yield* memoized({ type: 'string', value: { arr: [1, 2, 3] } })).toStrictEqual({
          type: 'string',
          value: { arr: [1, 2, 3] },
        })

        expect(counter).toBe(1)

        expect(yield* memoized({ type: 'string', value: { arr: [1, 2, 3] } })).toStrictEqual({
          type: 'string',
          value: { arr: [1, 2, 3] },
        })

        expect(yield* memoized({ type: 'number', value: { arr: [1, 2, 3] } })).toStrictEqual({
          type: 'number',
          value: { arr: [1, 2, 3] },
        })

        expect(counter).toBe(2)
      }).pipe(Effect.provide(testLayer_))
    )

    it.live('Failures are also cached.', () =>
      Effect.gen(function* () {
        let counter = 0

        const memoized = yield* makeRedisMemoize({
          lookup: (_x: { type: 'string' | 'number'; value: { arr: number[] } }) => {
            counter++
            return Effect.sleep(20).pipe(Effect.flatMap(() => Effect.fail('Oops')))
          },
          ttlForResult: () => ({ ttl: 100 }),
          keyResolver: x => x.type,
          namespace: Date.now().toString(),
        })

        expect(yield* memoized({ type: 'string', value: { arr: [1, 2, 3] } }).pipe(Effect.exit)).toStrictEqual(
          Exit.fail('Oops')
        )

        expect(counter).toBe(1)

        expect(yield* memoized({ type: 'string', value: { arr: [1, 2, 3] } }).pipe(Effect.exit)).toStrictEqual(
          Exit.fail('Oops')
        )

        expect(counter).toBe(1)

        yield* Effect.sleep(110)

        expect(yield* memoized({ type: 'string', value: { arr: [1, 2, 3] } }).pipe(Effect.exit)).toStrictEqual(
          Exit.fail('Oops')
        )

        expect(counter).toBe(2)
      }).pipe(Effect.provide(testLayer_))
    )
  })
})

import { setTimeout } from 'node:timers/promises'
import { Result } from '@swan-io/boxed'
import { Redis } from 'ioredis'
import type { StartedTestContainer } from 'testcontainers'
import { afterAll, beforeAll, beforeEach, describe, expect, test } from 'vitest'
import { RedisPersistentCache } from '../src/cache/RedisPersistentCache.js'
import { RedisClient } from '../src/redis/RedisClient.js'
import { createRedisContainer } from './test-utils.js'

describe('RedisPersistentCache', () => {
  let ioRedis: Redis
  let client: RedisClient
  let container: StartedTestContainer
  const identity = <T>(x: T) => x

  beforeAll(async () => {
    container = await createRedisContainer().start()

    ioRedis = new Redis({ port: container.getMappedPort(6379), keyPrefix: Date.now().toString() })
    client = RedisClient.apply(ioRedis)
  }, 60_000)

  afterAll(async () => {
    await client.disconnect()
    await container.stop()
  })

  describe('RedisPersistentCache', () => {
    let cache: RedisPersistentCache

    beforeEach(() => {
      const randomTestKey = Date.now()
      cache = RedisPersistentCache.apply(`test-${randomTestKey}`, client)
    })

    describe('Method - memoize', () => {
      test('Synchronous function', async () => {
        let called = 0
        const cachedFn = cache.memoize(
          (a: string) => {
            called++
            return Promise.resolve(Result.Ok(`${a} world`))
          },
          identity,
          _x => ({ ttlMs: 5000 })
        )

        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(1)
      })

      test('Asynchronous function', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(10)
          return Result.Ok(`${a} world`)
        }

        const cachedFn = cache.memoize(fn, identity, _x => ({ ttlMs: 5000 }))

        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))

        expect(called).toBe(1)
      })

      test('Respects TTL when results are memoized', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(10)
          return Result.Ok(`${a} world`)
        }

        const cachedFn = cache.memoize(fn, identity, () => ({ ttlMs: 5 }))

        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Sleep for 5ms.
        // This value will be purged from redis.
        await setTimeout(7)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(2)

        // Sleep for 5ms
        // This value would have been purged from redis again.
        await setTimeout(7)

        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(3)
      })

      test('Does not handle concurrent access or protect against cache stampedes', async () => {
        let called = 0
        const cachedFn = cache.memoize(
          async (a: string) => {
            called++
            return setTimeout(10).then(() => Result.Ok(`${a} world`))
          },
          identity,
          () => ({ ttlMs: 200 })
        )

        // When requests come-in simultaneously, all of the underlying computations are executed
        // and the value is set multiple times.
        await expect(
          Promise.all([
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
          ])
        ).resolves.toStrictEqual(Array(6).fill(Result.Ok('hello world')))

        expect(called).toBe(6)

        // Wait for ttl to expire
        await setTimeout(200)

        // Repeat "calls" to the cached function
        await cachedFn('hello')
        await cachedFn('hello')
        await cachedFn('hello')

        expect(called).toBe(7)
      })
    })

    describe('Method - memoizeLocked', () => {
      test('Synchronous function', async () => {
        let called = 0
        const cachedFn = cache.memoizeLocked(
          (a: string) => {
            called++
            return Promise.resolve(Result.Ok(`${a} world`))
          },
          identity,
          _x => ({ ttlMs: 5000 })
        )

        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(1)
      })

      test('Asynchronous function', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(10)
          return Result.Ok(`${a} world`)
        }

        const cachedFn = cache.memoizeLocked(fn, identity, _x => ({ ttlMs: 5000 }))

        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toStrictEqual(Result.Ok('hello world'))

        expect(called).toBe(1)
      })

      test('Respects TTL when results are memoized', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(10)
          return Result.Ok(`${a} world`)
        }

        const cachedFn = cache.memoizeLocked(fn, identity, () => ({ ttlMs: 5 }))

        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        expect(called).toBe(1)

        // Sleep
        // This value will be purged from redis.
        await setTimeout(7)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))
        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(2)

        // Sleep
        // This value would have been purged from redis again.
        await setTimeout(7)

        await expect(cachedFn('hello')).resolves.toEqual(Result.Ok('hello world'))

        expect(called).toBe(3)
      })

      test('Handles concurrent access and protects against cache stampedes', async () => {
        let called = 0
        const cachedFn = cache.memoizeLocked(
          async (a: string) => {
            called++
            return setTimeout(10).then(() => Result.Ok(`${a} world`))
          },
          identity,
          () => ({ ttlMs: 200 }),
          { maxRetries: 10, waitBetweenRetriesMs: 200 }
        )

        // When requests come-in simultaneously, only one underlying computation is executed.
        // until the value is set.
        await expect(
          Promise.all([
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
          ])
        ).resolves.toStrictEqual(Array(6).fill(Result.Ok('hello world')))

        expect(called).toBe(1)

        // Wait for ttl to expire
        await setTimeout(200)

        // Repeat "calls" to the cached function
        await cachedFn('hello')
        await cachedFn('hello')
        await cachedFn('hello')

        expect(called).toBe(2)
      })
    })

    describe('Method - memoizeUnsafe', () => {
      test('Synchronous function', async () => {
        let called = 0
        const cachedFn = cache.memoizeUnsafe(
          (a: string) => {
            called++
            return Promise.resolve(`${a} world`)
          },
          identity,
          _x => ({ ttlMs: 5000 })
        )

        await expect(cachedFn('hello')).resolves.toBe('hello world')
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toBe('hello world')
        await expect(cachedFn('hello')).resolves.toBe('hello world')
        await expect(cachedFn('hello')).resolves.toBe('hello world')

        expect(called).toBe(1)
      })

      test('Asynchronous function', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(10)
          return `${a} world`
        }

        const cachedFn = cache.memoizeUnsafe(fn, identity, _x => ({ ttlMs: 5000 }))

        await expect(cachedFn('hello')).resolves.toBe('hello world')
        expect(called).toBe(1)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toBe('hello world')
        await expect(cachedFn('hello')).resolves.toBe('hello world')
        await expect(cachedFn('hello')).resolves.toBe('hello world')

        expect(called).toBe(1)
      })

      test('Respects TTL when results are memoized', async () => {
        let called = 0

        const fn = async (a: string) => {
          called++
          await setTimeout(1)
          return `${a} world`
        }

        const cachedFn = cache.memoizeUnsafe(fn, identity, () => ({ ttlMs: 5 }))

        await expect(cachedFn('hello')).resolves.toBe('hello world')
        expect(called).toBe(1)

        // Sleep for 5ms.
        // This value will be purged from redis.
        await setTimeout(7)

        // Repeat "calls" to the cached function
        await expect(cachedFn('hello')).resolves.toBe('hello world')
        await expect(cachedFn('hello')).resolves.toBe('hello world')

        expect(called).toBe(2)

        // Sleep for 5ms
        // This value would have been purged from redis again.
        await setTimeout(7)

        await expect(cachedFn('hello')).resolves.toBe('hello world')

        expect(called).toBe(3)
      })

      test('Does not handle concurrent access or protect against cache stampedes', async () => {
        let called = 0
        const cachedFn = cache.memoizeUnsafe(
          async (a: string) => {
            called++
            return setTimeout(10).then(() => `${a} world`)
          },
          identity,
          () => ({ ttlMs: 200 })
        )

        // When requests come-in simultaneously, all of the underlying computations are executed
        // and the value is set multiple times.
        await expect(
          Promise.all([
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
          ])
        ).resolves.toStrictEqual(Array(6).fill('hello world'))

        expect(called).toBe(6)

        // Wait for ttl to expire
        await setTimeout(200)

        // Repeat "calls" to the cached function
        await cachedFn('hello')
        await cachedFn('hello')
        await cachedFn('hello')

        expect(called).toBe(7)
      })
    })

    describe('Method - memoizeLockedUnsafe', () => {
      test('Handles concurrent access and protects against cache stampedes', async () => {
        let called = 0
        const cachedFn = cache.memoizeLockedUnsafe(
          async (a: string) => {
            called++
            return setTimeout(10).then(() => Result.Ok(`${a} world`))
          },
          identity,
          () => ({ ttlMs: 200 }),
          { maxRetries: 10, waitBetweenRetriesMs: 200 }
        )

        // When requests come-in simultaneously, only one underlying computation is executed.
        // until the value is set.
        await expect(
          Promise.all([
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
            cachedFn('hello'),
          ])
        ).resolves.toStrictEqual(Array(6).fill(Result.Ok('hello world')))

        expect(called).toBe(1)

        // Wait for ttl to expire
        await setTimeout(200)

        // Repeat "calls" to the cached function
        await cachedFn('hello')
        await cachedFn('hello')
        await cachedFn('hello')

        expect(called).toBe(2)
      })
    })
  })
})

import { setTimeout } from 'node:timers/promises'
import { Result } from '@swan-io/boxed'
import { Redis } from 'ioredis'
import type { StartedTestContainer } from 'testcontainers'
import { afterAll, beforeAll, describe, expect, test } from 'vitest'
import { RedisClient } from '../src/redis/RedisClient.js'
import { createRedisContainer } from './test-utils.js'

describe('RedisClient', () => {
  let ioRedis: Redis
  let client: RedisClient
  let container: StartedTestContainer

  beforeAll(async () => {
    container = await createRedisContainer().start()

    ioRedis = new Redis({ port: container.getMappedPort(6379) })
    client = RedisClient.apply(ioRedis)
  }, 60_000)

  afterAll(async () => {
    await client.disconnect()
    await container.stop()
  })

  describe('Lock Primitives', () => {
    test('Acquire', async () => {
      const key = 'acquire.lock'
      const value = Date.now().toString()

      // Lock acquired
      const lock1 = await client['acquireLockFor'](key, value, 100)

      expect(lock1).toBe(true)

      // Lock already exists. This should be false.
      const lock2 = await client['acquireLockFor'](key, value, 100)

      expect(lock2).toBe(false)

      // Lock expired. This should be true.
      await setTimeout(110)
      const lock3 = await client['acquireLockFor'](key, value, 100)

      expect(lock3).toBe(true)
    })

    test('Extend', async () => {
      const key = 'acquire_extend.lock'
      const value = Date.now().toString()

      // Lock acquired
      const lock1 = await client['acquireLockFor'](key, value, 100)

      expect(lock1).toBe(true)

      // Attempt to extend lock
      const extend1 = await client['extendLockFor'](key, value, 100)

      expect(extend1).toBe(true)

      // Attempt to extend lock with a different value should return false.
      const extend2 = await client['extendLockFor'](key, 'different value', 100)

      expect(extend2).toBe(false)

      // Attempting to extend after the TTL should fail.
      await setTimeout(110)

      const extend3 = await client['extendLockFor'](key, value, 100)

      expect(extend3).toBe(false)
    })

    test('Release', async () => {
      const key = 'acquire_release.lock'
      const value = Date.now().toString()

      // Lock acquired
      const lock1 = await client['acquireLockFor'](key, value, 100)

      expect(lock1).toBe(true)

      // Released
      const release1 = await client['releaseLockFor'](key, value)

      expect(release1).toBe(true)

      // Trying to release a lock that has already been released should be false.
      const release2 = await client['releaseLockFor'](key, value)

      expect(release2).toBe(false)

      const lock3 = await client['acquireLockFor'](key, value, 100)

      expect(lock3).toBe(true)

      // Attempting to release a lock with a different token value should be false.
      const release3 = await client['releaseLockFor'](key, 'different value')

      expect(release3).toBe(false)

      await expect(client['releaseLockFor'](key, value)).resolves.toBe(true)

      // Releasing an unknown key should return false.
      await expect(client['releaseLockFor']('unknown', 'some value')).resolves.toBe(false)
    })
  })

  describe('LockAndRun - AsyncGenerator', () => {
    const LOCK_KEY = 'test.asyncgen.lock'

    test('Acquires & releases lock and returns values properly', async () => {
      const result = await client.lockAndRun([LOCK_KEY], async function* () {
        const value1 = yield await ioRedis.get(LOCK_KEY)

        expect(value1).toBeDefined()

        return 'result'
      })

      // Value should get "cleared" after the "run" block.
      const value2 = await ioRedis.get(LOCK_KEY)

      expect(value2).toBeNull()
      expect(result).toStrictEqual(Result.Ok('result'))
    })

    test('Acquires, extends & releases lock', async () => {
      await client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          const value1 = yield await ioRedis.get(LOCK_KEY)

          expect(value1).toBeDefined()

          await setTimeout(500)

          // Lock should have "extended" here.
          const value2 = await ioRedis.get(LOCK_KEY)

          expect(value2).toBeDefined()
          expect(value2).toStrictEqual(value1)
        },
        { lockDurationMs: 500, automaticExtensionThreshold: 100 }
      )

      // Value should get "cleared" after the "run" block.
      const value3 = await ioRedis.get(LOCK_KEY)

      expect(value3).toBeNull()
    })

    test('Acquires, extends & releases lock over a long time', async () => {
      const response = await client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          const value1 = yield await ioRedis.get(LOCK_KEY)

          expect(typeof value1).toBe('string')

          yield await setTimeout(500)

          // Lock would have "extended" here.
          const value2 = yield await ioRedis.get(LOCK_KEY)

          expect(value2).toStrictEqual(value1)

          yield await setTimeout(1500)

          // Lock would have "extended" here.
          const value3 = yield await ioRedis.get(LOCK_KEY)

          expect(value3).toStrictEqual(value1)

          return 'response'
        },
        { lockDurationMs: 500, automaticExtensionThreshold: 100 }
      )

      // Value should get "cleared" after the "run" block.
      const value4 = await ioRedis.get(LOCK_KEY)

      expect(value4).toBeNull()
      expect(response).toStrictEqual(Result.Ok('response'))
    }, 5000)

    test('Concatenates multiple lockKeys', async () => {
      await client.lockAndRun(['hello', 'world'], async function* () {
        const value = yield await ioRedis.get('hello-world')

        expect(typeof value).toBe('string')
      })
    })

    test('Return `Error` when lock fails to extend', async () => {
      let setAfterCancel = false

      const response = await client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          const value1 = yield await ioRedis.get(LOCK_KEY)

          expect(typeof value1).toBe('string')

          yield await ioRedis.del(LOCK_KEY)
          yield await setTimeout(500)

          // Since execution is cancelled, execution should not reach this line.
          setAfterCancel = true

          return 'response'
        },
        { lockDurationMs: 500, automaticExtensionThreshold: 100 }
      )

      // Value should get "cleared" after the "run" block.
      const value4 = await ioRedis.get(LOCK_KEY)

      expect(value4).toBeNull()

      expect(setAfterCancel).toBe(false)
      expect(response).toStrictEqual(Result.Error('FAILED_TO_EXTEND' as const))
    })

    test('Return `Error` when failing to acquire lock', async () => {
      const response1 = client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          yield await setTimeout(100)
          return 'response'
        },
        { lockDurationMs: 500, automaticExtensionThreshold: 100 }
      )

      const response2 = client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          yield await setTimeout(100)
          return 'response'
        },
        { lockDurationMs: 500, automaticExtensionThreshold: 100 }
      )

      const response = await Promise.race([response1, response2])

      expect(response).toStrictEqual(Result.Error('ALREADY_LOCKED' as const))

      // Have to still wait for both promises to "complete" so that
      // the LOCK_KEY has actually been released before the next test.
      await Promise.allSettled([response1, response2])
    })

    test('Correctly propagates Error within Result', async () => {
      class CustomError extends Error {
        public readonly name = 'CustomError'
      }

      // eslint-disable-next-line require-yield, @typescript-eslint/require-await
      // biome-ignore lint/correctness/useYield: no reason given
      const response = await client.lockAndRun(['some_random_key'], async function* () {
        throw new CustomError()
      })

      expect(response).toStrictEqual(Result.Error(new CustomError()))
      expect(response.value).toBeInstanceOf(CustomError)
    })

    test('Returns `FAILED_TO_EXTEND` if lock extension fails', async () => {
      const result = await client.lockAndRun(
        [LOCK_KEY],
        async function* () {
          const _ = await ioRedis.del(LOCK_KEY)
          await setTimeout(100)
          yield
          return 'result'
        },
        { lockDurationMs: 80, automaticExtensionThreshold: 60 }
      )

      expect(result).toStrictEqual(Result.Error('FAILED_TO_EXTEND'))
    })
  })

  describe('LockAndRun - Async functions', () => {
    const LOCK_KEY = 'test.async.lock'

    test('Never returns `FAILED_TO_EXTEND` even if lock extension fails', async () => {
      const result = await client.lockAndRun(
        [LOCK_KEY],
        async () => {
          const _ = await ioRedis.del(LOCK_KEY)
          await setTimeout(100)
          return 'result'
        },
        { lockDurationMs: 80, automaticExtensionThreshold: 60 }
      )

      expect(result).toStrictEqual(Result.Ok('result'))
    })
  })
})

{"name": "@upbound/redis", "version": "0.0.0", "license": "UNLICENSED", "type": "module", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./effect": {"types": "./build/effect/index.d.ts", "default": "./build/effect/index.js"}}, "private": true, "scripts": {"dev": "tsc --watch", "build": "tsc", "test": "vitest"}, "dependencies": {"@swan-io/boxed": "^1.1.0", "effect": "^3.17.4", "ioredis": "^5.6.1", "rxjs": "^7.8.2"}, "devDependencies": {"@effect/vitest": "^0.24.1", "@testcontainers/redis": "^11.3.1", "testcontainers": "^11.3.1", "tsconfig": "workspace:*", "vitest": "^3.2.4"}}
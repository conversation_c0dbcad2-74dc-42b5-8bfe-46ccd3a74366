export const flattenRecord = (rootKey: string, obj: object) => {
  const rootObj: Record<string, unknown> = {}

  for (const [key, value] of Object.entries(obj)) {
    const newKey = `${rootKey}.${key}`

    if (typeof value === 'object' && value != null) {
      Object.assign(rootObj, flattenRecord(newKey, value))
    } else {
      rootObj[newKey] = value
    }
  }

  return rootObj
}

export const filterEmptyStringValuesFromObject = (value: Record<string, unknown>) => {
  return Object.fromEntries(Object.entries(value).filter(([_, v]) => v !== '' && v !== null && v !== undefined))
}

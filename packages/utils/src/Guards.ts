export function isDefined<T>(value: T | undefined): value is T {
  return value !== undefined
}

export const isNotUndefined = isDefined

export function isNotNull<T>(value: T | null): value is T {
  return value !== null
}

export function isNotFalse<T>(value: T | false): value is T {
  return value !== false
}

export function isNotNullOrUndefined<T>(value: T | null | undefined): value is T {
  return isNotNull(value) && isDefined(value)
}

export const propertyIsNotNullOrUndefined =
  <T, K extends keyof T>(key: K) =>
  (value: T): value is T & Record<K, NonNullable<T[K]>> => {
    return isNotNullOrUndefined(value[key])
  }

export const propertyIsNotNullOrUndefined_ = <T, K extends keyof T>(
  key: K,
  value: T
): value is T & Record<K, NonNullable<T[K]>> => {
  return isNotNullOrUndefined(value[key])
}

export const propertyIsNullish =
  <T, K extends keyof T>(key: K) =>
  (value: T): value is T & Record<K, null | undefined> => {
    return !isNotNullOrUndefined(value[key])
  }

export const propertySatisfiesFilter =
  <T, K extends keyof T, U extends T[K]>(key: K, filter: (item: T[K]) => item is U) =>
  (value: T): value is T & Record<K, U> => {
    return filter(value[key])
  }

export const excludeAndNarrow =
  <V, K extends keyof V, P extends string>(key: K, prop: P) =>
  (value: V): value is Exclude<V, Record<K, P>> => {
    return value[key] !== prop
  }

export const extractAndFilter =
  <V, K extends keyof V, P extends string>(key: K, prop: P) =>
  (value: V): value is Extract<V, Record<K, P>> => {
    return value[key] === prop
  }

export type NonEmptyArray<T> = [T, ...T[]]
export type WidenNonEmptyArray<T> = T extends NonEmptyArray<infer U> ? U[] : never

export function nonEmptyOf<T>(item: T): NonEmptyArray<T> {
  return [item]
}

export const neOf = nonEmptyOf

export function nonEmptyFrom<T>(item: [T, ...T[]]): NonEmptyArray<T> {
  return item
}

export function nonEmptyLast<T>(arr: NonEmptyArray<T>): T {
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return arr[arr.length - 1]!
}

export function nonEmptyMap<T, U>(
  items: NonEmptyArray<T>,
  fn: (x: T, index: number, array: T[]) => U
): NonEmptyArray<U> {
  return items.map(fn) as NonEmptyArray<U>
}

export function groupBy<T>(items: T[], keyFn: (item: T) => string): Record<string, NonEmptyArray<T>> {
  const obj: Record<string, T[]> = {}

  for (const item of items) {
    const key = keyFn(item)

    if (!obj[key]) {
      obj[key] = []
    }

    obj[key]?.push(item)
  }

  return obj as Record<string, NonEmptyArray<T>>
}

/**
 * NE Methods
 */

/**
 * Turn any given array into a sliding array of tuples. i.e
 * slidingTuple([1,2,3,4,5]) = [[1,2], [2,3], [3,4], [4,5]]
 */
export function slidingTuple<T>(array: NonEmptyArray<T>): NonEmptyArray<[T, T]> {
  return array.slice(0, -1).map((_, index) => [array[index], array[index + 1]]) as NonEmptyArray<[T, T]>
}

/**
 * Verifies that an array is non-empty which allows safe access to the first element in the array
 */
export function isNonEmpty<T>(items?: T[]): items is NonEmptyArray<T> {
  return items ? items.length > 0 : false
}

/**
 * Maps a function over every element on a NonEmptyArray and returns a NonEmptyArray
 */
export function neMap<T, U>(items: NonEmptyArray<T>, fn: (x: T, index: number, array: T[]) => U): NonEmptyArray<U> {
  return items.map(fn) as NonEmptyArray<U>
}

export function neFlat<T>(items: NonEmptyArray<T>[]): NonEmptyArray<T> {
  return items.flat() as NonEmptyArray<T>
}

/**
 * Add an item to the end of a `NonEmptyArray` and return a `NonEmptyArray`
 */
export function neAppend<T>(items: NonEmptyArray<T>, item: T): NonEmptyArray<T> {
  return [...items, item] as NonEmptyArray<T>
}

/**
 * Get a unique set of items from a `NonEmptyArray` and return a `NonEmptyArray`
 */
export function neUniq<T>(items: NonEmptyArray<T>): NonEmptyArray<T> {
  return Array.from(new Set(items)) as NonEmptyArray<T>
}

/**
 * A callback to filter out nullish items with type safety.
 * Use with `Array.filter()`. e.g. `items.filter(isNonNullish)`
 */
export function isNonNullish<T>(value: T | null | undefined): value is T {
  if (value === null || value === undefined) return false

  return true
}

/**
 * Group items in an array where each item in the result is also a `NonEmptyArray`
 */
export function neGroupBy<T, U extends string | number | bigint | boolean>(
  items: NonEmptyArray<T>,
  groupByFn: (x: T) => U
): Map<U, NonEmptyArray<T>> {
  return items.reduce((acc, item) => {
    const key = groupByFn(item)
    const group = acc.get(key) || []
    return acc.set(key, [...group, item])
  }, new Map<U, NonEmptyArray<T>>())
}

export function neZipExact<T, U>(A: NonEmptyArray<T>, B: NonEmptyArray<U>): NonEmptyArray<[T, U]> {
  if (A.length !== B.length) {
    throw new Error('Arrays must be the same length')
  }

  return A.map((a, index) => [a, B[index]]) as NonEmptyArray<[T, U]>
}

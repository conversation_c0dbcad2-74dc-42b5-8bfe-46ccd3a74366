{"name": "@upbound/utils", "version": "0.0.0", "type": "module", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./Array": {"types": "./build/Array.d.ts", "default": "./build/Array.js"}, "./Guards": {"types": "./build/Guards.d.ts", "default": "./build/Guards.js"}, "./Hash": {"types": "./build/Hash.d.ts", "default": "./build/Hash.js"}, "./Record": {"types": "./build/Record.d.ts", "default": "./build/Record.js"}, "./String": {"types": "./build/String.d.ts", "default": "./build/String.js"}}, "license": "UNLICENSED", "private": true, "scripts": {"dev": "tsc --watch", "build": "tsc", "test": "vitest"}, "devDependencies": {"@types/node": "^22.16.5", "tsconfig": "workspace:*", "vitest": "^3.2.4"}}
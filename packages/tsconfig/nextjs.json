{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "extends": "./base.json", "compilerOptions": {"module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "plugins": [{"name": "next"}], "target": "ES2022", "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "declaration": false, "declarationMap": false, "incremental": true, "isolatedModules": true, "esModuleInterop": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "noEmit": true, "resolveJsonModule": true, "noUncheckedIndexedAccess": true, "useDefineForClassFields": false}}
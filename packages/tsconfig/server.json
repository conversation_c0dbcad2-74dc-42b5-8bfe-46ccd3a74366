{"$schema": "https://json.schemastore.org/tsconfig", "display": "Server", "compilerOptions": {"module": "NodeNext", "target": "ES2022", "lib": ["ES2023"], "declaration": true, "declarationMap": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "noEmitOnError": true, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "noUncheckedIndexedAccess": true, "noPropertyAccessFromIndexSignature": true}, "exclude": ["node_modules"]}
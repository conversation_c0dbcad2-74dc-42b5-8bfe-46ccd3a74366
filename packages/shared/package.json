{"name": "@upbound/shared", "version": "0.0.0", "type": "module", "exports": {".": {"import": "./build/index.js", "require": "./build/index.js"}, "./agents": {"import": "./build/agents/index.js", "require": "./build/agents/index.js"}, "./workflow": {"import": "./build/workflow/index.js", "require": "./build/workflow/index.js"}, "./utils": {"import": "./build/utils/index.js", "require": "./build/utils/index.js"}, "./repo/*": {"import": "./build/repo/*", "require": "./build/repo/*"}, "./llm": {"import": "./build/llm/index.js", "require": "./build/llm/index.js"}}, "license": "UNLICENSED", "private": true, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@effect/platform": "^0.87.13", "@effect/platform-node": "^0.88.2", "@json2csv/node": "^7.0.6", "@slack/web-api": "^7.9.2", "@types/lodash-es": "^4.17.12", "@upbound/postgres": "workspace:*", "@upbound/redis": "workspace:*", "@upbound/utils": "workspace:*", "agentkeepalive": "^4.6.0", "ai": "^4.3.19", "cheerio": "1.0.0", "dependency-graph": "^1.0.0", "effect": "^3.17.4", "got-esm": "npm:got@14", "hash-wasm": "^4.11.0", "libphonenumber-js": "^1.11.13", "lodash-es": "^4.17.21", "openai": "^5.11.0", "stripe": "^17.7.0", "tiktoken": "^1.0.21", "turndown": "^7.2.0", "uuid": "^11.1.0", "zod": "^3.25.76"}, "scripts": {"dev": "tsc --watch", "test": "vitest", "build": "tsc"}, "devDependencies": {"@slack/web-api": "^7.0.4", "@types/node": "^22.16.5", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "tsconfig": "workspace:*", "vitest": "^3.2.4"}}
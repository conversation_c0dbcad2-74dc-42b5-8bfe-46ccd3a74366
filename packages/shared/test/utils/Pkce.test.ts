import { assert, describe, test } from 'vitest'
import { generate, validate } from '../../src/utils/Pkce'

describe('Pkce', () => {
  test('Validate Known PKCE', () => {
    const pkce = {
      challenge: 'jHftugitip8zSeBNytaUvP5oVUghSb_aBIeP0uqAHxs',
      verifier: '0o5zwFeHxBheAvcCzQz9tkFzQXXfx8VRTGG-8m4EA9g',
      method: 'S256' as const,
    }

    assert(validate(pkce) === true)
  })

  test('Generate & Validate PKCE', () => {
    const pkce = generate()
    assert(validate(pkce) === true)
  })
})

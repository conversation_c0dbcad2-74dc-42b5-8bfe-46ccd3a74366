import { describe, expect, test } from 'vitest'
import { trimAndCleanHTML } from '../../src/llm/HTMLUtils'

describe('HtmlTrimmer', () => {
  describe('Remove all script, style and comments from HTML and remove all empty text nodes', () => {
    test('Remove all script, style and comments from HTML and trim all whitespace', () => {
      const html = `
<html>
<head>
<style>
  h1 {color:red;}
  p {color:blue;}
</style>
</head>
<body>
<img src="img_girl.jpg" alt="Girl in a jacket" width="500" height="600">

<h1>A heading</h1>
<p style="" class="" id="para">
A paragraph.
Another para
  <a href="http://url.com">Link</a>
</p>

<div><!-- Random comment --><img src="img_girl.jpg" alt="Girl in a jacket" width="500" height="600"></div>

<script>
document.getElementById("demo").innerHTML = "Hello JavaScript!";
</script>

<script type="text/javascript">
//<![CDATA[
let i = 10;
if (i < 5) {
  // some code
}
//]]>
</script>

<script type="module" src="main.js"></script>
<script nomodule src="fallback.js"></script>
</body>
</html>`

      const trimmed = trimAndCleanHTML(html)

      expect(trimmed).toEqual('<h1>A heading</h1><p>A paragraph.Another para<a href="http://url.com">Link</a></p>')
    })

    test('Remove all unneeded attributes from tags', () => {
      const html = `<html><body><div><a class="skip-link screen-reader-text" href="#content">Skip to content</a><header id="masthead" class="site-header dsvy-header-style-4 dsvy-sticky-logo-no">Header</header></div></body></html>`

      const trimmed = trimAndCleanHTML(html)

      expect(trimmed).toEqual('<div><a href="#content">Skip to content</a><header>Header</header></div>')
    })
  })
})

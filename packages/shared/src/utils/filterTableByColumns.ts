import type { ColumnId, ColumnMap } from '../workflow/WorkflowTableTypes.js'

export function filterTableByColumns<
  TableDefinition extends {
    name: string
    table_definition: {
      columnDefinitions: ColumnMap
    }
  },
  TableRows extends {
    row_id: string
    row_value: {
      data: Record<string, unknown>
    }
  }[],
>({
  columnNames,
  tableDefinition,
  tableRows,
}: {
  columnNames: string[]
  tableDefinition: TableDefinition
  tableRows: TableRows
}) {
  const columnNameToColumnIdMap: Record<string, ColumnId> = {}
  const filteredColumnDefinitions: ColumnMap = {}

  for (const [columnId, columnDef] of Object.entries(tableDefinition.table_definition.columnDefinitions)) {
    if (columnNames.includes(columnDef.columnName)) {
      columnNameToColumnIdMap[columnDef.columnName] = columnId
    }
  }

  for (const columnName of columnNames) {
    const columnId = columnNameToColumnIdMap[columnName]

    if (columnId) {
      filteredColumnDefinitions[columnId] = tableDefinition.table_definition.columnDefinitions[columnId]!
    }
  }

  const filteredRows = tableRows.map(row => {
    const filteredData: Record<string, unknown> = {}

    for (const columnName of columnNames) {
      const columnId = columnNameToColumnIdMap[columnName]
      if (columnId) {
        filteredData[columnId] = row.row_value.data[columnId]!
      }
    }

    return {
      row_id: row.row_id,
      row_value: {
        data: filteredData,
      },
    }
  })

  return {
    tableDefinition: {
      name: tableDefinition.name,
      table_definition: {
        ...tableDefinition.table_definition,
        columnDefinitions: filteredColumnDefinitions,
      },
    },
    rows: filteredRows,
  } as const
}

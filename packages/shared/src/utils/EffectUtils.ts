import type { Duration } from 'effect'
import { Effect, pipe, Schedule, Stream, Tracer } from 'effect'

const toPairs = <T>(arr: T[]): [T, T | undefined][] => {
  const pairs: [T, T | undefined][] = []
  for (let i = 0; i < arr.length; i += 2) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    pairs.push([arr[i]!, arr[i + 1]!])
  }

  return pairs
}

/**
 * Like `Stream.interleave` but evenly interleaves all streams in an array
 * recursively.
 */
export const interleaveAll = <T, U, V>(streams: Stream.Stream<T, U, V>[]) => {
  type StreamItem = { stream: Stream.Stream<T, U, V>; count: number }

  const interleaveIfNeeded = (stream1: StreamItem, stream2: StreamItem | undefined) => {
    if (stream2) {
      return {
        stream: Stream.interleaveWith(
          stream1.stream,
          stream2.stream,
          Stream.forever(Stream.make(...Array(stream1.count).fill(true), ...Array(stream2.count).fill(false)))
        ),
        count: stream1.count + stream2.count,
      }
    } else {
      return stream1
    }
  }

  const pairs = toPairs(streams.map(stream => ({ stream, count: 1 })))
  let result = pairs

  while (result.length > 1) {
    const interleaved = result.map(([stream1, maybeStream2]) => interleaveIfNeeded(stream1, maybeStream2))
    result = toPairs(interleaved)
  }

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  const stream = interleaveIfNeeded(result[0]![0], result[0]![1])

  return stream.stream
}

export const ExponentialBackoffSchedule = (base: Duration.DurationInput, retryAttempts: number) => {
  return pipe(Schedule.exponential(base), Schedule.intersect(Schedule.recurs(retryAttempts)))
}

export const B3TraceHeader = Effect.match(Effect.currentSpan, {
  onFailure: () => void 0,
  onSuccess: span =>
    `${span.traceId}-${span.spanId}-${span.sampled ? '1' : '0'}${
      span.parent._tag === 'Some' ? `-${span.parent.value.spanId}` : ''
    }`,
})

export const unsafeParseB3SingleLineHeader = (b3Header: string) => {
  const parts = b3Header.split('-')
  if (parts.length >= 2) {
    return Tracer.externalSpan({
      traceId: parts[0]!,
      spanId: parts[1]!,
      sampled: parts[2] ? parts[2] === '1' : true,
    })
  }
}

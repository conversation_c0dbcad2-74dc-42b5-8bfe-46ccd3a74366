/**
 * The Proof Key for Code Exchange (PKCE) is a security extension to OAuth 2.0.
 * Details : https://blog.postman.com/what-is-pkce
 */
import { createHash, randomBytes } from 'node:crypto'

export type PKCE = {
  method: 'S256'
  verifier: string
  challenge: string
}

/**
 * @internal
 */
const computeChallengeFor = (verifier: string, method: PKCE['method']) => {
  switch (method) {
    case 'S256':
      return createHash('sha256').update(verifier).digest('base64url')
  }
}

export const generate = (numBytes = 32): PKCE => {
  const method = 'S256' as const
  const verifier = randomBytes(numBytes).toString('base64url')
  const challenge = computeChallengeFor(verifier, method)

  return {
    method,
    verifier,
    challenge,
  }
}

export const validate = (pkce: PKCE) => {
  const computedChallenge = computeChallengeFor(pkce.verifier, pkce.method)
  return computedChallenge === pkce.challenge
}

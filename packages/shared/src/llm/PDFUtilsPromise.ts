import { FetchHttpClient } from '@effect/platform'
import { NodeCommandExecutor, NodeFileSystem, NodePath } from '@effect/platform-node'
import { Effect } from 'effect'
import * as PDFUtils from './PDFUtils.js'

export const readPdfFromUrl = (url: string, abortSignal?: AbortSignal) =>
  Effect.runPromise(
    PDFUtils.readPdfFromUrl(url).pipe(
      Effect.catchTag('PdfMaxFileSizeExceeded', error =>
        Effect.succeed(`Error: PDF max file size exceeded (${error.maxFileSizeInBytes / 1024 / 1024}mb)`)
      ),
      Effect.catchTag('ResponseError', error =>
        Effect.succeed(
          error.response.status === 404
            ? 'Error: PDF not found'
            : error.response.status >= 500
              ? 'Error: Server error'
              : 'Error: Bad request'
        )
      ),
      Effect.provide(FetchHttpClient.layer),
      Effect.provide(NodePath.layer),
      Effect.provide(NodeCommandExecutor.layer),
      Effect.provide(NodeFileSystem.layer)
    ),
    { signal: abortSignal }
  )

import { randomUUID } from 'node:crypto'
import { Command, CommandExecutor, FileSystem, HttpClient, Path } from '@effect/platform'
import { Data, Effect, Stream } from 'effect'

export function bytesToSize(bytes: number) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Byte'
  const i = Number.parseInt(String(Math.floor(Math.log(bytes) / Math.log(1024))))
  return Math.round(bytes / Math.pow(1024, i)) + ' ' + sizes[i]
}

export class PdfMaxFileSizeExceeded extends Data.TaggedError('PdfMaxFileSizeExceeded')<{
  maxFileSizeInBytes: number
}> {}

const downloadFileScoped = (url: string, maxFileSizeInBytes: number) =>
  Effect.gen(function* () {
    const client = yield* HttpClient.HttpClient
    const response = yield* client.get(url)

    if (
      response.headers['content-length'] &&
      Number.parseInt(response.headers['content-length']) > maxFileSizeInBytes
    ) {
      return yield* new PdfMaxFileSizeExceeded({ maxFileSizeInBytes })
    }

    const fs = yield* FileSystem.FileSystem
    const path = yield* Path.Path

    const filePath = yield* Effect.acquireRelease(
      Effect.sync(() => {
        const sourceFileName = new URL(url).pathname.split('/').slice(-1)[0]!
        const filename = `${randomUUID()}-${sourceFileName}`
        return path.join('/tmp', filename)
      }),
      filePath =>
        Effect.zipRight(Effect.logInfo(`PDFUtils: Deleted ${url}`), fs.remove(filePath).pipe(Effect.ignoreLogged))
    )

    const [streamA, streamB] = yield* response.stream.pipe(Stream.broadcast(2, 10))

    const persistFile = streamA.pipe(Stream.run(fs.sink(filePath)))

    const validateFileSize = streamB.pipe(
      Stream.scan(0, (acc, e) => acc + e.byteLength),
      Stream.mapEffect(totalBytes =>
        totalBytes > maxFileSizeInBytes ? new PdfMaxFileSizeExceeded({ maxFileSizeInBytes }) : Effect.void
      ),
      Stream.runDrain
    )

    yield* Effect.zip(persistFile, validateFileSize, { concurrent: true })

    return filePath
  })

const MAX_FILE_SIZE_IN_MB = 50

export const readPdfFromUrl = (url: string) =>
  Effect.gen(function* () {
    const filePath = yield* downloadFileScoped(url, MAX_FILE_SIZE_IN_MB * 1024 * 1024)
    const commandExecutor = yield* CommandExecutor.CommandExecutor
    const pdfTextContent = yield* commandExecutor.string(Command.make('pdftotext', filePath, '-'))
    return pdfTextContent
  }).pipe(
    Effect.annotateSpans({ url }),
    Effect.withSpan('readPdfFromUrl'),
    Effect.tapError(e => Effect.logError(`PDFUtils: Error downloading or converting PDF`, e)),
    Effect.scoped
  )

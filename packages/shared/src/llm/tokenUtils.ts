import type { TiktokenModel } from 'tiktoken'
import { encoding_for_model } from 'tiktoken'

export const limitTokens = (text: string, maxTokens: number, model: TiktokenModel) => {
  const encoder = encoding_for_model(model)
  const tokens = encoder.encode(text)
  const slicedTokens = tokens.slice(0, maxTokens)
  const slicedText = new TextDecoder().decode(encoder.decode(slicedTokens))
  encoder.free()
  return slicedText
}

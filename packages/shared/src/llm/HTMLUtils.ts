import { load } from 'cheerio'
import TurndownService from 'turndown'

/**
 * Parses any given HTML and performs the following operations :
 * - Removes all script, style and comments from HTML and remove all empty text nodes
 * - Removes all attributes from tags
 *  - Except for anchor tags, which will only keep the `href` attribute.
 * - Trims all text nodes to remove any unnecessary whitespace.
 * - Removes all empty tags
 *
 */
export const trimAndCleanHTML = (html: string) => {
  const $ = load(html)

  // Remove comments, script, style and empty text tags.
  $('*')
    .contents()
    .filter(
      (_index, node) =>
        node.type === 'comment' ||
        node.type === 'script' ||
        node.type === 'style' ||
        (node.type === 'text' && node.data.trim() === '')
    )
    .remove()

  // Remove all image,video,figure tags
  $('img').remove()
  $('video').remove()
  $('figure').remove()

  $('*').each((_index, node) => {
    if (node.type === 'tag') {
      // Remove all attributes on all tags except anchor tags ( <a> )
      if (node.tagName !== 'a') {
        node.attribs = {}
      }

      // On anchor tags, only keep the `href` attribute.
      // But remove everything else.
      if (node.tagName === 'a') {
        if (node.attribs['href']) {
          node.attribs = {
            href: node.attribs['href'],
          }
        } else {
          node.attribs = {}
        }
      }
    }
  })

  // Trim all text nodes
  $('*')
    .contents()
    .filter((_index, node) => node.type === 'text')
    .each((_index, node) => {
      if (node.type === 'text') {
        node.data = node.data.trim()
      }
    })

  // Remove all empty tags
  $('body *:not(:has(*)):empty').remove()

  const body =
    $('body')
      .html()
      // Remove all new lines.
      ?.replace(/\n/g, '') ?? ''

  return body
  // // Remove whitespace (space and tabs) before tags
  // .replace(/[\t ]+\</g, '<')
  // // Remove whitespace between tags
  // .replace(/\>[\t ]+\</g, '><')
  // // Remove whitespace after tags
  // .replace(/\>[\t ]+$/g, '>')
}

export const htmlToMarkdown = (html: string) => {
  const turndownService = new TurndownService({
    headingStyle: 'atx',
    codeBlockStyle: 'fenced',
  })
    .remove('video')
    .remove('figure')
    .remove('iframe')
    .remove('noscript')
    .remove('style')
    .remove('script')
    .remove('head')

  return turndownService.turndown(html)
}

export const extractDataFromSimilarWeb = (html: string) => {
  const $ = load(html)
  $('svg').remove()
  $('img').remove()
  $('iframe').remove()
  $('video').remove()
  $('figure').remove()
  $('script').remove()
  $('style').remove()

  const totalVisitsText = htmlToMarkdown($(`.wa-traffic__main-content`).html() ?? '')
  const websiteSummary = htmlToMarkdown($(`.wa-summary__rankings`).html() ?? '')

  return totalVisitsText + websiteSummary
}

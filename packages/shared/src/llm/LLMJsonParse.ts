import type { ZodRaw<PERSON>hape, z } from 'zod'

/**
 * LLM's don't return properly formatted JSON. The JSON structure often contains single or
 * double encoded new line characters at wrong positions in the JSON structure. This function tries
 * to account for that problem by replacing all instances of single and double encoded new line characters with spaces
 * ( which always results in valid JSON ), parsing them, validating them against the provided Zod schema,
 * and then replacing the spaces with new line characters again.
 *
 * This ensures that even when the LLM result contains multi-line strings, all multi-line strings are properly
 * parsed while also retrieving valid JSON.
 *
 * Only works on flat JSON objects with no nesting.
 *
 * Local Reference to the problem and solution : https://upboundhq.slack.com/archives/C057FU0MGGN/p1696545682742719?thread_ts=**********.099459&cid=C057FU0MGGN
 */
export const llmJsonParse = <T extends ZodRawShape>(zodSchema: z.ZodObject<T>, jsonString: string) => {
  try {
    return zodSchema.parse(JSON.parse(jsonString))
  } catch (e: unknown) {
    try {
      const space = ' '.repeat(20)
      const jsonWithSpacesReplaced = jsonString.replaceAll('\\n', space).replaceAll('\n', space)
      const jsonStruct = JSON.parse(jsonWithSpacesReplaced)
      const parsed = zodSchema.parse(jsonStruct)

      const transformed = Object.fromEntries(
        Object.entries(parsed).map(([key, value]) => [
          key,
          typeof value === 'string'
            ? value
                .split(space)
                .map(x => x.trim())
                .join('\n')
            : value,
        ])
      )

      return transformed as z.output<z.ZodObject<T>>
    } catch {
      throw e
    }
  }
}

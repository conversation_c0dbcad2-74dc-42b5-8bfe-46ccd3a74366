import { isNotNull } from '@upbound/utils'
import type OpenA<PERSON> from 'openai'
import { zodResponseFormat } from 'openai/helpers/zod'
import type { ChatCompletionCreateParamsBase, ChatCompletionMessageParam } from 'openai/resources/chat/completions.mjs'
import { type ZodObject, z } from 'zod'

export class WorkflowLLM {
  constructor(private readonly openAI: OpenAI) {}

  makeOpenAICall = async (params: {
    customerId: string
    prompts: { system: string; user: string }
    // biome-ignore lint/suspicious/noExplicitAny: no reason given
    responseFormat: 'string' | ZodObject<any, any, any, any, any>
    modelOpts?: Pick<
      ChatCompletionCreateParamsBase,
      'model' | 'temperature' | 'frequency_penalty' | 'top_p' | 'logit_bias' | 'logprobs' | 'top_logprobs'
    >
    abortSignal?: AbortSignal
  }) => {
    const { responseFormat, modelOpts, prompts, customerId, abortSignal } = params

    const messages: ChatCompletionMessageParam[] = [
      prompts.system
        ? {
            role: 'system' as const,
            content: prompts.system.trim(),
          }
        : null,
      { role: 'user' as const, content: prompts.user.trim() },
    ].filter(isNotNull)

    const model = responseFormat === 'string' ? (modelOpts?.model ?? ('gpt-4o-mini' as const)) : 'gpt-4o-2024-08-06'
    const { openAI } = this

    const stats = {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    }

    const agentLoop = async () => {
      let i = 0
      while (i++ < 20) {
        const response = await openAI.chat.completions.create(
          {
            ...(modelOpts ?? {}),
            model,
            user: customerId,
            messages,
            response_format:
              responseFormat === 'string' ? { type: 'text' } : zodResponseFormat(responseFormat, 'result'),
          },
          { signal: abortSignal }
        )

        stats.promptTokens = stats.promptTokens + (response.usage?.prompt_tokens ?? 0)
        stats.completionTokens = stats.completionTokens + (response.usage?.completion_tokens ?? 0)
        stats.totalTokens = stats.totalTokens + (response.usage?.total_tokens ?? 0)

        const responseMessage = response.choices[0]?.message

        if (responseMessage) {
          messages.push(responseMessage)
        }

        if (responseMessage?.content) {
          return {
            status: 'success' as const,
            content:
              responseFormat === 'string' ? responseMessage.content : (JSON.parse(responseMessage.content) as unknown),
          }
        }
      }

      return {
        status: 'error' as const,
        error: 'No content',
      }
    }

    const result = await agentLoop()
    return { result, stats }
  }

  shortFieldNameForPrompt = (customerId: string, prompt: string) => {
    const responseObj = z.object({ fieldName: z.string().describe('A short and concise field name') })

    return this.makeOpenAICall({
      customerId,
      prompts: {
        system: 'You are an excellent spreadsheet user',
        user: `Come up with a short and concise column name (in "PascalCase") for a table to store the result of the following prompt:\n\n"""${prompt}"""`,
      },
      responseFormat: responseObj,
      modelOpts: { model: 'gpt-4o-mini' },
    }).then(({ result }) => {
      if (result.status === 'success') {
        return responseObj.parse(result.content).fieldName
      } else {
        console.error(result.error)
        return null
      }
    })
  }

  responseSchema = (customerId: string, prompt: string) => {
    const responseObj = z.object({
      properties: z
        .object({
          name: z.string().describe('The name of the property'),
          dataType: z.enum(['string', 'url', 'number', 'boolean', 'date']).describe('The data type of the property'),
          description: z
            .string()
            .describe('A short and concise description of what the user is expecting in this property.'),
        })
        .array()
        .describe('A JSON schema for the properties that the user is interested in'),
    })

    return this.makeOpenAICall({
      customerId,
      prompts: {
        system: `You are an expert prompt engineer and schema designer`,
        user: `The ƒollowing is a user prompt to an LLM. Evaluate this prompt and design a JSON schema for all the properties that the user expects as an output from this prompt.

<user_prompt>
${prompt}
</user_prompt>

The JSON schema should include property names in "camelCase" and should include the following properties:
- result: The primary result of the prompt.
`,
      },
      responseFormat: responseObj,
      modelOpts: { model: 'gpt-4o-mini' },
    }).then(({ result }) => {
      if (result.status === 'success') {
        return responseObj.parse(result.content)
      } else {
        console.error(result.error)
        return null
      }
    })
  }
}

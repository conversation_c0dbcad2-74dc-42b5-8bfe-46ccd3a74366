import type { SqlClient, TransactionSqlClient } from '@upbound/postgres'
import { chunksOf } from 'effect/Array'
import type { ColumnDefinition, ColumnId } from '../workflow/WorkflowTableTypes.js'

export interface WorkflowTable {
  name: string
  customerId: string
  workflowTableId: number
  numRows: number
  deletedAt?: Date | null
  tableDefinition: {
    isOnboardingTable?: boolean
    columnDefinitions: Record<string, ColumnDefinition>
  }
}

export interface WorkflowTableRow {
  customerId: string
  workflowTableId: number
  rowId: string
  createdAt: Date
  updatedAt: Date
  rowValue: {
    data: Record<ColumnId, unknown>
  }
}

export class DBWorkflow {
  constructor(private readonly sql: SqlClient) {}

  WorkflowTableFields = this.sql`
    id "workflowTableId",
    customer_id "customerId",
    name,
    num_rows "numRows",
    deleted_at "deletedAt",
    table_definition "tableDefinition"`

  WorkflowTableRowFields = this.sql`
    workflow_table_id "workflowTableId",
    row_id "rowId",
    customer_id "customerId",
    row_value "rowValue",
    created_at "createdAt",
    updated_at "updatedAt"`

  getOnboardingTableId(params: { customerId: string }, sql = this.sql) {
    return sql<WorkflowTable[]>`
      SELECT ${this.WorkflowTableFields}
      FROM workflow_table
      WHERE customer_id = ${params.customerId}
      AND table_definition->>'isOnboardingTable' = 'true'
    `.then(x => x[0]?.workflowTableId ?? null)
  }

  private async createTableWithRows(params: {
    customerId: string
    userId: string
    name: string
    tableDefinition: WorkflowTable['tableDefinition']
    rows: { row_id: string; row_value: { data: Record<string, unknown> } }[]
  }) {
    const { table } = await this.createWorkflowTable({
      customerId: params.customerId,
      userId: params.userId,
      name: params.name,
      tableDefinition: params.tableDefinition,
    })

    await this.addRows({
      customerId: params.customerId,
      workflowTableId: table.workflowTableId,
      rows: params.rows.map(x => ({ rowId: x.row_id, rowData: x.row_value.data })),
    })

    return table
  }

  async createOnboardingTable(params: { customerId: string; userId: string; columnNames?: string[] }) {
    return this.createTableWithRows({
      ...params,
      name: 'Welcome to Freckle!',
      tableDefinition: {
        columnDefinitions: {},
        isOnboardingTable: true,
      },
      rows: [],
    })
  }

  createWorkflowTable(
    params: { customerId: string; userId: string; name: string; tableDefinition?: WorkflowTable['tableDefinition'] },
    sql = this.sql
  ) {
    const payload = {
      user_id: params.userId,
      customer_id: params.customerId,
      name: params.name,
      table_definition: params.tableDefinition ?? {
        columnDefinitions: {},
      },
    }

    const run = async (sql: SqlClient) => {
      const table = await sql<[WorkflowTable]>`
        INSERT INTO workflow_table ${sql(payload)}
        RETURNING ${this.WorkflowTableFields}
      `.then(x => x[0])

      return {
        table,
        views: [],
      }
    }

    return sql ? run(sql) : this.sql.begin(run)
  }

  updateWorkflowTable(params: { customerId: string; workflowTableId: number; name: string }) {
    return this.sql<WorkflowTable[]>`
      UPDATE workflow_table
      SET name = ${params.name}
      WHERE id = ${params.workflowTableId}
      AND customer_id = ${params.customerId}
      RETURNING ${this.WorkflowTableFields}
    `.then(x => x[0])
  }

  deleteWorkflowTable(params: { customerId: string; workflowTableId: number }) {
    return this.sql.begin(async sql => {
      const success = await sql<[WorkflowTable]>`
        DELETE FROM workflow_table
        WHERE customer_id = ${params.customerId}
        AND id = ${params.workflowTableId}
      `.then(x => x.count === 1)

      if (success) {
        return true
      } else {
        throw new Error('fail')
      }
    })
  }

  trashWorkflowTable(params: { customerId: string; workflowTableId: number }) {
    return this.sql.begin(async sql => {
      const success = await sql<[WorkflowTable]>`
        UPDATE workflow_table
        SET deleted_at = NOW()
        WHERE customer_id = ${params.customerId}
        AND id = ${params.workflowTableId}
        AND deleted_at IS NULL
      `.then(x => x.count === 1)

      if (success) {
        return true
      } else {
        throw new Error('fail')
      }
    })
  }

  trashWorkflowTables(params: { customerId: string; workflowTableIds: ReadonlyArray<number> }) {
    return this.sql.begin(async sql => {
      try {
        await sql<[WorkflowTable]>`
          UPDATE workflow_table
          SET deleted_at = NOW()
          WHERE customer_id = ${params.customerId}
          AND id = ANY(${params.workflowTableIds})
          AND deleted_at IS NULL`

        return { success: true as const }
      } catch (e) {
        return { success: false as const, message: 'Failed to persist' }
      }
    })
  }

  restoreWorkflowTables(params: { customerId: string; workflowTableIds: ReadonlyArray<number> }) {
    return this.sql.begin(async sql => {
      try {
        await sql<[WorkflowTable]>`
          UPDATE workflow_table
          SET deleted_at = NULL
          WHERE customer_id = ${params.customerId}
          AND id = ANY(${params.workflowTableIds})
          AND deleted_at IS NOT NULL`

        return { success: true } as const
      } catch (e) {
        return { success: false, error: 'Failed to persist' } as const
      }
    })
  }

  upsertColumn(
    params: {
      customerId: string
      workflowTableId: number
      columnDefinition: ColumnDefinition
    },
    sql = this.sql
  ) {
    return sql<WorkflowTable[]>`
      UPDATE workflow_table
      SET table_definition = JSONB_SET(table_definition, ARRAY['columnDefinitions', ${params.columnDefinition.columnId}::TEXT], ${params.columnDefinition})
      WHERE id = ${params.workflowTableId} AND customer_id = ${params.customerId}
      RETURNING ${this.WorkflowTableFields}
    `.then(x => x[0])
  }

  upsertColumns(
    params: {
      customerId: string
      workflowTableId: number
      columnDefinitions: Record<string, ColumnDefinition>
    },
    sql = this.sql
  ) {
    return sql<WorkflowTable[]>`
      UPDATE workflow_table
      SET table_definition = JSONB_SET(table_definition, ARRAY['columnDefinitions'], table_definition->'columnDefinitions' || ${params.columnDefinitions})
      WHERE id = ${params.workflowTableId} AND customer_id = ${params.customerId}
      RETURNING ${this.WorkflowTableFields}
    `.then(x => x[0])
  }

  removeColumn(params: { customerId: string; workflowTableId: number; columnId: string }) {
    return this.sql.begin(async sql => {
      const updatedTable = await sql<WorkflowTable[]>`
        UPDATE workflow_table
        SET table_definition = table_definition #- ARRAY['columnDefinitions', ${params.columnId}::TEXT]
        WHERE id = ${params.workflowTableId} AND customer_id = ${params.customerId}
        RETURNING id "workflowTableId", customer_id "customerId", name, table_definition "tableDefinition"
      `.then(x => x[0])

      if (updatedTable) {
        const updatedRows = await sql<WorkflowTableRow[]>`
          UPDATE workflow_table_row
          SET row_value = row_value #- ARRAY['data', ${params.columnId}::TEXT]
          WHERE customer_id = ${params.customerId} AND workflow_table_id = ${params.workflowTableId}
          RETURNING ${this.WorkflowTableRowFields}
        `.then(x => Array.from(x))

        return {
          updatedTable,
          updatedRows,
        }
      } else {
        return null
      }
    })
  }

  listWorkflowTables(
    params: {
      customerId: string
      filter: 'active' | 'trash' | 'all'
      fuzzyName?: string
    },
    sql = this.sql
  ) {
    return sql<
      (Omit<WorkflowTable, 'tableDefinition'> & {
        isOnboardingTable: boolean
        userId: string
        updatedAt: Date
        deletedAt: Date | null
        owner: {
          userId: string
          firstName: string | null
          lastName: string | null
          profileImageUrl: string | null
        } | null
      })[]
    >`
      SELECT
        wt.id "workflowTableId",
        wt.customer_id "customerId",
        wt.name,
        wt.user_id "userId",
        wt.updated_at "updatedAt",
        wt.deleted_at "deletedAt",
        wt.table_definition->>'isOnboardingTable' "isOnboardingTable",
        CASE WHEN u.user_id IS NULL THEN NULL ELSE JSONB_BUILD_OBJECT(
          'userId', u.user_id,
          'firstName', u.first_name,
          'lastName', u.last_name,
          'profileImageUrl', u.profile_image_url
        ) END "owner"
      FROM workflow_table wt
      LEFT JOIN users u ON u.user_id = wt.user_id
      WHERE wt.customer_id = ${params.customerId}
      ${
        params.filter === 'trash'
          ? sql`AND wt.deleted_at IS NOT NULL`
          : params.filter === 'active'
            ? sql`AND wt.deleted_at IS NULL`
            : sql``
      }
      ${params.fuzzyName ? sql`AND name ILIKE ${`%${params.fuzzyName}%`}` : sql``}
      ORDER BY wt.updated_at DESC
    `.then(x => Array.from(x))
  }

  listPinnedWorkflowTableIds(params: { customerId: string; userId: string }, sql = this.sql) {
    return sql<{ workflowTableId: number }[]>`
      SELECT workflow_table_id "workflowTableId"
      FROM workflow_table_pin
      WHERE customer_id = ${params.customerId}
      AND user_id = ${params.userId}
      ORDER BY pinned_at DESC
    `.then(rows => rows.map(r => r.workflowTableId))
  }

  setPinnedTable(
    params: { customerId: string; userId: string; workflowTableId: number; pinned: boolean },
    sql = this.sql
  ) {
    if (params.pinned) {
      return sql`
        INSERT INTO workflow_table_pin (customer_id, user_id, workflow_table_id)
        VALUES (${params.customerId}, ${params.userId}, ${params.workflowTableId})
        ON CONFLICT (customer_id, user_id, workflow_table_id) DO UPDATE SET pinned_at = NOW()
      `
    } else {
      return sql`
        DELETE FROM workflow_table_pin
        WHERE customer_id = ${params.customerId}
        AND user_id = ${params.userId}
        AND workflow_table_id = ${params.workflowTableId}
      `
    }
  }

  getWorkflowTable(
    params: {
      customerId: string
      workflowTableId: number
    },
    sql = this.sql
  ) {
    return sql<WorkflowTable[]>`
      SELECT ${this.WorkflowTableFields}
      FROM workflow_table
      WHERE id = ${params.workflowTableId}
      AND customer_id = ${params.customerId}
    `.then(x => x[0])
  }

  addRows(
    params: {
      customerId: string
      workflowTableId: number
      rows: { rowId: string; rowData?: Record<string, unknown> }[]
    },
    sql?: TransactionSqlClient
  ) {
    const payload = params.rows.map(row => ({
      customer_id: params.customerId,
      workflow_table_id: params.workflowTableId,
      row_id: row.rowId,
      row_value: { data: row.rowData ?? {} },
    }))

    const chunks = chunksOf(payload, 2048)

    const run = async (sql: TransactionSqlClient) => {
      return Promise.all(
        chunks.map(
          chunk =>
            sql<
              Pick<WorkflowTableRow, 'rowId'>[]
            >`INSERT INTO workflow_table_row ${this.sql(chunk)} RETURNING row_id "rowId"`
        )
      ).then(x => x.flatMap(y => Array.from(y)))
    }

    return sql ? run(sql) : this.sql.begin(run)
  }

  deleteRows(params: { customerId: string; workflowTableId: number; rowIds: string[] }, sql = this.sql) {
    return sql<[WorkflowTableRow]>`
    DELETE FROM workflow_table_row
    WHERE customer_id = ${params.customerId}
    AND workflow_table_id = ${params.workflowTableId}
    AND row_id IN ${sql(params.rowIds)}
    `.then(x => x.count === params.rowIds.length)
  }

  setCellValue<T = unknown>(
    params: {
      customerId: string
      workflowTableId: number
      rowId: string
      columnId: ColumnId
      value: T
    },
    sql = this.sql
  ) {
    return sql<WorkflowTableRow[]>`
    UPDATE workflow_table_row
    SET row_value = JSONB_SET(row_value, ARRAY['data', ${params.columnId}::TEXT], ${params.value}::JSONB)
    WHERE customer_id = ${params.customerId}
    AND workflow_table_id = ${params.workflowTableId}
    AND row_id = ${params.rowId}
    RETURNING ${this.WorkflowTableRowFields}
    `.then(x => x[0])
  }

  updateRows(
    params: {
      customerId: string
      workflowTableId: number
      rows: ({ rowId: string } & Record<ColumnId, unknown>)[]
    },
    sql = this.sql
  ) {
    const { customerId, workflowTableId, rows } = params

    const payload = rows.map(row => {
      const { rowId, createdAt, updatedAt, ...rowValue } = row
      return [rowId, JSON.stringify(rowValue)] as const
    })

    return sql<WorkflowTableRow[]>`
        UPDATE workflow_table_row
        SET row_value = JSONB_SET(row_value, ARRAY['data'], row_value->'data' || (update_data.rowvalue)::JSONB)
        FROM (VALUES ${sql(payload)}) AS update_data (rowid, rowvalue)
        WHERE customer_id = ${customerId}
        AND workflow_table_id = ${workflowTableId}
        AND row_id = (update_data.rowid)::UUID
        RETURNING ${this.WorkflowTableRowFields}
      `.then(x => Array.from(x))
  }

  getRowsQuery_ = (params: { customerId: string; workflowTableId: number; rowId?: string }, sql = this.sql) => {
    return sql<WorkflowTableRow[]>`
    SELECT ${this.WorkflowTableRowFields}
    FROM workflow_table_row
    WHERE customer_id = ${params.customerId} AND workflow_table_id = ${params.workflowTableId}
    ${params.rowId ? sql`AND row_id = ${params.rowId}` : sql``}
    ORDER BY row_id ASC`
  }

  getRows = (params: { customerId: string; workflowTableId: number; rowId?: string }, sql = this.sql) =>
    this.getRowsQuery_(params, sql).then(x => Array.from(x))

  getRowsCursor = (params: { customerId: string; workflowTableId: number }, sql = this.sql) =>
    this.getRowsQuery_(params, sql).cursor(128)
}

export type SubscriptionEvent =
  | {
      operation: 'INITIAL_PART'
      rows: WorkflowTableRow[]
    }
  | { operation: 'INSERT' | 'UPDATE'; rowId: string; row: WorkflowTableRow | null }
  | { operation: 'DELETE'; rowId: string }

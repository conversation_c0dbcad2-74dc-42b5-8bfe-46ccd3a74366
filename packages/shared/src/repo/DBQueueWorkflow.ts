import type { SqlClient } from '@upbound/postgres'
import { Duration } from 'effect'
import { chunksOf } from 'effect/Array'
import type { DurationInput } from 'effect/Duration'

// Duplicated from QueueWorkflow.ts
type EvalRow = {
  topic: 'eval:row'
  payload: {
    rowId: string
    // If "columnId" is present, only run evaluation for the defined column in the row.
    // Any upstream dependencies will also be evaluated.
    columnId?: string
  }
}

// Duplicated from QueueWorkflow.ts
type EvalColumn = {
  topic: 'gen:row'
  payload: {
    columnId: string
    size?: number
  }
}

type UpdateRow = {
  topic: 'update:row'
  payload: {
    columnId: string
  }
}

// Duplicated from QueueWorkflow.ts
type QueueWorkflow = {
  id: number
  customerId: string
  workflowTableId: number
  status: 'pending' | 'running' | 'done' | 'error'
  payload: Record<string, unknown>
} & (EvalRow | EvalColumn | UpdateRow)

export class DBQueueWorkflow {
  constructor(private readonly sql: SqlClient) {}

  QueueWorkflowFields = this.sql`
    id "queueWorkflowId",
    customer_id "customerId",
    workflow_table_id "workflowTableId",
    status,
    topic,
    payload "payload"
  `

  async addToQueueWorkflow(
    params: ({
      customerId: string
      workflowTableId: number
      deduplicationId: string
      delay?: DurationInput
    } & (EvalRow | EvalColumn | UpdateRow))[],
    sql = this.sql
  ) {
    if (params.length === 0) {
      return true
    }

    const transformed = params.map(p => ({
      customer_id: p.customerId,
      workflow_table_id: p.workflowTableId,
      topic: p.topic,
      payload: p.payload,
      delay: p.delay ? sql`CAST(${`${Duration.toMillis(p.delay)} ms`} AS INTERVAL)` : null,
      deduplication_id: p.deduplicationId,
    }))

    const chunks = chunksOf(transformed, 1024)

    for (const chunk of chunks) {
      // Uses the unique index 'idx_workflow_queue_unique_deduplication'
      await sql`
        INSERT INTO q_workflow ${sql(chunk)}
        ON CONFLICT DO NOTHING
      `
    }
  }

  async deletePendingFromQueueWorkflow(params: { customerId: string; workflowTableId: number }, sql = this.sql) {
    return sql<QueueWorkflow[]>`
      DELETE FROM q_workflow
      WHERE customer_id = ${params.customerId}
      AND workflow_table_id = ${params.workflowTableId}
      AND status = 'pending'
      RETURNING ${this.QueueWorkflowFields}`.then(x => Array.from(x))
  }

  async getCurrentPendingRunningCounts(params: { customerId: string; workflowTableId: number }, sql = this.sql) {
    const [pending, running] = await Promise.all([
      sql<
        [{ count: number }]
      >`SELECT COUNT(*)::INT FROM q_workflow WHERE customer_id = ${params.customerId} AND workflow_table_id = ${params.workflowTableId} AND status = 'pending'`,
      sql<
        [{ count: number }]
      >`SELECT COUNT(*)::INT FROM q_workflow WHERE customer_id = ${params.customerId} AND workflow_table_id = ${params.workflowTableId} AND status = 'running'`,
    ])

    return {
      pending: pending[0].count,
      running: running[0].count,
    }
  }
}

export type QueueWorkflowSubscriptionEvent = {
  pending: number
  running: number
}

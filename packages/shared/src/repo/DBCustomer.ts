import type { SqlClient } from '@upbound/postgres'

export class DBCustomer {
  constructor(private readonly sql: SqlClient) {}

  async getCustomer(customerId: string) {
    return this.sql<[{ clerkId: string; clerkOrgSlug: string | null; creditsAvailable: number } | undefined]>`SELECT
        clerk_id "clerkId",
        clerk_org_slug "clerkOrgSlug",
        credits_available "creditsAvailable"
      FROM customer
      WHERE clerk_id = ${customerId}`.then(([row]) => row ?? undefined)
  }

  async getCustomerSlug(customerId: string) {
    return this.sql<
      [{ clerkOrgSlug?: string }]
    >`SELECT clerk_org_slug "clerkOrgSlug" FROM customer WHERE clerk_id = ${customerId}`.then(x => x[0]?.clerkOrgSlug)
  }

  /**
   * Upserts into the customer table.
   * We don't use `on conflict do nothing` because we don't want to create
   * dead tuples with every upsert attempt.
   */
  async getOrUpsert(customerId: string, orgSlug?: string) {
    const org = await this.getCustomer(customerId)

    if (!org) {
      const payload = {
        clerk_id: customerId,
        clerk_org_slug: orgSlug,
        credits_available: 500,
      }

      void (await this.sql`
      INSERT INTO "customer" ${this.sql(payload)}
      ON CONFLICT ("clerk_id") DO NOTHING;
    `)

      return { clerkId: customerId, name: orgSlug, creditsAvailable: 500 }
    }

    // If the name we have them as is different from their configured
    // Clerk name, update our local reference of the name
    if (orgSlug && org.clerkOrgSlug?.trim() !== orgSlug.trim()) {
      void (await this.sql`
        UPDATE "customer"
          SET clerk_org_slug = ${orgSlug.trim()}
        WHERE clerk_id = ${customerId}
      `)
    }

    return org
  }

  createCustomer(args: { orgId: string; orgSlug: string; companyName: string; companyDomain: string }) {
    const payload = {
      clerk_id: args.orgId,
      clerk_org_slug: args.orgSlug,
      company_name: args.companyName,
      company_domain: args.companyDomain,
      credits_available: 500,
    }

    return this.sql`
      INSERT INTO customer ${this.sql(payload)}
      ON CONFLICT ("clerk_id") DO NOTHING
    `.then(x => x.count === 1)
  }

  getAvailableCredits = (customerId: string, sql = this.sql) => {
    return sql<
      [{ creditsAvailable: number }]
    >`SELECT credits_available "creditsAvailable" FROM customer WHERE clerk_id = ${customerId}`.then(
      x => x[0].creditsAvailable
    )
  }

  private updateAvailableCredits = (customerId: string, credits: number, sql = this.sql) => {
    return sql<[{ creditsAvailable: number }]>`
      UPDATE "customer"
      SET credits_available = credits_available + ${credits}
      WHERE clerk_id = ${customerId}
      RETURNING credits_available "creditsAvailable"
    `.then(x => x[0].creditsAvailable)
  }

  addCredits = (customerId: string, credits: number, sql = this.sql) =>
    this.updateAvailableCredits(customerId, credits, sql)

  /**
   * INTERNAL ONLY.
   * Use `addCredits` or `consumeCredit` for transactional operations.
   */
  setCredits = (customerId: string, credits: number, sql = this.sql) =>
    this.getAvailableCredits(customerId, sql).then(currentCredits =>
      this.updateAvailableCredits(customerId, credits - currentCredits, sql)
    )

  consumeCredit = (customerId: string, sql = this.sql) => this.updateAvailableCredits(customerId, -1, sql)

  consumeMultipleCredits = (customerId: string, credits: number, sql = this.sql) =>
    this.updateAvailableCredits(customerId, credits * -1, sql)
}

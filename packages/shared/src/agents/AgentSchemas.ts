import { z } from 'zod'

const FormatTemplateSchema = z
  .string()
  .describe(
    'A format template with an example of the value that should be inserted into this column. Eg:"[URL] (e.g. https://www.linkedin.com/company/example)" or "[City], [State] (e.g. San Francisco, CA)"'
  )

const SingleLineTextColumnDefinitionSchema = z.object({
  type: z.literal('singleLineText'),
  formatTemplate: FormatTemplateSchema,
})

const MultiLineTextColumnDefinitionSchema = z.object({
  type: z.literal('multiLineText'),
  formatTemplate: FormatTemplateSchema,
})

const EmailColumnDefinitionSchema = z.object({
  type: z.literal('email'),
})

const UrlColumnDefinitionSchema = z.object({
  type: z.literal('url'),
})

const CurrencyColumnDefinitionSchema = z.object({
  type: z.literal('currency'),
})

const PhoneNumberColumnDefinitionSchema = z.object({
  type: z.literal('phoneNumber'),
})

const SelectColumnDefinitionSchema = z.object({
  type: z.literal('select'),
  options: z.array(z.string()).describe('The list of options the value can be'),
})

const MultiSelectColumnDefinitionSchema = z.object({
  type: z.literal('multiSelect'),
  options: z.array(z.string()).describe('The list of options the user can select from'),
})

const BooleanColumnDefinitionSchema = z.object({
  type: z.literal('boolean'),
})

const NumberColumnDefinitionSchema = z.object({
  type: z.literal('number'),
})

const UnconfiguredAgentColumnDefinitionSchema = z.object({
  type: z.literal('unconfigured'),
})

const AgentDataTypeSchemas = [
  SingleLineTextColumnDefinitionSchema,
  MultiLineTextColumnDefinitionSchema,
  EmailColumnDefinitionSchema,
  UrlColumnDefinitionSchema,
  CurrencyColumnDefinitionSchema,
  PhoneNumberColumnDefinitionSchema,
  SelectColumnDefinitionSchema,
  MultiSelectColumnDefinitionSchema,
  NumberColumnDefinitionSchema,
  BooleanColumnDefinitionSchema,
] as const

export const AgentDataType = z.discriminatedUnion('type', AgentDataTypeSchemas)

export const AgentDataTypeWithUnconfigured = z.discriminatedUnion('type', [
  ...AgentDataTypeSchemas,
  UnconfiguredAgentColumnDefinitionSchema,
])

export const AgentDataTypeFor = (type: AgentDataType['type']) => {
  switch (type) {
    case 'select':
      return SelectColumnDefinitionSchema
    case 'multiSelect':
      return MultiSelectColumnDefinitionSchema
    case 'boolean':
      return BooleanColumnDefinitionSchema
    case 'number':
      return NumberColumnDefinitionSchema
    case 'phoneNumber':
      return PhoneNumberColumnDefinitionSchema
    case 'singleLineText':
      return SingleLineTextColumnDefinitionSchema
    case 'multiLineText':
      return MultiLineTextColumnDefinitionSchema
    case 'email':
      return EmailColumnDefinitionSchema
    case 'url':
      return UrlColumnDefinitionSchema
    case 'currency':
      return CurrencyColumnDefinitionSchema
  }
}

export type AgentDataType = z.output<typeof AgentDataType>
export type AgentColumnTypeKey = AgentDataType['type']
export type AgentDataTypeWithUnconfigured = z.output<typeof AgentDataTypeWithUnconfigured>

const AgentSelectOutputSchema = (options: string[]) => {
  return z
    .string()
    .describe(
      `One value from the following options that represents the result : ${options.map(x => '`' + x + '`').join(', ')}`
    )
}

const AgentMultiSelectOutputSchema = (options: string[]) => {
  return z
    .string()
    .array()
    .describe(
      `One or more values from the following options that represents the result : ${options.map(x => '`' + x + '`').join(', ')}`
    )
}

export const AgentCellOutputSchema = (dataType: AgentDataType) => {
  switch (dataType.type) {
    case 'select':
      return AgentSelectOutputSchema(dataType.options)
    case 'multiSelect':
      return AgentMultiSelectOutputSchema(dataType.options)
    case 'boolean':
      return z.boolean().describe('A single boolean value that represents the result')
    case 'number':
      return z.number().describe('A single number that represents the result')
    case 'phoneNumber':
      return z.string().describe('A phone number that represents the result')
    case 'singleLineText':
      return z.string().describe(`A single string value that represents the result`)
    case 'multiLineText':
      return z.string().describe(`A sentence of text that represents the result`)
    case 'email':
      return z.string().describe('An email address that represents the result')
    case 'url':
      return z.string().describe('A URL that represents the result')
    case 'currency':
      return z.string().describe('A currency value with a symbol that represents the result')
  }
}

import { createOpenAI } from '@ai-sdk/openai'
import { type RedisClient, RedisPersistentCache } from '@upbound/redis'
import { ONE_MIN } from '@upbound/utils'
import { hashString } from '@upbound/utils/Hash'
import { filterEmptyStringValuesFromObject } from '@upbound/utils/Record'
import { generateObject } from 'ai'
import { z } from 'zod'
import { AgentDataType, AgentDataTypeFor } from '../agents/AgentSchemas.js'

export const ResultSchema = z.object({
  instructions: z
    .string()
    .describe(
      'A detailed definition of the task that should be performed to populate this column. In most cases, this should start with the word "Find" and must be very specific and include any necessary context or constraints provided by the user.'
    ),
  columnType: AgentDataType.describe(
    `Suggest the most appropriate column type. Prefer 'singleLineText' over 'multiLineText' when possible.`
  ),
  columnName: z.string().describe('The name of the column in "Sentence case".'),
})

export class AgentLLM {
  #cache = RedisPersistentCache.apply('agent.llm', this.redisClient)
  #openai = createOpenAI({ apiKey: this.credentials.openAIApiKey })

  constructor(
    private readonly credentials: {
      openAIApiKey: string
    },
    private readonly redisClient: RedisClient
  ) {}

  computeAgentColumnDefinition_ = async ({
    task,
    existingColumns,
    desiredOutputDataType,
  }: {
    customerId: string
    task: string
    existingColumns: { columnName: string; dataType: AgentDataType }[]
    desiredOutputDataType?: AgentDataType['type']
  }): Promise<z.output<typeof ResultSchema>> => {
    const systemRole = `You are a seasoned GTM analyst at a Fortune 500 company.`

    const systemPrompt = `
You are working in the context of a table. You will be given a list of existing columns and their data types along with a user-supplied "intent" for a new column.

1. Analyze the "intent" using the following steps to clearly identify "instructions" that should be performed to populate this new column:
    - The task must include any constraints or specifics provided by the user.
    - Do not mention any existing columns in the task intent unless the user explicitly mentions an existing column.
    - If the user mentions an existing column name, then make sure to include that column name in the task intent within double quotes.
    - Use the appropriate verb at the start of the task to indicate the action to be performed. For example :
      a. If the intent is to find a value, then the task should start with the word "Find".
      b. If the intent is to calculate a value, then the task should start with the word "Calculate".
      c. If the intent is to summarize a value, then the task should start with the word "Summarize".
      d. If the intent is to compare values, then the task should start with the word "Compare".
      e. If the intent is to find a relationship, then the task should start with the word "Find".
2. Come up with a "columnType" that is a good fit for the values that will be populated in this new column.
3. Come up with a "columnName" that best represents the values that will be populated in this new column.
`.trim()

    const userPrompt = `
<intent>
${task}
</intent>

<existing-columns>
${existingColumns.map(({ columnName, dataType }) => `ColumnName: ${columnName}\nColumnDefinition: ${JSON.stringify(filterEmptyStringValuesFromObject(dataType))}`).join('\n\n')}
</existing-columns>
  `.trim()

    const outputSchema = desiredOutputDataType
      ? z.object({
          instructions: z
            .string()
            .describe(
              'A detailed definition of the task that should be performed to populate this column. In most cases, this should start with the word "Find" and must be very specific and include any necessary context or constraints.'
            ),
          columnType: AgentDataTypeFor(desiredOutputDataType).describe(`Suggest the most appropriate column type`),
          columnName: z.string().describe('The name of the column in "Sentence case".'),
        })
      : ResultSchema

    const result = await generateObject({
      model: this.#openai('gpt-5-nano'),
      messages: [
        {
          role: 'system',
          content: systemRole,
        },
        {
          role: 'user',
          content: systemPrompt,
          providerOptions: {
            anthropic: {
              cacheControl: { type: 'ephemeral' },
            },
          },
        },
        {
          role: 'user',
          content: userPrompt,
        },
      ],
      schema: outputSchema,
    })

    const output = result.object

    if (output.columnType.type === 'singleLineText' || output.columnType.type === 'multiLineText') {
      output.columnType.formatTemplate = output.columnType.formatTemplate ?? void 0
    }

    return output
  }

  computeAgentColumnDefinition = this.#cache.memoizeUnsafe(
    this.computeAgentColumnDefinition_,
    x =>
      hashString(
        `colType:${x.task}-${JSON.stringify(x.existingColumns.sort((a, b) => a.columnName.localeCompare(b.columnName)))}-${x.desiredOutputDataType ?? 'none'}`
      ),
    () => ({ ttlMs: ONE_MIN * 5 })
  )
}

import { isNonEmpty, isNotNullOrUndefined } from '@upbound/utils'
import type { CellValueOf, ColumnDefinition, ColumnId, RowData, TemplateDefinition } from './WorkflowTableTypes.js'
import { getValueOfCell } from './WorkflowUtils.js'

const isRecordOrArray = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object' && obj !== null

export const compileTemplateDefToStruct = (
  templateMapping: TemplateDefinition['templateMapping'],
  columnMap: Record<ColumnId, ColumnDefinition>,
  rowData: RowData
) => {
  const transformed = Object.entries(templateMapping).map(([mappingId, { columnId, subPath }]) => {
    const columnDef = columnMap[columnId]
    let cellData = columnDef ? getValueOfCell({ columnDef, row: rowData, columnMap }) : null

    // HACKHACK
    // When a subpath is defined, then the implication is that we are looking for a nested value from the data-structure.
    // So we leave cellData as is.
    // However, when its not defined and cellValue is a data-structure, we most likely just want the user-specific value of the agent cell.
    if ((!subPath || !isNonEmpty(subPath)) && columnDef?.type === 'agent' && isNotNullOrUndefined(cellData)) {
      const cData = cellData as CellValueOf<'agent'>
      cellData = cData.status === 'success' || cData.status === 'user' ? cData.value : null
    }

    if (subPath && isNonEmpty(subPath)) {
      return [mappingId, subPath.reduce((acc, key) => (isRecordOrArray(acc) ? acc[key] : void 0), cellData)] as const
    } else {
      return [mappingId, cellData] as const
    }
  })

  return Object.fromEntries(transformed)
}

type TemplatedPart =
  | { type: 'text'; value: string }
  | { type: 'templateField'; mappingId: string }
  | { type: 'newline' }

export const handlebarsToTemplateParts = (template: string): TemplatedPart[] => {
  return template.split('\n').flatMap(line => {
    const content: TemplatedPart[] = []
    let mutableLine = line
    const handlebarsSimpleRegex = /{{([a-z]|[0-9]|-)+}}/i
    let matched: RegExpExecArray | null

    // biome-ignore lint/suspicious/noAssignInExpressions: no reason given
    while ((matched = handlebarsSimpleRegex.exec(mutableLine)) !== null) {
      const [match] = matched
      const index = matched.index
      const preMatch = mutableLine.slice(0, index)
      const postMatch = mutableLine.slice(index + match.length)

      if (preMatch.length > 0) {
        content.push({ type: 'text', value: preMatch })
      }

      const mappingId = match.slice(2, -2)
      content.push({ type: 'templateField', mappingId })
      mutableLine = postMatch
    }

    if (mutableLine.length > 0) {
      content.push({ type: 'text', value: mutableLine })
    }

    content.push({ type: 'newline' })

    return content
  })
}

export const EmptyTemplateDefinition: TemplateDefinition = {
  template: '',
  templateMapping: {},
}

import type { NonEmptyArray } from '@upbound/utils'
import { DepGraph } from 'dependency-graph'
import type { ColumnDefinition } from './WorkflowTableTypes.js'

function isNonEmpty<T>(items?: T[]): items is NonEmptyArray<T> {
  return items ? items.length > 0 : false
}

export type GraphError = { columnId: string; code: 'DependencyCycle' | 'NodeDoesNotExist'; detail: string[] }
export type DepGraphResult = { graph: DepGraph<string> } & (
  | { graphErrors: NonEmptyArray<GraphError>; isValid: false }
  | { isValid: true }
)

export const detectDependencyCycle = (
  columnId: string,
  depGraph: DepGraph<string>
): { isValid: true } | { isValid: false; nodes: string[] } => {
  try {
    void depGraph.dependenciesOf(columnId)
    return { isValid: true }
  } catch (e: unknown) {
    if (e instanceof Error) {
      if (e.message.startsWith('Dependency Cycle Found')) {
        const cycle = e.message.replace('Dependency Cycle Found: ', '').split(' -> ')
        return { isValid: false, nodes: cycle }
      }
    }
    return { isValid: false, nodes: [] }
  }
}

export const buildDepGraph = (columnDef: ColumnDefinition[]): DepGraphResult => {
  const graph = new DepGraph<string>()
  const graphErrors: { columnId: string; code: 'DependencyCycle' | 'NodeDoesNotExist'; detail: string[] }[] = []

  for (const col of columnDef) {
    graph.addNode(col.columnId)
  }

  for (const col of columnDef) {
    if (col.columnVariant === 'computed') {
      for (const inpCol of col.inputColumns.concat(col.inputColumnsOptional ?? []).map(x => x.columnId)) {
        try {
          graph.addDependency(col.columnId, inpCol)
        } catch (e: unknown) {
          if (e instanceof Error) {
            if (e.message.startsWith('Dependency Cycle Found')) {
              const cycle = e.message.replace('Dependency Cycle Found: ', '').split(' -> ')
              graphErrors.push({ columnId: col.columnId, code: 'DependencyCycle', detail: cycle })
              continue
            }

            if (e.message.startsWith('Node does not exist')) {
              const node = e.message.replace('Node does not exist: ', '')
              graphErrors.push({ columnId: col.columnId, code: 'NodeDoesNotExist', detail: [node] })
              continue
            }
          }

          throw e
        }
      }
    }
  }

  const isNonEmptyGraphErrors = isNonEmpty(graphErrors)

  if (isNonEmptyGraphErrors) {
    return {
      graph,
      graphErrors,
      isValid: false,
    }
  } else {
    return {
      graph,
      isValid: true,
    }
  }
}

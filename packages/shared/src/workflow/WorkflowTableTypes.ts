import type { OmitOverUnion, Pretty } from '@upbound/utils'

/**
 * Enums and Base Types
 */
type RowDataDefault = {
  rowId: string
  createdAt: Date
  updatedAt: Date
}

type EmptyRecord = Record<never, never>

type ComputedCellResponseType<
  SuccessExtended = EmptyRecord,
  ErrorExtended = EmptyRecord,
  RunningExtended = EmptyRecord,
  QueuedExtended = EmptyRecord,
> = Pretty<
  | ({ status: 'queued' } & QueuedExtended)
  | ({ status: 'running' } & RunningExtended)
  | ({ status: 'success' } & SuccessExtended)
  | ({ status: 'error'; message?: string } & ErrorExtended)
  | { status: 'runConditionNotMet' }
>

export type RowId = string
export type ColumnId = string
export type InternalTemplateId = string
export type ColumnMap = Record<ColumnId, ColumnDefinition>
export type RowData = RowDataDefault & Record<ColumnId, unknown>

export type Digests = {
  // The digest value derived from the configuration of the column.
  columnDigest: string

  // A digest value derived from the values of the input column(s) used to generate the value of this computed column.
  // If an input column does not have a value, the final digest should be null.
  inputDigest: string

  // The input digest calculated using the current values of the input columns.
  // This value should not be stored and should only be computed when needed.
  uiCurrentInputDigest?: string
}

export type TemplateColumn = {
  columnId: ColumnId
  columnName?: string // for placeholder cells
  subPath?: string[]
}

export type TemplateMapping = Record<InternalTemplateId, TemplateColumn>

export interface TemplateDefinition {
  /**
   * Handlebars template string using internal ids as placeholders
   */
  template: string
  /**
   * InternalIds to columnId + subpath mapping
   */
  templateMapping: TemplateMapping
}

/**
 * Column Definitions
 */

// Static columns are simple column types.
// string, number, boolean, etc...
type ColumnVariant = 'static' | 'computed' | 'source'

// Additional configs for computed columns
type ComputedColumnConfigs = {
  columnDigest: string
  inputColumns: { columnId: string; columnName: string }[]
  /**
   * Optional input columns that are not required to compute the column.
   * But are nevertheless used in calculating the digest if a value exists
   * but should not prevent execution of the cell if not present.
   */
  inputColumnsOptional?: { columnId: string; columnName: string }[]
  /**
   * If set, the column will only be re-run if the formula evaluates to true.
   */
  runIf?: { formula: TemplateDefinition; description?: TemplateDefinition }
  /**
   * If set, the column will be scheduled to re-run automatically when the row is created, updated or upserted.
   */
  autoRun?: 'onCreate' | 'onUpdate' | 'onUpsert' | null
}

type StaticColumnConfigs = {
  // uiVariant determines the type of static column and the UX
  // for the user.
  // - static: The column value is set manually by the user.
  // - derived: The column value is derived from other columns in the table.
  // - formula: The column value is generated from a formula.
  // In all cases, the value can be edited by the user.
  uiVariant?: 'static' | 'derived' | 'formula'
  defaultFormula?: {
    formula: TemplateDefinition
    description?: TemplateDefinition
  }
}

type ColumnDefinitionBase<T, CVariant extends ColumnVariant, Config = Record<string, unknown>> = {
  type: T

  // Column ID (UUIDv7)
  columnId: ColumnId

  // A human-readable name for the column
  columnName: string
  columnVariant: CVariant
  computed?: CVariant extends 'computed' ? true : false
  config: Config
}

type CellValueBase<T, ValueType = unknown> = {
  type: T
  value: ValueType
}

type ColumnDefinitionWithCellValue<TypeTag, CVariant extends ColumnVariant, Config, ValueType> = {
  type: TypeTag
  // Column Definition
  // Includes column type and any additional configuration
  column: ColumnDefinitionBase<TypeTag, CVariant, Config> &
    (CVariant extends 'computed' ? ComputedColumnConfigs : StaticColumnConfigs)
  // Row Definition
  // Includes the value of the column for a specific row
  cell: CVariant extends 'computed' ? CellValueBase<TypeTag, ValueType & Digests> : CellValueBase<TypeTag, ValueType>
}

/**
 * STATIC AND COMPUTED COLUMN TYPE ⬆
 */

type StringColumnDefinition = Pretty<
  ColumnDefinitionWithCellValue<
    'string',
    'static',
    {
      subType?: 'text' | 'multilineText' | 'email' | 'url'
    },
    string
  >
>

/**
 *
 * AGENT COLUMN TYPE ⬇
 */
type AgentTypeContainer<TypeTag, DataTypeConfig, ValueType> = {
  type: TypeTag
  dataType: {
    type: TypeTag
  } & DataTypeConfig
  value: ValueType
}

type SelectColumnDefinition = AgentTypeContainer<
  'select',
  {
    options: string[]
  },
  string
>

type MultiSelectColumnDefinition = AgentTypeContainer<
  'multiSelect',
  {
    options: string[]
  },
  string[]
>

export type TextColumnDefinition = AgentTypeContainer<
  'singleLineText' | 'multiLineText',
  { formatTemplate: string },
  string
>
type EmailColumnDefinition = AgentTypeContainer<'email', EmptyRecord, string>
type UrlColumnDefinition = AgentTypeContainer<'url', EmptyRecord, string>
type CurrencyColumnDefinition = AgentTypeContainer<'currency', EmptyRecord, string>
type PhoneNumberColumnDefinition = AgentTypeContainer<'phoneNumber', EmptyRecord, string>
type NumberColumnDefinition = AgentTypeContainer<'number', EmptyRecord, number>
type BooleanColumnDefinition = AgentTypeContainer<'boolean', EmptyRecord, boolean>
type UnconfiguredAgentColumnDefinition = AgentTypeContainer<'unconfigured', EmptyRecord, string>

type AgentDataTypes =
  | TextColumnDefinition
  | EmailColumnDefinition
  | UrlColumnDefinition
  | CurrencyColumnDefinition
  | PhoneNumberColumnDefinition
  | SelectColumnDefinition
  | MultiSelectColumnDefinition
  | NumberColumnDefinition
  | BooleanColumnDefinition
  | UnconfiguredAgentColumnDefinition

type AgentColumnDefinition = {
  type: 'agent'
  column: {
    type: 'agent'
    columnVariant: 'computed'
    columnId: ColumnId
    columnName: string
    config: {
      task: string
      agentTask: string
      dataType: AgentDataTypes['dataType']
    }
  } & ComputedColumnConfigs
  cell: {
    type: 'agent'
    value: (
      | ComputedCellResponseType<{
          value: AgentDataTypes['value']
          reasoning: string
          steps?: string[]
          sources?: string[]
          // Additional metadata about the result that might come from providers
          // or is relevant to show on the UI.
          meta?: Record<string, unknown>
        }>
      // If the user has manually set the value of the column, we store that value here.
      | { status: 'user'; value: AgentDataTypes['value'] }
    ) &
      Digests
  }
}

export type TableDefinition =
  | StringColumnDefinition
  // Agent columns
  | AgentColumnDefinition

export type ColumnDefinition = TableDefinition['column']
export type ColumnDefinitionComputed = Extract<ColumnDefinition, { columnVariant: 'computed' }>
export type ColumnDefinitionStatic = Extract<ColumnDefinition, { columnVariant: 'static' }>
export type ColumnDefinitionSource = Extract<ColumnDefinition, { columnVariant: 'source' }>

export type ColumnType = TableDefinition['type']

export type ColumnDefinitionOf<T extends ColumnType> = Extract<ColumnDefinition, { type: T }>
export type ColumnDefinitionWithoutColumnId = OmitOverUnion<ColumnDefinition, 'columnId' | 'columnVariant'>
export type ColumnDefinitionWithOptionalColumnId = OmitOverUnion<ColumnDefinition, 'columnId' | 'columnVariant'> & {
  columnId?: ColumnId
}

export type CellDefinition = TableDefinition['cell']
export type CellValueOf<T extends ColumnType> = Extract<CellDefinition, { type: T }>['value']
export type CellValueComputed = CellValueOf<ColumnDefinitionComputed['type']>

export type ColumnDefinitionForUI =
  | Pick<ColumnDefinitionStatic, 'type' | 'uiVariant' | 'columnName'>
  | Pick<ColumnDefinitionComputed, 'type' | 'columnName'>
  | Pick<ColumnDefinitionSource, 'type' | 'columnName'>

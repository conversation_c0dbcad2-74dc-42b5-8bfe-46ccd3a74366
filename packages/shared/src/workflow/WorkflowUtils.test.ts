import { describe, expect, test } from 'vitest'
import type { ColumnMap, RowData, TemplateDefinition } from './WorkflowTableTypes.js'
import { evalCellFormula } from './WorkflowUtils.js'

describe('evalCellFormula', () => {
  const mockColumnMap: ColumnMap = {
    col1: {
      columnId: 'col1',
      columnName: 'Name',
      type: 'string',
      columnVariant: 'static',
      config: {},
    },
  }

  const mockRowData: RowData = {
    col1: 'John Doe',
    col2: { Name: 'Acme Corp', Industry: 'Tech' },
    rowId: 'row1',
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  test('Evaluate simple template with single field without QuickJS', () => {
    const templateDef: TemplateDefinition = {
      template: '{{0}}',
      templateMapping: {
        '0': {
          columnId: 'col1',
          subPath: undefined,
        },
      },
    }

    const result = evalCellFormula(templateDef, mockColumnMap, mockRowData)
    expect(result).toBe('<PERSON>')
  })
})

import { isNotNullOrUndefined, type OmitOverUnion } from '@upbound/utils'
import { xxhash64 } from 'hash-wasm'
import type { AgentDataType } from '../agents/AgentSchemas.js'
import type { WorkflowTableRow } from '../repo/DBWorkflow.js'
import { compileTemplateDefToStruct } from './TemplateUtils.js'
import type {
  CellValueOf,
  ColumnDefinition,
  ColumnDefinitionComputed,
  ColumnDefinitionStatic,
  ColumnId,
  ColumnMap,
  ColumnType,
  RowData,
  TemplateDefinition,
} from './WorkflowTableTypes.js'

export const transformWorkflowTableRowToRowData = (d: WorkflowTableRow): RowData => {
  return {
    ...d.rowValue.data,
    rowId: d.rowId,
    createdAt: d.createdAt,
    updatedAt: d.updatedAt,
  }
}

/**
 * Calculate a digest for any given column definition
 * based on any configurable properties present on the column.
 */
export const computeDigestForColumnDefinition = <T extends OmitOverUnion<ColumnDefinitionComputed, 'columnDigest'>>(
  columnDef: T
): Promise<string> => {
  let parts: (string | null | undefined)[] = []

  const inputColumns = columnDef.inputColumns.map(col => col.columnId)
  const inputColumnsOptional = columnDef.inputColumnsOptional?.map(col => col.columnId) ?? []

  switch (columnDef.type) {
    case 'agent': {
      const config = columnDef.config
      parts = [
        JSON.stringify(config.task),
        JSON.stringify(config.dataType),
        JSON.stringify(columnDef.runIf?.formula ?? ''),
      ]
      break
    }
  }

  return xxhash64(
    parts
      .concat(inputColumns.filter(isNotNullOrUndefined).filter(x => x.length > 0))
      .concat(inputColumnsOptional.filter(isNotNullOrUndefined).filter(x => x.length > 0))
      .join('|')
      .normalize()
  )
}

export const addDigestToColumnDefinition = <T extends OmitOverUnion<ColumnDefinitionComputed, 'columnDigest'>>(
  columnDef: T
): Promise<T & { columnDigest: string }> => {
  return computeDigestForColumnDefinition(columnDef).then(digest => ({
    ...columnDef,
    columnDigest: digest,
  }))
}

export const computeDigestForCell = (params: {
  columnId: ColumnId
  columnMap: ColumnMap
  row: RowData
}): Promise<string | null> => {
  const columnDef = params.columnMap[params.columnId]

  if (columnDef && isComputedColumn(columnDef)) {
    // These contribute to the digest and are required to compute the cell.
    const cellValues = columnDef.inputColumns.map(col => {
      const columnDef = params.columnMap[col.columnId]

      if (!columnDef) {
        return null
      }

      const cellValue = getValueOfCell_({ columnDef, row: params.row, columnMap: params.columnMap })
      return cellValue && cellValue !== '' ? cellValue : null
    })

    // These contribute to the digest but are not required to compute the cell.
    const cellValuesOptional =
      columnDef.inputColumnsOptional
        ?.map(col => {
          const columnDef = params.columnMap[col.columnId]

          if (!columnDef) {
            return null
          }

          return getValueOfCell({ columnDef, row: params.row, columnMap: params.columnMap })
        })
        .filter(isNotNullOrUndefined) ?? []

    if (cellValues.some(v => v === null)) {
      return Promise.resolve(null)
    }

    return xxhash64(cellValues.concat(cellValuesOptional).join('|').normalize())
  } else {
    return Promise.resolve(null)
  }
}

/**
 * Fast path evaluation for templates that extract a single value
 */
const evalSimpleTemplate = (templateDef: TemplateDefinition, columnMap: ColumnMap, row: RowData): unknown => {
  const mapping = templateDef.templateMapping['0']
  if (!mapping) return null

  const columnDef = columnMap[mapping.columnId]
  if (!columnDef) return null

  return compileTemplateDefToStruct(templateDef.templateMapping, columnMap, row)[0]
}

export const evalCellFormula = (templateDef: TemplateDefinition, columnMap: ColumnMap, row: RowData): unknown => {
  // Fast path for simple templates
  if (templateDef.template === '{{0}}') {
    return evalSimpleTemplate(templateDef, columnMap, row)
  }

  return 'formula result'
}

export const isComputedColumn = (column: ColumnDefinition): column is ColumnDefinitionComputed =>
  column.columnVariant === 'computed'

export const isStaticColumn = (column: ColumnDefinition): column is ColumnDefinitionStatic =>
  column.columnVariant === 'static'

export const getCellValueForColumn = <T extends ColumnType>(
  columnId: ColumnId,
  row: Record<string, unknown>
): CellValueOf<T> | undefined | null => {
  return row[columnId] as CellValueOf<T> | undefined | null
}

export const getValueOfCell = ({
  columnDef,
  row,
  columnMap,
}: {
  columnDef: ColumnDefinition
  row: RowData
  columnMap: ColumnMap
}) => {
  switch (columnDef.type) {
    case 'string': {
      const cellValue = getCellValueForColumn<typeof columnDef.type>(columnDef.columnId, row)
      const v = cellValue && cellValue.length > 0 ? cellValue : null

      if (!v && columnDef.defaultFormula) {
        const v = evalCellFormula(columnDef.defaultFormula.formula, columnMap, row)
        return isNotNullOrUndefined(v) ? v : null
      }

      return v
    }
    case 'agent': {
      const cellValue = getCellValueForColumn<typeof columnDef.type>(columnDef.columnId, row)
      return cellValue?.status === 'user' || cellValue?.status === 'success' ? (cellValue ?? null) : null
    }
    default: {
      console.error(`Unknown column type: ${JSON.stringify(columnDef, null, 2)}`)
      return null
    }
  }
}

/**
 * Convert a column map to a JSON schema.
 * Useful for inserting context into LLMs.
 */
export const columnMapToJsonSchema = (_title: string, columnMap: ColumnMap): unknown => {
  type JsonSchemaProperty = {
    type: 'array' | 'string' | 'number' | 'boolean'
    enum?: string[]
    items?: JsonSchemaProperty
    uniqueItems?: boolean
    format?: string
  }

  const properties: Record<string, JsonSchemaProperty> = {}

  for (const value of Object.values(columnMap)) {
    // If the value type is agent, we create a properly structured json schema for it.
    if (value.type === 'agent') {
      const colType = value.config.dataType.type

      if (colType === 'select') {
        properties[value.columnName] = {
          type: 'string',
          enum: value.config.dataType.options,
        }
      } else if (colType === 'multiSelect') {
        properties[value.columnName] = {
          type: 'array',
          items: {
            type: 'string',
            enum: value.config.dataType.options,
          },
          uniqueItems: true,
        }
      } else if (colType === 'boolean' || colType === 'number') {
        properties[value.columnName] = {
          type: colType,
        }
      } else if (colType === 'url') {
        properties[value.columnName] = {
          type: 'string',
          format: 'uri',
        }
      } else if (colType === 'email') {
        properties[value.columnName] = {
          type: 'string',
          format: 'email',
        }
      } else {
        properties[value.columnName] = {
          type: 'string',
        }
      }
    } else {
      // Otherwise, we fall back to just treating it as a string.
      properties[value.columnName] = {
        type: 'string',
      }
    }
  }

  return {
    // $schema: 'http://json-schema.org/draft-07/schema#',
    // title,
    type: 'object',
    properties,
    required: [],
  }
}

export const excludeColumnsFromColumnMap = (columnMap: ColumnMap, columnIds: ColumnId[]): ColumnMap => {
  return Object.fromEntries(Object.entries(columnMap).filter(([columnId]) => !columnIds.includes(columnId)))
}

export const columnDefinitionsToExistingColumnValuesForAgents = (
  columnDefinitions: ColumnDefinition[]
): { columnName: string; dataType: AgentDataType }[] => {
  return columnDefinitions.map(x => ({
    columnName: x.columnName,
    dataType:
      x.type === 'agent'
        ? x.config.dataType.type === 'singleLineText' || x.config.dataType.type === 'multiLineText'
          ? { ...x.config.dataType, formatTemplate: x.config.dataType.formatTemplate ?? '' }
          : x.config.dataType.type === 'unconfigured'
            ? { type: 'singleLineText' as const, formatTemplate: '' }
            : x.config.dataType
        : { type: 'singleLineText' as const, formatTemplate: '' },
  }))
}

export const getValueOfCell_ = ({
  columnDef,
  row,
  columnMap,
}: {
  columnDef: ColumnDefinition
  row: RowData
  columnMap: ColumnMap
}) => {
  let cellData = getValueOfCell({ columnDef, row, columnMap })

  if (columnDef.type === 'agent' && isNotNullOrUndefined(cellData)) {
    const cData = cellData as CellValueOf<'agent'>
    cellData = cData?.status === 'success' || cData?.status === 'user' ? cData.value : null
  }

  return cellData
}

export const kvEntriesToJson = (
  columnMap: ColumnMap,
  rowData: RowData,
  kvEntries: { fieldName: string; fieldValue: ColumnId }[]
): Record<string, unknown> => {
  return Object.fromEntries(
    kvEntries
      .map(kv => {
        const colDef = columnMap[kv.fieldValue]

        if (!colDef) {
          return null
        }

        const cellData = getValueOfCell_({ columnDef: colDef, row: rowData, columnMap })
        return [kv.fieldName, cellData]
      })
      .filter(isNotNullOrUndefined)
  )
}

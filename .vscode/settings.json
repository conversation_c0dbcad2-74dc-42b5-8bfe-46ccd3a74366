{"npm.packageManager": "pnpm", "typescript.tsdk": "node_modules/typescript/lib", "i18n-ally.localesPaths": ["apps/web/app/vendor/i18n/locales", "apps/web/app/vendor/i18n"], "i18n-ally.enabledFrameworks": ["i18next", "react-i18next", "react"], "i18n-ally.keystyle": "nested", "[handlebars]": {"editor.wordWrap": "on"}, "editor.formatOnSave": true, "typescript.preferences.preferTypeOnlyAutoImports": true, "typescript.tsserver.maxTsServerMemory": 4000, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}}
# Build layer
FROM public.ecr.aws/docker/library/node:23-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

FROM base AS builder

COPY . /app
WORKDIR /app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    --mount=type=secret,id=NODE_AUTH_TOKEN \
    export NODE_AUTH_TOKEN=$(cat /run/secrets/NODE_AUTH_TOKEN) && \
    pnpm config set '//npm.pkg.github.com/:_authToken' "${NODE_AUTH_TOKEN}" && \
    pnpm install --frozen-lockfile

ARG SERVICE_NAME
RUN pnpm turbo build --filter=@app/${SERVICE_NAME}... --force && pnpm turbo prune "@app/${SERVICE_NAME}"

RUN --mount=type=cache,id=pnpm,target=/pnpm/store rm -rf node_modules && cd out && pnpm install --prod --frozen-lockfile

# Runtime layer
FROM base AS runner
RUN apt-get update && apt-get install -y --no-install-recommends curl dumb-init poppler-utils && rm -rf /var/lib/apt/lists/*
WORKDIR /app

ENV NODE_ENV=production
USER node

COPY --from=builder /app/out ./

ARG SERVICE_NAME
WORKDIR /app/apps/${SERVICE_NAME}/build
CMD ["dumb-init", "node", "index.js"]

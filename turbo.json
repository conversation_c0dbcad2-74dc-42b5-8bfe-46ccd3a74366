{"$schema": "https://turbo.build/schema.json", "ui": "stream", "globalDependencies": [".env.local", "packages/tsconfig/**"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_HIDE_NONSTANDARD", "NEXT_PUBLIC_VERCEL_ENV", "NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA", "NEXT_PUBLIC_API_SERVER", "NEXT_PUBLIC_SALESFORCE_PACKAGE_ID", "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY", "NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL"], "tasks": {"//#format-and-lint": {}, "//#format-and-lint:fix": {"cache": false}, "build": {"dependsOn": ["^build"], "outputs": ["build/**", ".next/**", "!.next/cache/**"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "storybook": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "build-storybook": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "persistent": true}}}
-- Create user for Superday database with all permissions
-- This script creates a new user and grants comprehensive permissions

-- Create the user (replace 'superday_user' and 'your_secure_password' with desired values)
CREATE USER superday_user WITH PASSWORD 'your_secure_password';

-- Grant connection privileges to the database
GRANT CONNECT ON DATABASE superday TO superday_user;

-- Grant usage on the public schema
GRANT USAGE ON SCHEMA public TO superday_user;

-- Grant all privileges on all existing tables in public schema
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superday_user;

-- Grant all privileges on all existing sequences in public schema
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO superday_user;

-- Grant all privileges on all existing functions in public schema
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO superday_user;

-- Grant privileges on future tables, sequences, and functions
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO superday_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO superday_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO superday_user;

-- Grant CREATE privilege on the public schema (allows creating new tables, etc.)
GRANT CREATE ON SCHEMA public TO superday_user;

-- Optional: Make the user a superuser (uncomment if needed - provides maximum privileges)
-- ALTER USER superday_user WITH SUPERUSER;

-- Optional: Grant replication privileges (uncomment if needed for replication)
-- ALTER USER superday_user WITH REPLICATION;

-- Display confirmation
SELECT 'User superday_user created successfully with all privileges on superday database' AS status;

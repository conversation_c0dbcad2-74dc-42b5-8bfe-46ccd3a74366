# Cursor Rules for Upbound Monorepo

## Project Overview
This is a TypeScript monorepo using pnpm workspaces, Turbo for build orchestration, and Biome for linting/formatting. The project consists of:
- Frontend: Next.js app with React 19, TypeScript, and Mantine UI
- Backend: tRPC API with TypeScript ( built on express.js )
- Worker: Background processing service with Effect framework
- Shared packages: UI components, utilities, integrations, etc.

## Code Style & Formatting

### TypeScript
- Use TypeScript 5.8+ with strict mode enabled
- Prefer explicit types over inference when it improves readability
- Use `interface` for object shapes, `type` for unions and utility types
- Always use `const` assertions (`as const`) for literal types

### Naming Conventions
- Use PascalCase for components, interfaces, types, and classes
- Use camelCase for variables, functions, and properties
- Use UPPER_SNAKE_CASE for constants
- Use kebab-case for file names and CSS classes
- Prefix private methods with underscore: `_privateMethod()`
- Use descriptive names that explain intent, not implementation

### File Organization
- One component/class per file
- Group related files in directories
- Use index files for clean exports
- Keep files under 500 lines when possible
- Separate concerns: UI components, business logic, utilities

## React & Next.js

### Component Guidelines
- Use functional components with hooks
- Prefer composition over inheritance
- Use React.memo() for expensive components
- Implement proper prop types with TypeScript
- Use custom hooks for reusable logic
- Keep components focused and single-purpose

### State Management
- Use Zustand for client state (already in use)
- Prefer local state when possible
- Use context sparingly and only for truly global state

### Performance
- Use React.lazy() for code splitting
- Implement proper dependency arrays in useEffect
- Use useMemo and useCallback appropriately
- Avoid creating objects/functions in render

## Security

### Authentication & Authorization
- Use Clerk for authentication (already implemented)
- Implement proper role-based access control
- Validate user permissions on all endpoints
- Use JWT tokens securely
- Implement proper session management

### Data Validation
- Validate all user input with Zod
- Sanitize data before database operations
- Use parameterized queries
- Implement rate limiting
- Validate file uploads

### Environment & Secrets
- Use environment variables for configuration
- Never commit secrets to version control
- Use proper secret management in production
- Implement proper CORS policies

## Performance & Monitoring

### Frontend Performance
- Implement proper code splitting
- Use Next.js Image component for images
- Optimize bundle size
- Implement proper caching strategies
- Use React DevTools for performance analysis

### Backend Performance
- Implement proper database indexing
- Use connection pooling
- Implement caching where appropriate
- Monitor API response times
- Use proper logging levels

### Monitoring
- Use Effect's built-in observability
- Implement proper error tracking
- Monitor application metrics
- Set up alerts for critical issues
- Use structured logging

## Dependencies & Package Management

### Package Management
- Use pnpm for package management
- Keep dependencies up to date
- Audit dependencies regularly
- Use workspace dependencies for internal packages
- Minimize external dependencies

### Version Management
- Use semantic versioning
- Lock dependency versions in pnpm-lock.yaml
- Use caret ranges for minor updates
- Pin major versions for stability

## Git & Version Control

### Commit Messages
- Use conventional commit format
- Write descriptive commit messages
- Reference issues in commit messages
- Keep commits focused and atomic

### Branch Strategy
- Use feature branches for development
- Require pull request reviews
- Use semantic versioning for releases
- Maintain a clean git history

## Development Workflow

### Required Commands
**ALWAYS run these commands from the root of the project after making changes:**

1. **Build Check**: Run `pnpm build` to ensure the project builds successfully
2. **Lint & Format**: Run `pnpm format-and-lint:fix` to fix any linting errors and format code

### Local Development
- Use `pnpm dev` for local development
- Use `pnpm build` to build all packages
- Use `pnpm test` to run tests
- Use `pnpm format-and-lint:fix` to format code

### Code Review
- **MANDATORY**: Verify that `pnpm build` passes before submitting code
- **MANDATORY**: Verify that `pnpm format-and-lint:fix` passes before submitting code
- Review for security vulnerabilities
- Check for performance issues
- Ensure proper error handling
- Verify test coverage
- Check for accessibility issues

## Specific Technologies

### Mantine UI
- Use Mantine components consistently
- Follow Mantine's design patterns
- Use Mantine's theming system
- Implement proper responsive design
- Use existing examples of component use from the codebase when using them again. Pay special attention to any custom "styles" passed into the component.

## Anti-Patterns to Avoid

- Don't use `any` type in TypeScript
- Don't ignore TypeScript errors
- Don't use `console.log` in production
- Don't store sensitive data in localStorage
- Don't make API calls without error handling
- Don't use `dangerouslySetInnerHTML` without sanitization
- Don't commit large files to git
- Don't use deprecated APIs or patterns
- Don't skip code reviews

## Performance Guidelines

- Use React.memo() for expensive components
- Implement proper memoization with useMemo/useCallback
- Use React.lazy() for code splitting
- Optimize images and assets
- Implement proper caching strategies
- Monitor bundle size
- Use production builds for performance testing

## Security Checklist

- [ ] Validate all user input
- [ ] Use parameterized queries
- [ ] Implement proper authentication
- [ ] Sanitize user-generated content
- [ ] Implement rate limiting
- [ ] Use secure session management
- [ ] Audit dependencies regularly
- [ ] Follow OWASP guidelines
- [ ] Implement proper CORS policies
